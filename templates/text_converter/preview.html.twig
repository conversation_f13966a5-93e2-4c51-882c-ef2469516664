{% extends 'base2.html.twig' %}

{% block title %}HTML Output
{% endblock %}

{% block body %}
	<div
		class="mt-4">
		<!-- Header with Path and Action Buttons -->
		<div class="d-flex justify-content-between align-items-center mb-3 p-4 bg-light rounded">
			<div class="path-display">
				<strong class="text-primary" style="font-size: 1.1rem; margin-right: 0.5rem;">Path:</strong>
				<span class="font-monospace" style="font-weight: 500;">{{ path }}</span>
			</div>
			<div class="action-buttons">
				<a href="javascript:history.back()" class="btn btn-outline-secondary me-2 py-3 px-4">
					<i class="fas fa-arrow-left me-2"></i>
					Back
				</a>
				<button id="copyPathBtn" class="btn btn-outline-primary me-2 py-3 px-4" data-path="{{ path }}">
					<i class="fas fa-copy me-2"></i>
					Copy Path
				</button>
			</div>
		</div>

		{% if htmlContent is empty %}
			<div class="alert alert-warning text-center">
				No HTML content was received. Please check your form submission.
			</div>
		{% endif %}

		<!-- Centered Tabs Navigation -->
		<ul class="nav nav-tabs justify-content-center mt-4" id="htmlTabs" role="tablist">
			<li class="nav-item" role="presentation">
				<button class="nav-link active" id="rendered-tab" data-bs-toggle="tab" data-bs-target="#rendered" type="button" role="tab">Rendered Output</button>
			</li>
			<li class="nav-item" role="presentation">
				<button class="nav-link" id="code-tab" data-bs-toggle="tab" data-bs-target="#code" type="button" role="tab">HTML Code</button>
			</li>
		</ul>

		<!-- Tabs Content -->
		<div
			class="tab-content mt-3" id="htmlTabsContent">
			<!-- Rendered Output -->
			<div class="tab-pane fade show active" id="rendered" role="tabpanel">
				<div class="card w-100">
					<div class="card-header">
						<h3>Rendered Output</h3>
					</div>
					<div class="card-body">
						{{ htmlContent|raw }}
					</div>
				</div>
			</div>

			<!-- HTML Code -->
			<div class="tab-pane fade" id="code" role="tabpanel">
				<div class="card w-100">
					<div class="card-header">
						<h3>HTML Code</h3>
					</div>
					<div class="card-body bg-dark text-white p-3">
						<pre><code>{{ htmlContent|e }}</code></pre>
					</div>
				</div>
			</div>
		</div>
	</div>

	<style>
		.path-display {
			padding: 0.75rem 1rem;
			background-color: #f8f9fa;
			border-radius: 6px;
			border-left: 4px solid #0d6efd;
			flex-grow: 1;
			margin-right: 2rem;
		}

		.action-buttons {
			display: flex;
			gap: 1rem;
		}

		@media(max-width: 768px) {
			.d-flex {
				flex-direction: column;
				gap: 1rem;
			}

			.path-display {
				margin-right: 0;
				width: 100%;
			}

			.action-buttons {
				width: 100%;
				justify-content: flex-end;
			}
		}
	</style>
{% endblock %}

{% block script %}
	{{ parent() }}
	 <script>
document.addEventListener('DOMContentLoaded', function() {
    const copyButton = document.getElementById('copyPathBtn');
    
    // Debug information - add this to see if the button is found
    console.log('Copy button found:', copyButton !== null);
    
    if (copyButton) {
        // Log the path value
        console.log('Path value:', copyButton.getAttribute('data-path'));
        
        // Add a simple text display for debugging
        const debugDiv = document.createElement('div');
        debugDiv.style.padding = '10px';
        debugDiv.style.marginTop = '10px';
        debugDiv.style.border = '1px solid #ccc';
        debugDiv.style.display = 'none';
        debugDiv.id = 'copyDebugInfo';
        document.querySelector('.action-buttons').appendChild(debugDiv);
        
        copyButton.addEventListener('click', function() {
            debugDiv.style.display = 'block';
            debugDiv.innerHTML = '<strong>Debug:</strong> Copy started...';
            
            const path = this.getAttribute('data-path');
            const fullPath = window.location.origin + path;
            
            debugDiv.innerHTML += '<br>Full path: ' + fullPath;
            
            // Add a manual copy option that's always visible
            const manualCopySection = document.createElement('div');
            manualCopySection.style.marginTop = '10px';
            manualCopySection.innerHTML = `
                <p>If automatic copy isn't working, copy this text manually:</p>
                <input type="text" value="${fullPath}" style="width:100%; padding:5px;" onclick="this.select();" readonly>
            `;
            
            debugDiv.appendChild(manualCopySection);
            
            // Try the Clipboard API
            if (navigator.clipboard && window.isSecureContext) {
                debugDiv.innerHTML += '<br>Using Clipboard API...';
                
                navigator.clipboard.writeText(fullPath)
                    .then(() => {
                        debugDiv.innerHTML += '<br>Clipboard API success!';
                        showCopyResult(true);
                    })
                    .catch((error) => {
                        debugDiv.innerHTML += '<br>Clipboard API failed: ' + error.message;
                        // Don't try fallback, just show the manual option
                        showCopyResult(false);
                    });
            } else {
                debugDiv.innerHTML += '<br>Clipboard API not available. Using manual option only.';
                showCopyResult(false);
            }
            
            function showCopyResult(success) {
                const originalHtml = copyButton.innerHTML;
                
                if (success) {
                    copyButton.innerHTML = '<i class="fas fa-check me-2"></i> Copied!';
                    copyButton.classList.remove('btn-outline-primary');
                    copyButton.classList.add('btn-outline-success');
                } else {
                    copyButton.innerHTML = '<i class="fas fa-info me-2"></i> See Manual Copy';
                    copyButton.classList.remove('btn-outline-primary');
                    copyButton.classList.add('btn-outline-warning');
                }
                
                // Revert after 3 seconds
                setTimeout(() => {
                    copyButton.innerHTML = originalHtml;
                    copyButton.classList.remove('btn-outline-success', 'btn-outline-warning');
                    copyButton.classList.add('btn-outline-primary');
                }, 3000);
            }
        });
    }
});
	    </script>
{% endblock %}
