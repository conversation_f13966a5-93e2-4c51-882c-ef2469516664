{# External APIs Swagger Interface #}
{% if externalApis %}
<div class="card mt-4" style="border: none; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); border-radius: 12px;">
    <div class="card-header" style="background: linear-gradient(135deg, #040c58 0%, #2637d1 100%); color: white; border-radius: 12px 12px 0 0; border: none;">
        <h5 class="mb-0"><i class="fas fa-code mr-2"></i>API Swagger Interface</h5>
        <small style="color: rgba(255,255,255,0.8);">Select API groups and endpoints to test</small>
    </div>
    <div class="card-body" style="padding: 25px;">
        <!-- API Groups Bootstrap Accordion -->
        <div class="accordion" id="apiGroupsAccordion">
            {% for groupKey, groupConfig in externalApis.api_groups %}
            <div class="card mb-3 api-group-card">
                <div class="card-header p-0" id="heading{{ groupKey|capitalize }}">
                    <button class="btn accordion-toggle w-100 text-left d-flex justify-content-between align-items-center p-3"
                            type="button"
                            data-toggle="collapse"
                            data-target="#collapse{{ groupKey|capitalize }}"
                            aria-expanded="false"
                            aria-controls="collapse{{ groupKey|capitalize }}">
                        <div class="api-group-info">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-server mr-3 text-primary p-3"></i>
                                <div>
                                    <h6 class="mb-1 font-weight-bold text-dark">{{ groupConfig.name }}</h6>
                                    <small class="text-muted">{{ groupConfig.description }}</small>
                                </div>
                            </div>
                            <div class="mt-2">
                                <span class="badge badge-info mr-2">{{ groupConfig.base_url }}</span>
                                <span class="badge badge-success">{{ groupConfig.endpoints|length }} endpoints</span>
                            </div>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon ml-3 text-muted"></i>
                    </button>
                </div>


                <div id="collapse{{ groupKey|capitalize }}"
                     class="collapse"
                     aria-labelledby="heading{{ groupKey|capitalize }}"
                     data-parent="#apiGroupsAccordion">
                    <div class="card-body p-3">
                        <div class="row">
                            {% for endpointKey, endpointConfig in groupConfig.endpoints %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card api-endpoint-card h-100"
                                     data-group="{{ groupKey }}"
                                     data-endpoint="{{ endpointKey }}"
                                     onclick="selectEndpoint('{{ groupKey }}', '{{ endpointKey }}')">
                                    <div class="card-body p-3">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="card-title mb-0 font-weight-bold">{{ endpointConfig.name }}</h6>
                                            <span class="badge badge-method method-{{ endpointConfig.method|lower }}">
                                                {{ endpointConfig.method }}
                                            </span>
                                        </div>
                                        <p class="card-text text-muted small mb-2">{{ endpointConfig.description }}</p>
                                        <div class="endpoint-path-container">
                                            <code class="endpoint-path">{{ endpointConfig.path }}</code>
                                        </div>
                                        <div class="mt-2">
                                            <small class="text-muted">
                                                <i class="fas fa-cogs mr-1"></i>
                                                {{ endpointConfig.parameters|length }} parameters
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Selected API Details and Parameters -->
       <div class="d-flex justify-content-between mt-4" id="api-details-section" style="display: none;">
            <div class="p-2 w-50">
                <!-- Parameters Input Card -->
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0"><i class="fas fa-cogs mr-2"></i>Parameters</h6>
                    </div>
                    <div class="card-body">
                        <form id="api-params-form">
                            <div id="api-params-content">
                                <!-- Populated by JS -->
                            </div>
                            <div class="mt-3">
                                <button type="button" class="btn btn-success btn-block" id="execute-api-btn" disabled>
                                    <i class="fas fa-play mr-2"></i>Execute API
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="p-2 w-50">
                <!-- API Details Card -->
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="fas fa-info-circle mr-2"></i>API Details</h6>
                    </div>
                    <div class="card-body">
                        <div class='d-flex flex-column gap-3' id="api-details-content">
                            <!-- Populated by JS -->
                        </div>
                    </div>
                </div>
            </div>
            
        </div>

    </div>
</div>
{% endif %}
