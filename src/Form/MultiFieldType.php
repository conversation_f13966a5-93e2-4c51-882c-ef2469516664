<?php

namespace App\Form;

use App\Helpers\FormTypeResolver;
use App\Helpers\RegexHelper;
use App\Service\WidgetManager;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\NotBlank;

class MultiFieldType extends AbstractType
{
    const MEDIA_TYPE = [
        'filetype',
        'imagetype',
        'mediatype'
    ];

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $schema = $options['label_attr'] ?? [];
        $languages = $options['row_attr']['languages'] ?? [];

        $builder->add('type', HiddenType::class, [
            'label' => false,
            'data' => 'isMultifield'
        ]);

        foreach ($schema as $key => $value) {
            $fieldOptions = [];
            $translatable = (isset($value['translatable']) && $value['translatable'] == true);
            if (is_array($value) && $value['type'] == 'ChoiceType') {
                $type = FormTypeResolver::getClass($value['type']);
                $fieldOptions = $value['options'];
            }

            if (is_array($value)) {
                $typeValue = $value['type'] ?? '';
                $regexValue = $value['regex'] ?? null;
                $fieldOptions['constraints'] = $this->getConstraints($regexValue, $options['attr']['typeForm'] ?? null);
            } else {
                $typeValue = $value;
            }

            $typeField = strtolower(preg_replace('/\s+/', '', $typeValue));
            $type = $this->getType($typeField);
            $fieldOptions['attr']['type'] = $typeField;
            $fieldOptions['label'] = $value['label'] ?? '';

            if ($value['required'] ?? false) {
                $fieldOptions['required'] = true;
                if (!isset($fieldOptions['constraints'])) {
                    $fieldOptions['constraints'] = [];
                }
                $fieldOptions['constraints'][] = new NotBlank([
                    'message' => "Field '$key' cannot be empty"
                ]);
            }

            if (FileType::class == $type && !$translatable) {
                $this->buildFileTypeForm($builder, $key, $type, $fieldOptions);
            } else {
                if ($translatable) {
                    $languageCodes = [];
                    foreach ($languages as $language) {
                        $languageCodes[] = $language->getCode();
                    }
                    $builder->add($key, TranslatedFieldFormType::class, [
                        'label_attr' => [
                            'label_type' => $type
                        ],
                        'attr' => [
                            'class' => 'translated-field',
                            'languages' => $languageCodes,
                            'required' => $value['required'] ?? false,
                            'parent' => $key
                        ]
                    ]);
                } else {
                    $builder->add($key, $type, array_merge($fieldOptions, [
                        'label' => $value['label'] ?? $key,
                        'attr' => [
                            'class' => 'non-translatable-field'
                        ],
                        'disabled' => $translatable
                    ]));
                }
            }
        }
    }

    private function getType(?string $typeField)
    {
        if (in_array($typeField, self::MEDIA_TYPE)) {
            $type = FileType::class;
        } else {
            $type = FormTypeResolver::getClass($typeField);
        }
        return $type;
    }

    private function getConstraints($regexValue, ?string $typeForm): array
    {
        $constraints = [];
        if (
            isset($regexValue) &&
            isset($typeForm) &&
            $typeForm == WidgetManager::WIDGET
        ) {
            $constraints = RegexHelper::getListConstraints($regexValue);
        }

        return $constraints;
    }

    private function buildFileTypeForm(FormBuilderInterface &$builder, string $key, $type, ?array $fieldOptions = [])
    {
        $constraints = $fieldOptions['constraints'] ?? [];
        $typefield = $fieldOptions['attr']['type'];
        switch ($typefield) {
            case 'mediatype':
                $placeholder = 'choose_media';
                break;
            case 'imagetype':
                $placeholder = 'choose_image';
                break;
            default:
                $placeholder = 'choose_file';
        }
        $label = !empty($fieldOptions['label']) ? $fieldOptions['label'] : $key;
        $builder
            ->add($key . '_input', $type, [
                'label' => $label,
                'data_class' => null,
                'row_attr' => [
                    'type' => 'fileInput'
                ],
                'attr' => [
                    'placeholder' => $placeholder
                ],
                'mapped' => false
            ])
            ->add($key, TextType::class, [
                'label' => false,
                'attr' => [
                    'class' => 'd-none image-type-error'
                ],
                'constraints' => $constraints
            ]);
    }
}
