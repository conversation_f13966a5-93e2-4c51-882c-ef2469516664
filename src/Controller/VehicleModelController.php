<?php

namespace App\Controller;

use App\Document\VehicleModel;
use App\Security\User;
use App\Form\VehicleModelType;
use App\Repository\MediaRepository;
use App\Service\VehicleModelManager;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;

#[Route('/vehicleModel', name: 'vehicle_model_')]
class VehicleModelController extends AbstractController
{
    const BRANDS = ['AC','AP','DS','OP','VX','XX','SP','FT','FO','AH','AR','CY','DG','JE','LA','RM','MA'];

    #[Route('/', name: 'list')]
    public function list(
        #[CurrentUser] User $user,
        Request $request,
        VehicleModelManager $vehicleModelManager,
    ): Response {
        $profile = $user->getProfile();
        $site = $profile->getSite();
        $brands = $profile->isSuperAdmin() ? self::BRANDS : [$site->getBrand()->getCode() ?? 'XX'];
        $vechicleModels = $vehicleModelManager->getVehicleModel($brands);
        return $this->render('vehicle_model/list.html.twig', [
            'models'=>$vechicleModels
        ]);
    }

    #[Route(path: '/create', name: 'create')]
    public function create(
        #[CurrentUser] User $user,
        Request $request,
        VehicleModelManager $vehicleModelManager,
        MediaRepository $mediaRepository
    ): Response {
        $profile = $user->getProfile();
        $site = $profile->getSite();
        $vehicleModel = new VehicleModel();
        if (!$profile->isSuperAdmin()) $vehicleModel->setBrand($site->getBrand()->getCode() ?? 'XX');
        $form = $this->createForm(VehicleModelType::class, $vehicleModel, [
            'is_super_admin' => $profile->isSuperAdmin(),
        ]);
        $form->handleRequest($request);
        $media = $mediaRepository->findBy(['site' => $site]);

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $lcdvValues = $data->getLcdv();
            $duplicateKeys = $vehicleModelManager->findDuplicateKeys($lcdvValues);
            if ($duplicateKeys) {
                $duplicateKeysString = implode(', ', $duplicateKeys);
                $this->addFlash('danger', "The following LCDV values already exist: $duplicateKeysString.");
                return $this->render('vehicle_model/create.html.twig', [
                    'form' => $form->createView(),
                    'medias' => $media,
                ]);
            }
            $vehicleModelManager->save($data);
            $this->addFlash('success', 'Vehicle model created successfully!');
            return $this->redirectToRoute('vehicle_model_list');
        }
        return $this->render('vehicle_model/create.html.twig', [
            'form' => $form->createView(),
            'medias' => $media,
        ]);
    }

    #[Route(path: '/edit/{id}', name: 'edit')]
    public function edit(
        #[CurrentUser] User $user,
        Request $request,
        string $id,
        VehicleModelManager $vehicleModelManager,
        MediaRepository $mediaRepository
    ): Response {
        $profile = $user->getProfile();
        $site = $profile->getSite();
        $vehicleModel = $vehicleModelManager->findModel($id);
        $lcdvCodes = $vehicleModel->getLcdv();
        $form = $this->createForm(VehicleModelType::class, $vehicleModel);
        $form->handleRequest($request);
        $media = $mediaRepository->findBy(['site' => $site]);

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $lcdvValues = $data->getLcdv();
            $newLcdv = array_values(array_diff($lcdvValues, $lcdvCodes));
            $duplicateKeys = $vehicleModelManager->findDuplicateKeys($newLcdv);
            if ($duplicateKeys) {
                $duplicateKeysString = implode(', ', $duplicateKeys);
                $this->addFlash('danger', "The following LCDV values already exist: $duplicateKeysString.");
                return $this->render('vehicle_model/create.html.twig', [
                    'form' => $form->createView(),
                    'medias' => $media,
                ]);
            }
            $vehicleModelManager->update($data, $id);
            $this->addFlash('success', 'Vehicle model created successfully!');
            return $this->redirectToRoute('vehicle_model_list');
        }
        return $this->render('vehicle_model/create.html.twig', [
            'form' => $form->createView(),
            'medias' => $media,
        ]);
    }

    #[Route('/delete/{id}', name: 'delete')]
    public function delete(Request $request, VehicleModelManager $vehicleModelManager, string $id): Response
    {
        if ($this->isCsrfTokenValid('delete-model', $request->request->get('_token'))) {

            if ($vehicleModelManager->removeModel($id)) {
                $this->addFlash('success', 'Vehicle model deleted successfully!');
            } else {
                $this->addFlash('danger', 'Vehicle model deletion failed.');
            }
        }
        return $this->redirectToRoute('vehicle_model_list', [], Response::HTTP_SEE_OTHER);
    }
}
