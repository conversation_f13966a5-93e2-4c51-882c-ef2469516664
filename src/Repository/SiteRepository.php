<?php

namespace App\Repository;

use App\Entity\Language;
use App\Entity\Site;
use App\Entity\Brand;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use <PERSON><PERSON>zart\Assert\Assert;

/**
 * @extends ServiceEntityRepository<Site>
 *
 * @method Site|null find($id, $lockMode = null, $lockVersion = null)
 * @method Site|null findOneBy(array $criteria, array $orderBy = null)
 * @method Site[]    findAll()
 * @method Site[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class SiteRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Site::class);
    }

    public function add(Site $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Site $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function findUniqueCountriesByBrand($brand): array
    {
        $result = $this->createQueryBuilder('s')
            ->select('DISTINCT c, s')
            ->join('s.country', 'c')
            ->where('s.brand = :brand')
            ->setParameter('brand', $brand)
            ->getQuery()
            ->getResult();

        if (is_array($result)) {
            return $result;
        }
        return [];
    }


    public function findSiteByCultureAndBrand(string $languageCode, string $countryCode, string $brand): ?Site
    {
        $qb = $this->createQueryBuilder('s');
        $qb->join('s.languages', 'l')
            ->join('s.country', 'c')
            ->join('s.brand', 'b')
            ->where('TRIM(LOWER(l.code)) = :languageCode')
            ->andWhere('TRIM(LOWER(c.code)) = :countryCode')
            ->andWhere('TRIM(LOWER(b.code)) = :brand')
            ->setParameter('languageCode', trim(strtolower($languageCode)))
            ->setParameter('countryCode', trim(strtolower($countryCode)))
            ->setParameter('brand', trim(strtolower($brand)))
        ;
        $site = $qb->getQuery()->getOneOrNullResult();
        return is_int($site) ? null : $site;
    }

    public function getSites(): ?array
    {
        $qb = $this->createQueryBuilder('s');
        $qb->select(['b.id', 'b.code brand', 'c.code country', 'l.code language', 'b.name'])
            ->join('s.languages', 'l')
            ->join('s.brand', 'b')
            ->join('s.country', 'c')
            ->orderBy('b.name', 'ASC');

        return $qb->getQuery()->getArrayResult();
    }

    public function findByBrandAndCountry($brand, $country = null): array
    {
        $qb = $this->createQueryBuilder('s')
            ->Where('s.brand = :brand')
            ->setParameter('brand', $brand);

        if ($country !== null) {
            $qb->andWhere('s.country = :country')
                ->setParameter('country', $country);
        } else {
            $qb = $this->createQueryBuilder('s')
                ->Where('s.brand = :brand')
                ->orWhere('s.country = :country')
                ->setParameter('brand', $brand)
                ->setParameter('country', $country);
        }
        $result = $qb->getQuery()->getResult();

        if (is_array($result)) {
            return $result;
        } else {
            return [];
        }
    }

    public function getSiteLanguagesByBrandsAndPreferredLanguages(array $brands, array $preferredLanguages): array
    {
        $entityManager = $this->getEntityManager();
        $qb = $entityManager->createQueryBuilder();

        $qb->select('DISTINCT l')
            ->from(Language::class, 'l')
            ->innerJoin(Site::class, 's', 'WITH', 'l MEMBER OF s.languages')
            ->innerJoin(Brand::class, 'b', 'WITH', 's.brand = b.id')
            ->where($qb->expr()->in('s.brand', ':brands'))
            ->andWhere($qb->expr()->neq('b.code', ':globalBrandCode'))
            ->andWhere($qb->expr()->in('s.preferedLanguage', ':preferredLanguages'))
            ->setParameter('brands', $brands)
            ->setParameter('globalBrandCode', 'XX')
            ->setParameter('preferredLanguages', $preferredLanguages);

        // Get the results
        $query = $qb->getQuery();
        $languages = $query->getResult();
        if (is_array($languages)) {
            return $languages;
        } else {
            return [];
        }
    }
}
