<?php

namespace App\Controller;

use App\Document\OwnersManual;
use App\Form\OwnersManualFormType;
use App\Security\User;
use App\Form\VehicleModelType;
use App\Repository\MediaRepository;
use App\Service\OwnersManualManager;
use App\Service\VehicleModelManager;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;

#[Route('/owners-manual', name: 'owners_manual_')]
class OwnersManualController extends AbstractController
{
    const BRANDS = ['AC','AP','DS','OP','VX','XX','SP','FT','FO','AH','AR','CY','DG','JE','LA','RM','MA'];

    #[Route('/', name: 'list')]
    public function list(
        #[CurrentUser] User $user,
        Request $request,
        OwnersManualManager $ownersManualManager,
    ): Response {
        $profile = $user->getProfile();
        $site = $profile->getSite();
        $brands = $profile->isSuperAdmin() ? self::BRANDS : [$site->getBrand()->getCode() ?? 'XX'];
        $vechicleModels = $ownersManualManager->getVehicleModel($brands);
        return $this->render('owners_manual/list.html.twig', [
            'models'=>$vechicleModels
        ]);
    }

    #[Route(path: '/create', name: 'create')]
    public function create(
        #[CurrentUser] User $user,
        Request $request,
        OwnersManualManager $ownersManualManager,
        MediaRepository $mediaRepository
    ): Response {
        $profile = $user->getProfile();
        $site = $profile->getSite();
        $ownersManual = new OwnersManual();
        if (!$profile->isSuperAdmin()) $ownersManual->setBrand($site->getBrand()->getCode() ?? 'XX');
        $form = $this->createForm(OwnersManualFormType::class, $ownersManual, [
            'is_super_admin' => $profile->isSuperAdmin(),
        ]);
        $form->handleRequest($request);
        $media = $mediaRepository->findBy(['site' => $site]);

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $lcdvValues = $data->getLcdv();
            $duplicateKeys = $ownersManualManager->findDuplicateKeys($lcdvValues);
            if ($duplicateKeys) {
                $duplicateKeysString = implode(', ', $duplicateKeys);
                $this->addFlash('danger', "The following LCDV values already exist: $duplicateKeysString.");
                return $this->render('owners_manual/create.html.twig', [
                    'form' => $form->createView(),
                    'medias' => $media,
                ]);
            }
            $ownersManualManager->save($data);
            $this->addFlash('success', 'Owners manual added successfully!');
            return $this->redirectToRoute('owners_manual_list');
        }
        return $this->render('owners_manual/create.html.twig', [
            'form' => $form->createView(),
            'medias' => $media,
        ]);
    }

    #[Route(path: '/edit/{id}', name: 'edit')]
    public function edit(
        #[CurrentUser] User $user,
        Request $request,
        string $id,
        OwnersManualManager $ownersManualManager,
        MediaRepository $mediaRepository
    ): Response {
        $profile = $user->getProfile();
        $site = $profile->getSite();
        $ownersManual = $ownersManualManager->findModel($id);
        $lcdvCodes = $ownersManual->getLcdv();
        $form = $this->createForm(OwnersManualFormType::class, $ownersManual);
        $form->handleRequest($request);
        $media = $mediaRepository->findBy(['site' => $site]);

        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();
            $lcdvValues = $data->getLcdv();
            $newLcdv = array_values(array_diff($lcdvValues, $lcdvCodes));
            $duplicateKeys = $ownersManualManager->findDuplicateKeys($newLcdv);
            if ($duplicateKeys) {
                $duplicateKeysString = implode(', ', $duplicateKeys);
                $this->addFlash('danger', "The following LCDV values already exist: $duplicateKeysString.");
                return $this->render('owners_manual/create.html.twig', [
                    'form' => $form->createView(),
                    'medias' => $media,
                ]);
            }
            $ownersManualManager->update($data, $id);
            $this->addFlash('success', 'Created successfully!');
            return $this->redirectToRoute('owners_manual_list');
        }
        return $this->render('owners_manual/create.html.twig', [
            'form' => $form->createView(),
            'medias' => $media,
        ]);
    }

    #[Route('/delete/{id}', name: 'delete')]
    public function delete(Request $request, OwnersManualManager $ownersManualManager, string $id): Response
    {
        if ($this->isCsrfTokenValid('delete-model', $request->request->get('_token'))) {

            if ($ownersManualManager->removeModel($id)) {
                $this->addFlash('success', 'Deleted successfully!');
            } else {
                $this->addFlash('danger', 'Deletion failed.');
            }
        }
        return $this->redirectToRoute('owners_manual_list', [], Response::HTTP_SEE_OTHER);
    }
}
