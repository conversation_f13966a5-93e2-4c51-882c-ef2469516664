<?php

namespace App\Controller;

use App\Entity\EvRouting;
use App\Form\EvRoutingType;
use App\Helpers\BrandHelper;
use App\Repository\EvRoutingRepository;
use App\Security\User;
use App\Service\EvRoutingFormManager;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\{Request, Response, RedirectResponse};
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;

#[Route('admin/profile/ev_routing', name: 'ev_routing_')]
class EvRoutingController extends AbstractController
{

    /**
     * Display a list of EV Routing configurations for a given profile and brand.
     *
     * @param EvRoutingRepository $repository Repository to fetch EV routing data.
     * @return Response Rendered HTML page listing all routing configs.
     */
    #[Route('', name: 'index', methods: ['GET'])]
    public function index(#[CurrentUser] User $user, EvRoutingRepository $repository ): Response
    {
        $profile = $user->getProfile();
        $site = $profile->getSite();
        $brand = $profile->getBrand() ?? $site->getBrand();

        $evRoutings = $repository->findAll();
        return $this->render('admin/ev_routing/index.html.twig', [
            'profile' => $profile,
            'ev_routings' => $evRoutings,
            'brand' => $brand->getCode()
        ]);
    }

    /**
     * Display and handle the add EV Routing form.
     *
     * @param Request $request HTTP request object containing form data.
     * @param EvRoutingFormManager $manager Handles form processing and persistence.
     * @return Response Rendered form or redirect after successful save.
     */
    #[Route('/add', name: 'add', methods: ['GET', 'POST'])]
    public function add(
        Request $request,
        EvRoutingFormManager $manager,
        #[CurrentUser] User $user
    ): Response {
        $profile = $user->getProfile();
        $site = $profile->getSite();
        $brand = $profile->getBrand() ?? $site->getBrand();
        $form = $this->createForm(EvRoutingType::class, [
            'brand' => $brand->getCode()
        ]);
        $form->handleRequest($request);

        if ($form->isSubmitted()) {
            $form = $manager->manageAddForm($form, $brand->getCode(), $profile);
            if ($form->getErrors(true)->count() === 0) {
                $this->addFlash('success', 'ev_routing_configuration_added');

                return $this->redirectToRoute('ev_routing_index', [
                    'profile' => $profile->getId(),
                    'brand' => $brand->getCode()
                ]);
            }
        }

        return $this->render('admin/ev_routing/new.html.twig', [
            'form' => $form->createView(),
            'profile' => $profile,
            'brand' => $brand->getCode()
        ]);
    }

    /**
     * Display and handle the edit EV Routing form.
     * @param EvRouting $evRouting The existing EV routing entity to update.
     * @param EvRoutingFormManager $manager Service to process the update.
     * @param Request $request HTTP request data.
     * @return Response Form or redirect on success.
     */

    #[Route('/edit/{evRouting}', name: 'edit', methods: ['GET', 'POST'])]
    public function edit(
        #[CurrentUser] User $user,
        EvRouting $evRouting,
        EvRoutingFormManager $manager,
        Request $request
    ): Response {
        $profile = $user->getProfile();
        $site = $profile->getSite();
        $brand = $profile->getBrand() ?? $site->getBrand();
        $formData = $manager->getFormData($evRouting);

        $form = $this->createForm(EvRoutingType::class, $formData);
        $form->handleRequest($request);

        if ($form->isSubmitted()) {
            $form = $manager->manageUpdateForm($form, $evRouting, $profile, $brand->getCode());
            if ($form->getErrors(true)->count() === 0) {
                $this->addFlash('success', 'ev_routing_configuration_updated');

                return $this->redirectToRoute('ev_routing_index', [
                    'profile' => $profile->getId(),
                    'brand' => $brand->getCode()
                ]);
            }
        }

        return $this->render('admin/ev_routing/edit.html.twig', [
            'form' => $form->createView(),
            'profile' => $profile,
            'brand' => $brand->getCode(),
            'id' => $evRouting->getId()
        ]);
    }

    /**
     * Delete an EV Routing entry for a brand.
     *
     * @param EvRouting $evRouting The routing entity to delete.
     * @param EntityManagerInterface $manager Doctrine entity manager.
     * @return RedirectResponse Redirect to index after deletion attempt.
     */

    #[Route('/delete/{evRouting}', name: 'delete', methods: ['POST'])]
    public function delete(
        EvRouting $evRouting,
        #[CurrentUser] User $user,
        EntityManagerInterface $manager,
        Request $request
    ): RedirectResponse {
        $profile = $user->getProfile();
        $site = $profile->getSite();
        $brand = $profile->getBrand() ?? $site->getBrand();

        // Validate CSRF token
        $submittedToken = $request->request->get('token');
        if (!$this->isCsrfTokenValid('delete-ev-routing', $submittedToken)) {
            $this->addFlash('error', 'Invalid CSRF token');
            return $this->redirectToRoute('ev_routing_index', [
                'profile' => $profile->getId(),
                'brand' => $brand->getCode()
            ]);
        }

        if (!in_array($brand->getCode(), BrandHelper::all(), true) || !$evRouting) {
            $this->addFlash('error', 'ev_routing_not_deleted');
            return $this->redirectToRoute('ev_routing_index', [
                'profile' => $profile->getId(),
                'brand' => $brand->getCode()
            ]);
        }

        try {
            $manager->remove($evRouting);
            $manager->flush();
            $this->addFlash('success', 'ev_routing_configuration_deleted');
        } catch (\Throwable) {
            $this->addFlash('error', 'ev_routing_not_deleted');
        }

        return $this->redirectToRoute('ev_routing_index', [
            'profile' => $profile->getId(),
            'brand' => $brand->getCode()
        ]);
    }
}

