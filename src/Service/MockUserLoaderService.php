<?php

namespace App\Service;

use App\Helpers\LoggerTrait;
use App\Repository\ProfileRepository;
use App\Security\User;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;
use Symfony\Component\Security\Core\User\UserInterface;

/**
 * Service used to load and create prepared users from xml file.
 */
class MockUserLoaderService
{
    use LoggerTrait;

    /**
     * MockUsers file injected.
     */
    public function __construct(
        private string $mockUsersFile,
        private ProfileLoader $profileLoader,
        private RequestStack $requestStack,
        private ProfileRepository $profileRepository
    ) {
    }

    /**
     * Method responsible for loading user for the MockUserProvider.
     */
    public function loadUserByUsername(string $username): ?User
    {
        try {
            $usersFromXml = $this->convertFileToArray();
            $userFromXml = $this->searchUser($usersFromXml, $username);
            if (is_array($userFromXml)) {
                return $this->buildUser($userFromXml);
            }
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__.': error when try to load user from XML', [
                'identifier' => $username,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }

        $this->logger->error(__METHOD__.': User not found', [
            'identifier' => $username,
        ]);

        return null;
    }

    /**
     * Symfony calls this method if you use features like switch_user
     * or remember_me.
     *
     * If you're not using these features, you do not need to implement
     * this method.
     *
     * @throws UserNotFoundException if the user is not found
     */
    public function loadUserByIdentifier($identifier): UserInterface
    {
        try {
            $usersFromXml = $this->convertFileToArray();
            $userFromXml = $this->searchUser($usersFromXml, $identifier);
            if (is_array($userFromXml)) {
                return $this->buildUser($userFromXml);
            }
        } catch (\Exception $e) {
            $this->logger->error(__METHOD__.': error when try to load user from XML', [
                'identifier' => $identifier,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }

        $this->logger->error(__METHOD__.': User not found', [
            'identifier' => $identifier,
        ]);
        throw new UserNotFoundException(sprintf('User "%s" not found.', $identifier));
    }

    /**
     * Convert Xml File to array and retrieve user.
     */
    private function convertFileToArray(): array
    {
        try {
            $fileContent = file_get_contents($this->mockUsersFile);
            $usersXml = simplexml_load_string($fileContent);
            $json = json_encode($usersXml);
            $users = json_decode($json, true);

            return $users['user'] ?? [];
        } catch (\Exception $e) {
            $this->logger->error(' error in MockUserLoader::convertFileToArray : ' . $e->getMessage());

            return [];
        }
    }

    /**
     * Look for the corresponding user using the username.
     */
    private function searchUser(array $users, string $username)
    {
        return current(array_filter($users, function ($user) use ($username) {
            return $user['@attributes']['login'] == $username;
        }));
    }

    /**
     * Construct user entiy from the xml data.
     */
    private function buildUser(array $userFromXml): ?User
    {
        if (!$this->checkMinimumValidity($userFromXml)) {
            return null;
        }

        $user = new User();
        $user->setUsername($userFromXml['@attributes']['login'])
            ->setPassword($userFromXml['@attributes']['password']);

        $properties = ['firstname', 'lastname', 'title', 'country', 'locale', 'email'];

        foreach ($properties as $value) {
            if (isset($userFromXml[$value])) {
                $user->__set($value, $userFromXml[$value]);
            }
        }

        $roles = $this->getRoles($userFromXml);
        $profiles = $this->profileLoader->getProfiles($roles);
        $user->__set('groups', $roles);
        $user->setProfiles($profiles);

        $roles[] = 'ROLE_ADMIN';
        $user->setRoles($roles);

        $session = $this->requestStack->getSession();
        $profile = $session->get('selected_profile' . $user->getUserIdentifier());
        // load all profile data
        if ($profile && $profile->getId()) {
            $profile = $this->profileRepository->findWithRelations($profile->getId());
            $user->setProfile($profile);
        }
        return $user;
    }

    /**
     * Check if the login, password and roles are set.
     */
    private function checkMinimumValidity(array $userFromXml): bool
    {
        return isset($userFromXml['@attributes']['login']) && isset($userFromXml['@attributes']['password']) && isset($userFromXml['groups']['group']['name']);
    }

    /**
     * Get roles from the users xml array.
     */
    private function getRoles(array $userFromXml): array
    {
        $roles = $userFromXml['groups']['group']['name'];
        if (!is_array($roles)) {
            $roles = [$roles];
        }

        return $roles;
    }
}
