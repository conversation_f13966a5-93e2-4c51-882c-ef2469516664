<?php

namespace App\Repository;

use App\Entity\Country;
use App\Entity\Brand;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\Query\QueryBuilder;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Country>
 *
 * @method Country|null find($id, $lockMode = null, $lockVersion = null)
 * @method Country|null findOneBy(array $criteria, array $orderBy = null)
 * @method Country[]    findAll()
 * @method Country[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CountryRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Country::class);
    }

    public function save(Country $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Country $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
    
    public function getCountriesByBrand(Brand $brand): array
    {
        return $this->createQueryBuilder('c')
            ->distinct()
            ->join('c.sites', 'site')
            ->where('site.brand =:brand')
            ->setParameter('brand', $brand)
            ->orderBy('c.name')
            ->getQuery()
            ->getArrayResult();
        ;
    }

    public function getAllCountryCodes(array $params): array
    {
        $qb = $this->createQueryBuilder('c')
            ->select('c.code');

        if (!empty($params['country'])) {
            $qb
                ->andWhere('c.code IN (:country)')
                ->setParameter('country', $params['country']);
        }

        $result = $qb->getQuery()->getScalarResult();
        
        // Extract the codes from the result
        $codes = array_column($result, 'code');

        return $codes;
    }

    public function getCountryByIds(string $ids): array
    {
        $qb = $this->createQueryBuilder('c')
            ->select('c')
            ->where('c.id IN (:ids)')
            ->setParameter('ids', explode(',', $ids));

        return $qb->getQuery()->getResult();
    }
}
