<?php

namespace App\Entity;

use App\Repository\SiteRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Serializer\Attribute\Groups;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: SiteRepository::class)]
#[UniqueEntity(fields: ['brand', 'country'], message: 'msg_error_site_unique')]
#[UniqueEntity(fields: ['label'], message: 'msg_error_site_unique')]
#[ORM\Table(name: 'site', uniqueConstraints: [
    new ORM\UniqueConstraint(name: 'unique_brand_country', columns: ['brand_id', 'country_id']),
    new ORM\UniqueConstraint(name: 'unique_label', columns: ['label'])
])]
class Site
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[Assert\NotBlank(message: 'msg_error_label_notblank')]
    #[Assert\Regex(pattern: '/^[a-zA-Z0-9 ]+$/', message: 'msg_error_label_invalid')]
    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    #[Groups(['package_export'])]
    private ?string $label;

    #[Assert\NotBlank(message: 'msg_error_country_notblank')]
    #[ORM\ManyToOne(targetEntity: Country::class, inversedBy: 'sites')]
    #[ORM\JoinColumn(nullable: true)]
    private ?Country $country;

    #[Assert\NotBlank(message: 'msg_error_prefered_timezone_notblank')]
    #[ORM\Column(type: 'string', length: 255)]
    private string $timezone;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $ldapCode;

    #[ORM\Column(type: 'boolean', nullable: true)]
    private ?bool $domtom;

    #[ORM\Column(type: 'boolean', nullable: true)]
    private ?bool $civilitesCustomerAtActive;

    #[ORM\Column(type: 'boolean', nullable: true)]
    private ?bool $reversedNameOrder;

    #[Assert\NotBlank(message: 'msg_error_brand_notblank')]
    #[ORM\ManyToOne(targetEntity: Brand::class, inversedBy: 'sites')]
    #[ORM\JoinColumn(nullable: true)]
    private ?Brand $brand;

    #[Assert\NotBlank(message: 'msg_error_site_languages_notblank')]
    #[Assert\Count(min: 1, minMessage: 'msg_error_site_languages_min')]
    #[ORM\ManyToMany(targetEntity: Language::class, inversedBy: 'sites' )]
    #[ORM\JoinTable(name:'site_language')]
    private Collection $languages;

    #[Assert\NotBlank(message: 'msg_error_prefered_language_notblank')]
    #[ORM\ManyToOne(targetEntity: Language::class)]
    #[ORM\JoinColumn(nullable: false)]
    private Language $preferedLanguage;

    #[Assert\Choice(choices: ['KM', 'MILES'], message: 'msg_error_distance_unit_choices')]
    #[Assert\Length(max: 5, maxMessage: 'msg_error_distance_unit_max')]
    #[ORM\Column(type: 'string', length: 10, nullable: true)]
    private ?string $distanceUnit;

    #[ORM\Column(type: 'string', length: 20, nullable: true)]
    private ?string $consumptionUnit; // consumptionUnit

    #[ORM\Column(type: 'string', length: 20, nullable: true)]
    private ?string $cost;

    #[ORM\Column(type: 'string', length: 10, nullable: true)]
    private ?string $volumeUnit;

    #[ORM\Column(type: 'string', length: 10, nullable: true)]
    private ?string $dateFormat;

    #[ORM\Column(type: 'string', length: 10, nullable: true)]
    private ?string $hourFormat;

    #[ORM\ManyToMany(targetEntity: Currency::class)]
    private Collection $currencies;

    #[ORM\OneToMany(targetEntity: Media::class, mappedBy: 'site')]
    private Collection $medias;

    #[ORM\OneToMany(targetEntity: Profile::class, mappedBy: 'site')]
    private Collection $profiles;

    #[ORM\ManyToOne(targetEntity: Region::class)]
    private ?Region $region;

    public function __construct()
    {
        $this->languages = new ArrayCollection();
        $this->currencies = new ArrayCollection();
        $this->profiles = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function setLabel(?string $label): self
    {
        $this->label = $label;

        return $this;
    }

    public function getCountry(): ?Country
    {
        return $this->country;
    }

    public function setCountry(?Country $country): self
    {
        $this->country = $country;

        return $this;
    }

    public function getTimezone(): ?string
    {
        return $this->timezone;
    }

    public function setTimezone(string $timezone): self
    {
        $this->timezone = $timezone;

        return $this;
    }

    public function getLdapCode(): ?string
    {
        return $this->ldapCode;
    }

    public function setLdapCode(?string $ldapCode): self
    {
        $this->ldapCode = $ldapCode;

        return $this;
    }

    public function isDomtom(): ?bool
    {
        return $this->domtom;
    }

    public function setDomtom(?bool $domtom): self
    {
        $this->domtom = $domtom;

        return $this;
    }

    public function isCivilitesCustomerAtActive(): ?bool
    {
        return $this->civilitesCustomerAtActive;
    }

    public function setCivilitesCustomerAtActive(?bool $civilitesCustomerAtActive): self
    {
        $this->civilitesCustomerAtActive = $civilitesCustomerAtActive;

        return $this;
    }

    public function isReversedNameOrder(): ?bool
    {
        return $this->reversedNameOrder;
    }

    public function setReversedNameOrder(?bool $reversedNameOrder): self
    {
        $this->reversedNameOrder = $reversedNameOrder;

        return $this;
    }

    public function getBrand(): ?Brand
    {
        return $this->brand;
    }

    public function setBrand(?Brand $brand): self
    {
        $this->brand = $brand;

        return $this;
    }

    /**
     * @return Collection<int, Language>
     */
    public function getLanguages(): Collection
    {
        return $this->languages;
    }

    public function addLanguages(Language $languages): self
    {
        if (!$this->languages->contains($languages)) {
            $this->languages[] = $languages;
        }

        return $this;
    }

    public function removeLanguages(Language $language): self
    {
        $this->languages->removeElement($language);

        return $this;
    }

    public function getPreferedLanguage(): Language
    {
        return $this->preferedLanguage;
    }

    public function setPreferedLanguage(Language $preferedLanguage): self
    {
        $this->preferedLanguage = $preferedLanguage;

        return $this;
    }

    public function getDistanceUnit(): ?string
    {
        return $this->distanceUnit;
    }

    public function setDistanceUnit(?string $distance_unit): self
    {
        $this->distanceUnit = $distance_unit;

        return $this;
    }

    public function getConsumptionUnit(): ?string
    {
        return $this->consumptionUnit;
    }

    public function setConsumptionUnit(?string $consumptionUnit): self
    {
        $this->consumptionUnit = $consumptionUnit;

        return $this;
    }

    public function getCost(): ?string
    {
        return $this->cost;
    }

    public function setCost(?string $cost): self
    {
        $this->cost = $cost;

        return $this;
    }

    public function getVolumeUnit(): ?string
    {
        return $this->volumeUnit;
    }

    public function setVolumeUnit(?string $volumeUnit): self
    {
        $this->volumeUnit = $volumeUnit;

        return $this;
    }

    public function getDateFormat(): ?string
    {
        return $this->dateFormat;
    }

    public function setDateFormat(?string $dateFormat): self
    {
        $this->dateFormat = $dateFormat;

        return $this;
    }

    public function getHourFormat(): ?string
    {
        return $this->hourFormat;
    }

    public function setHourFormat(?string $hourFormat): self
    {
        $this->hourFormat = $hourFormat;

        return $this;
    }

    public function addLanguage(Language $language): self
    {
        if (!$this->languages->contains($language)) {
            $this->languages->add($language);
        }

        return $this;
    }

    public function removeLanguage(Language $language): self
    {
        $this->languages->removeElement($language);

        return $this;
    }

    /**
     * @return Collection<int, Currency>
     */
    public function getCurrencies(): Collection
    {
        return $this->currencies;
    }

    public function addCurrency(Currency $currency): self
    {
        if (!$this->currencies->contains($currency)) {
            $this->currencies->add($currency);
        }

        return $this;
    }

    public function removeCurrency(Currency $currency): self
    {
        $this->currencies->removeElement($currency);

        return $this;
    }

    /**
     * @return Collection<int, Profile>
     */
    public function getProfiles(): Collection
    {
        return $this->profiles;
    }

    public function addProfile(Profile $profile): self
    {
        if (!$this->profiles->contains($profile)) {
            $this->profiles[] = $profile;
            $profile->setSite($this);
        }

        return $this;
    }

    public function removeProfile(Profile $profile): self
    {
        if ($this->profiles->removeElement($profile)) {
            // set the owning side to null (unless already changed)
            if ($profile->getSite() === $this) {
                $profile->setSite(null);
            }
        }

        return $this;
    }

    /**
     * Get the value of region
     */ 
    public function getRegion(): ?Region
    {
        return $this->region;
    }

    /**
     * Set the value of region
     *
     * @return  self
     */ 
    public function setRegion(?Region $region): self
    {
        $this->region = $region;

        return $this;
    }

    public function isAdministrationCentral(): bool
    {
        return $this->getLabel() === 'ADMINISTRATION CENTRAL';
    }

    public function getMedias(): Collection
    {
        return $this->medias;
    }
}
