{#
    LCDV Content Template

    This template renders the LCDV tab content for SPS Eligibility management.
    It includes:
    - Header with "Add New" button (for users with write permissions)
    - Search filters for LCDV code, type, and disclaimer
    - Data table with LCDV eligibility rules
    - Action buttons for view/edit/delete operations

    <AUTHOR>
    @since 2024-06-27
#}

{# Header section with title and add button #}
<div class="row mb-3">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0">{{ 'LCDV Eligibility Rules' | trans }}</h4>
            {% if can_write %}
                <a href="{{ path('sps_eligibility_lcdv_new') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>{{ 'Add New LCDV Rule' | trans }}
                </a>
            {% endif %}
        </div>
    </div>
</div>

{# Search filters card #}
<div class="card mb-4">
    <div class="card-header">
        <i class="fas fa-search me-2"></i>{{ 'Search Filters' | trans }}
    </div>
    <div class="card-body">
        <form method="GET" action="{{ path('sps_eligibility_index') }}">
            <input type="hidden" name="tab" value="lcdv">
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="lcdv_code">{{ 'LCDV Code' | trans }}</label>
                        <input type="text" 
                               class="form-control" 
                               id="lcdv_code" 
                               name="lcdv_code" 
                               value="{{ search.lcdv_code }}" 
                               placeholder="{{ 'Enter LCDV code...' | trans }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="type">{{ 'Type' | trans }}</label>
                        <select class="form-control" id="type" name="type">
                            <option value="">{{ 'All Types' | trans }}</option>
                            {% for type in types %}
                                <option value="{{ type }}" {{ search.type == type ? 'selected' : '' }}>
                                    {{ type }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label for="disclaimer">{{ 'Disclaimer' | trans }}</label>
                        <select class="form-control" id="disclaimer" name="disclaimer">
                            <option value="">{{ 'All' | trans }}</option>
                            <option value="1" {{ search.disclaimer == '1' ? 'selected' : '' }}>{{ 'Yes' | trans }}</option>
                            <option value="0" {{ search.disclaimer == '0' ? 'selected' : '' }}>{{ 'No' | trans }}</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <div class="d-flex">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i>{{ 'Search' | trans }}
                            </button>
                            <a href="{{ path('sps_eligibility_index', {'tab': 'lcdv'}) }}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i>{{ 'Clear' | trans }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- LCDV Rules Table -->
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">{{ 'LCDV Eligibility Rules' | trans }}</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-bordered dataTable" id="lcdvDataTable" width="100%">
                <thead>
                    <tr>
                        <th class="text">{{ 'LCDV Codes' | trans }}</th>
                        <th class="text">{{ 'Eligibility Rules' | trans }}</th>
                        <th class="text">{{ 'Type' | trans }}</th>
                        <th class="text">{{ 'Disclaimer' | trans }}</th>
                        <th class="text text-end" width="20%">{{ 'Actions' | trans }}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for rule in eligibility_rules %}
                        <tr>
                            <td class="lcdv-codes">
                                {% set codes = rule.codes %}
                                {% for code in codes %}
                                    {% if loop.index <= 5 %}
                                        <span class="badge bg-secondary me-1 mb-1">{{ code }}</span>
                                    {% endif %}
                                {% endfor %}
                                {% if codes|length > 5 %}
                                    <span class="text-muted">... and {{ codes|length - 5 }} more</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="text-truncate" style="max-width: 300px;" title="{{ rule.eligibilityRule }}">
                                    {{ rule.eligibilityRule }}
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ rule.type }}</span>
                            </td>
                            <td class="text-center">
                                {% set isYes = (rule.eligibilityDisclaimer is same as('YES')) or (rule.eligibilityDisclaimer is same as(true)) or (rule.eligibilityDisclaimer is same as('Yes')) %}
                                {% if isYes %}
                                    <span class="badge bg-warning">{{ 'Yes' | trans }}</span>
                                {% else %}
                                    <span class="badge bg-success">{{ 'No' | trans }}</span>
                                {% endif %}
                            </td>
                            <td class="text-end">
                                <div class="btn-group" role="group">
                                    <a href="{{ path('sps_eligibility_lcdv_show', {'id': rule.id}) }}" 
                                       class="btn btn-sm btn-outline-info" 
                                       title="{{ 'View' | trans }}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if can_write %}
                                        <a href="{{ path('sps_eligibility_lcdv_edit', {'id': rule.id}) }}" 
                                           class="btn btn-sm btn-outline-warning" 
                                           title="{{ 'Edit' | trans }}">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ path('sps_eligibility_lcdv_delete', {'id': rule.id}) }}" 
                                           class="btn btn-sm btn-outline-danger" 
                                           title="{{ 'Delete' | trans }}"
                                           onclick="return confirm('{{ 'Are you sure you want to delete this rule?' | trans }}')">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                    {% else %}
                        <tr>
                            <td colspan="5" class="text-center text-muted">
                                {{ 'No LCDV eligibility rules found.' | trans }}
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
