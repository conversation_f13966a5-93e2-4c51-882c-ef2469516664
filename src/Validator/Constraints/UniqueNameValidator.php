<?php

namespace App\Validator\Constraints;

use S<PERSON>fony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Exception\UnexpectedValueException;
use App\Repository\FeatureSettingRepository; // Import your repository

class UniqueNameValidator extends ConstraintValidator
{
    private $featureSettingRepository;

    public function __construct(FeatureSettingRepository $featureSettingRepository)
    {
        $this->featureSettingRepository = $featureSettingRepository;
    }

    public function validate($value, Constraint $constraint)
    {
        if (!$constraint instanceof UniqueName) {
            throw new UnexpectedTypeException($constraint, UniqueName::class);
        }

        // Check if value is already taken
        $existingFeature = $this->featureSettingRepository->findOneBy(['name' => $value]);
        
        if ($existingFeature) {
            // Add a violation if the name is already taken
            $this->context->buildViolation($constraint->message)
                ->setParameter('{{ value }}', $value)
                ->addViolation();
        }
    }
}
