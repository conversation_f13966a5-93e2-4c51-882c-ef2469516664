/////////////////////////////////////
	// CREATE GIT TAG
	/////////////////////////////////////
   def stageTagGitCreate() {
	   stage('TagGitCreate') {
          sh "bash ./scripts/tag.sh  ${version} ${justification}"
	   }
	}

	/////////////////////////////////////
	// ENVIRONMENT INITIALIZATION
	/////////////////////////////////////
   def stageInit() {
	   stage('EnvInitialization') {
	       script { folderName = new java.text.SimpleDateFormat("yyyyMMddHHmmssMs").format(new Date()); }
		   sh "<NAME_EMAIL>:D4UDigitalPlatform/space-aws-infra.git /tmp/${folderName}"
		   sh "cp -r  /tmp/${folderName}/config/_init ."
		   sh "cp -r /tmp/${folderName}/deployment/scripts ."
		   sh "rm -rf /tmp/${folderName}"
	   }
	}

	/////////////////////////////////////
	// INSTALL BY USER : COMPOSER INSTALL - BUILD APP
	/////////////////////////////////////
   def stageInstall(String currentDate, String projectName, String targetEnvironment, String appName) {
	   stage('Install') {
		   GIT_COMMIT_HASH = sh (script: "git log -n 1 --pretty=format:'(%h) %cd'", returnStdout: true)
		   sh "rm -rf ./.git"
		   sh "bash ./scripts/aws_login_docker.sh"
		   sh "CONTAINER_NAME=${currentDate}  /usr/local/bin/docker-compose --project-name=${currentDate} --file _init/docker/docker-compose_build_app_image.yml up --build -d"

                   sh "docker exec ${currentDate} bash -c 'bash /var/www/tmp/scripts/spacebo_install.sh ${targetEnvironment} ${appName} --gitcommit \"${GIT_COMMIT_HASH}\"'"
	   }
	}

   ////////////////////////////////////
   // PUSH   IN ECR (ECS SERVICE AWS)
   ///////////////////////////////////
   def stagePushImage(String currentDate, String targetEnvironment, String appName) {
	   stage('Push') {
		   sh "bash ./scripts/spacebo_push.sh ${env.BRANCH_NAME} ${targetEnvironment} ${currentDate} ${appName}"
	   }
	}

   ////////////////////////////////////
   // DEPLOY IMAGE
   ///////////////////////////////////
   def stageDeploy(String currentDate, String targetEnvironment, String appName) {
	   stage('Deploy') {
		   sh "bash ./scripts/deploy/space_deploy.sh ${env.BRANCH_NAME} ${targetEnvironment} ${currentDate} ${appName}"
	   }
	}
	

   return this
