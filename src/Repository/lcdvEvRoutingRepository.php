<?php

namespace App\Repository;

use App\Entity\EvRouting;
use App\Entity\LcdvEvRouting;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method LcdvEvRouting|null find($id, $lockMode = null, $lockVersion = null)
 * @method LcdvEvRouting|null findOneBy(array $criteria, array $orderBy = null)
 * @method LcdvEvRouting[]    findAll()
 * @method LcdvEvRouting[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LcdvEvRoutingRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LcdvEvRouting::class);
    }

    public function deleteByBrandAndLabel($brand, $label) {
        try {
            return $this->createQueryBuilder('ev_routing')
                ->delete()
                ->where('ev_routing.label = :label')
                ->andWhere('ev_routing.brand = :brand')
                ->setParameter('label', $label)
                ->setParameter('brand', $brand)
                ->getQuery()->execute();
        }
        catch (\Exception $e) {
            return 0;
        }
    }

    public function checkLabelUsageForUpdateValidation($label, $brand, $ids)
    {
        return $this->createQueryBuilder('l')
            ->where('l.label = :label')
            ->andWhere('l.brand = :brand')
            ->andWhere('l.id not in (:ids)')
            ->setParameter('label', $label)
            ->setParameter('brand', $brand)
            ->setParameter('ids', $ids)
            ->getQuery()
            ->getResult();
    }

    public function getLCDVs($lcdv)
    {
        $lcdv = substr($lcdv, 0, 2);

        return $this->createQueryBuilder('l')
            ->where("l.lcdv LIKE :lcdv")
            ->setParameter('lcdv', $lcdv.'%')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param  string  $lcdv
     *
     * @return \Doctrine\Common\Collections\ArrayCollection
     */
    public function getLcdvEvRoutingsWhereInLcdv(string $lcdv): ArrayCollection
    {
        // Prepare the parameter
        $lcdvs = [];

        foreach (range(2, strlen($lcdv)) as $length) {
            $lcdvs[] = substr($lcdv, 0, $length);
        }
        // Query
        $qb = $this->createQueryBuilder('l');

        $results = $qb
            ->where($qb->expr()->in('l.lcdv', ':lcdvs'))
            ->setParameter('lcdvs', array_filter($lcdvs))
            ->getQuery()
            ->getResult();

        return new ArrayCollection($results);
    }

    /**
     * @param  string  $lcdv
     * @param  string  $dvq
     * @param  string  $b0f
     * @param  string  $dar
     *
     * @return \Doctrine\Common\Collections\ArrayCollection
     */
    public function getEvRoutingConfig(string $lcdv, string $dvq, string $b0f, string $dar): ArrayCollection
    {
        // Prepare the parameter
        $lcdvs = [];

        foreach (range(2, strlen($lcdv)) as $length) {
            $lcdvs[] = substr($lcdv, 0, $length);
        }
        // Query
        $results = $this->createQueryBuilder('l')
            ->join('l.evRouting', 'le')
            ->where('l.lcdv in (:lcdvs)')
            ->andWhere('le.dvq = :dvq') 
            ->andWhere('le.b0f = :b0f')
            ->andWhere('le.dar = :dar')
            ->setParameter('dvq', $dvq)
            ->setParameter('b0f', $b0f)
            ->setParameter('dar', $dar)
            ->setParameter('lcdvs', $lcdvs)
            ->getQuery()
            ->getResult();
        return new ArrayCollection($results);
    }

    public function findOneLcdvLabelByBrand($fields)
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.lcdv =:lcdv')
            ->join('l.evRouting', 'le')
            ->andWhere('le.brand = :brand')
            ->setParameter('lcdv', $fields['lcdv'])
            ->setParameter('brand', (isset($fields['evRouting']) && $fields['evRouting'] instanceof EvRouting) ? $fields['evRouting']->getBrand() : null)
            ->getQuery()
            ->getResult();
    }

    public function findUniqueLcdvEvRouting($fields)
    {
        $evRouting = $fields['evRouting'];

        return $this->createQueryBuilder('l')
            ->andWhere('l.lcdv =:lcdv')
            ->join('l.evRouting', 'le')
            ->andWhere('le.brand = :brand')
            ->andWhere('le.dvq = :dvq')
            ->andWhere('le.b0f = :b0f')
            ->andWhere('le.dar = :dar')
            ->setParameter('lcdv', $fields['lcdv'])
            ->setParameter('brand', $evRouting->getBrand())
            ->setParameter('dvq', $evRouting->getDvq())
            ->setParameter('b0f', $evRouting->getB0f())
            ->setParameter('dar', $evRouting->getDar())
            ->getQuery()
            ->getResult();
    }
}