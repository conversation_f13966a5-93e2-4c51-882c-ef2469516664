import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ConfigReader {
    private static final Logger logger = LoggerFactory.getLogger(ConfigReader.class);
    private Properties properties;

    // Constructor to load multiple properties files
    public ConfigReader(String... propertyFiles) {
        properties = new Properties();
        for (String filePath : propertyFiles) {
            try (InputStream input = new FileInputStream(filePath)) {
                properties.load(input);
                logger.info("Loaded properties file: {}", filePath);
            } catch (IOException e) {
                logger.error("An IOException occurred while loading file: " + filePath, e);
            }
        }
    }

    // Method to get a property value by key
    public String getProperty(String key) {
        String value = properties.getProperty(key);
        if (value == null) {
            logger.warn("Property key '{}' not found in the properties file.", key);
        }
        return value;
    }
}
