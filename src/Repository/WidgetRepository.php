<?php

namespace App\Repository;

use App\Entity\Brand;
use App\Entity\Country;
use App\Entity\Widget;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Persistence\ManagerRegistry;
use Webmozart\Assert\Assert;

/**
 * @extends ServiceEntityRepository<Widget>
 *
 * @method Widget|null find($id, $lockMode = null, $lockVersion = null)
 * @method Widget|null findOneBy(array $criteria, array $orderBy = null)
 * @method Widget[]    findAll()
 * @method Widget[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class WidgetRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Widget::class);
    }

    public function save(Widget $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Widget $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * @return ArrayCollection<Widget>
     */
    public function getWidgetList(): ArrayCollection
    {
        return $this->createQueryBuilder('w')
            ->select('w.id, w.name')
            ->orderBy('w.name', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function autocompleteList(string $term, int $page): array
    {
        $firstResult = ((int)$page - 1) * 10;
        $firstResult = $firstResult < 0 ? 0 : $firstResult;
        $widgets = $this->createQueryBuilder('w')
            ->where('w.name LIKE :term')
            ->setParameter('term', '%' . $term . '%')
            ->orderBy('w.name', 'ASC')
            ->setMaxResults(10)
            ->setFirstResult($firstResult)
            ->getQuery()
            ->getResult();

        Assert::isArray($widgets);
        return $widgets;
    }

    public function findWidgetAndConfig($brand, $country): array
    {
        return $this->createQueryBuilder('widget')
            ->select(['widget.id', 'widget.wguid', 'widget.name', 'widget.type', 'widgetData.enabled', 'widgetData.source', 'widgetData.id as widgetDataId'])
            ->leftJoin('widget.widgetData', 'widgetData', 'WITH', 'widgetData.country = :country and widgetData.brand = :brand')
            ->setParameter('country', $country)
            ->setParameter('brand', $brand)
            ->orderBy('widget.id', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function findWidgetSettings(Brand $brand, string $country, string $source): array
    {
        $sq = $this->createQueryBuilder('widget')
        ->select([
            'widget.id', 
            'widget.wguid', 
            'widget.name', 
            'widget.type', 
            'widgetData.enabled', 
            'widgetData.source', 
            'widgetData.id as widgetDataId'
        ])
        ->leftJoin('widget.widgetData', 'widgetData');

        if ($country) {
            $sq->andWhere('widgetData.country = :country')
                ->setParameter('country', $country);
        }

        if ($brand) {
            $sq->andWhere('widgetData.brand = :brand')
                ->setParameter('brand', $brand);
        }

        return $sq->orderBy('widget.id', 'ASC')
                ->getQuery()
                ->getResult();
    }

    /**
     * @return array
     */
    public function findAllWidgetSettings(string $brand, string $country, array $sources): array
    {

        return $this->createQueryBuilder('widget')
            ->select([
                'widget.wguid as guid',
                'widget.name', 'widgetData.enabled',
                'countries.code as country',
                'brands.code as brand',
                'widgetData.source as source'
            ])
            ->Join('widget.widgetData', 'widgetData')
            ->Join('widgetData.country', 'countries')
            ->Join('widgetData.brand', 'brands')
            ->where('countries.code =:country')
            ->andWhere('brands.code =:brand')
            ->andWhere('widgetData.source IN (:sources)')
            ->setParameter('country', $country)
            ->setParameter('brand', $brand)
            ->setParameter('sources', $sources)
            ->getQuery()->getResult();
    }
}
