<?php

namespace App\Entity;

use App\Repository\LocalTranslationRepository;
use DateTime;
use Doctrine\ORM\Mapping as ORM;
use Doctrine\ORM\Mapping\HasLifecycleCallbacks;
use Doctrine\ORM\Mapping\Table;
use Doctrine\ORM\Mapping\UniqueConstraint;
use Symfony\Component\Serializer\Attribute\Groups;

#[ORM\Entity(repositoryClass: LocalTranslationRepository::class)]
#[HasLifecycleCallbacks]
#[Table(uniqueConstraints: [
    new UniqueConstraint(
        name: "local_translation_unique", 
        columns: (["translation_key_id", "language_id", "site_id"])
    )
])]
class LocalTranslation
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private ?DateTime $updatedAt;

    #[ORM\ManyToOne(targetEntity: TranslationKey::class, inversedBy: 'localTranslations')]
    #[ORM\JoinColumn(nullable: false)]
    private TranslationKey $translationKey;

    #[ORM\Column(type: 'string', length: 2048, nullable: true)]
    #[Groups(['package_export'])]
    private ?string $translation;

    #[ORM\ManyToOne(targetEntity: Site::class)]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['package_export'])]
    private Site $site;

    #[ORM\ManyToOne(targetEntity: Language::class)]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['package_export'])]
    private Language $language;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUpdatedAt(): ?DateTime
    {
        return $this->updatedAt;
    }

    #[ORM\PreUpdate]
    public function setUpdatedAt(): self
    {
        $this->updatedAt = new DateTime();
        return $this;
    }

    public function getTranslationKey(): TranslationKey
    {
        return $this->translationKey;
    }

    public function setTranslationKey(TranslationKey $translationKey): self
    {
        $this->translationKey = $translationKey;

        return $this;
    }

    public function getTranslation(): ?string
    {
        return $this->translation;
    }

    public function setTranslation(?string $translation): self
    {
        $this->translation = $translation;

        return $this;
    }

    public function getSite(): Site
    {
        return $this->site;
    }

    public function setSite(Site $site): self
    {
        $this->site = $site;

        return $this;
    }

    public function getLanguage(): Language
    {
        return $this->language;
    }

    public function setLanguage(Language $language): self
    {
        $this->language = $language;

        return $this;
    }

    #[ORM\PrePersist]
    #[ORM\PreUpdate]
    public function updateTimestamps(): void
    {
        $this->setUpdatedAt();
    }
}
