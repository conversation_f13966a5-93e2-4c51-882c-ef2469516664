<?php

namespace App\Security;

use App\Helpers\LoggerTrait;
use App\Security\User;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Security\Core\Exception\UnsupportedUserException;
use Symfony\Component\Security\Core\Exception\UserNotFoundException;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Security\Core\User\UserProviderInterface;
use Drenso\OidcBundle\Security\UserProvider\OidcUserProviderInterface;
use Drenso\OidcBundle\Exception\OidcException;
use App\Service\ProfileLoader;
use Drenso\OidcBundle\Model\OidcUserData;

/**
 * @implements UserProviderInterface<User>
 */
class OidcUserProvider implements UserProviderInterface, OidcUserProviderInterface
{
    use LoggerTrait;

    public function __construct(private RequestStack $requestStack, private ProfileLoader $profileLoader)
    {
    }

    /**
     * @deprecated since Symfony 5.3, loadUserByIdentifier() is used instead
     */
    /*
    public function loadUserByUsername($username): UserInterface
    {
        return $this->loadUserByIdentifier($username);
    }
    */

    /**
     * Symfony calls this method if you use features like switch_user
     * or remember_me.
     *
     * If you're not using these features, you do not need to implement
     * this method.
     *
     * @throws UserNotFoundException if the user is not found
     */
    public function loadUserByIdentifier($identifier): UserInterface
    {
        // Load a User object from your data source or throw UserNotFoundException.
        // The $identifier argument may not actually be a username:
        // it is whatever value is being returned by the getUserIdentifier()
        // method in your User class.
        $session = $this->requestStack->getSession();
        $userData = $session->get('user_'.$identifier);
        $selectedProfile = $session->get('selected_profile'.$identifier);

        $selectedProfile = $selectedProfile ? $this->profileLoader->refreshProfile($selectedProfile->getId()) : null;
        
        $user = new User();
        $user->setOidcUserData($userData);
        $user->setProfile($selectedProfile);
 
        if ($profiles = $this->profileLoader->getProfiles($user->getRoles())) {
            $user->setProfiles($profiles);
        }
        
        if (!$userData || !$user) {
            throw new UserNotFoundException(sprintf('User "%s" not found.', $identifier));
        }

 		return $user;
    }

    /**
     * Tells Symfony to use this provider for this User class.
     */
    public function supportsClass(string $class): bool
    {
        return User::class === $class || is_subclass_of($class, User::class);
    }

     /**
     * Refreshes the user after being reloaded from the session.
     *
     * When a user is logged in, at the beginning of each request, the
     * User object is loaded from the session and then this method is
     * called. Your job is to make sure the user's data is still fresh by,
     * for example, re-querying for fresh User data.
     *
     * If your firewall is "stateless: true" (for a pure API), this
     * method is not called.
     */
    public function refreshUser(UserInterface $user): UserInterface
    {
        if (!$user instanceof User) {
            throw new UnsupportedUserException(sprintf('Invalid user class "%s".', get_class($user)));
        }
        
        return $this->loadUserByIdentifier($user->getUserIdentifier());
        // Return a User object after making sure its data is "fresh".
        // Or throw a UsernameNotFoundException if the user no longer exists.

    }

    /** 
    * @throws OidcException Can be thrown when the user cannot be created 
    */
    public function ensureUserExists(string $userIdentifier, OidcUserData $userData)
    {
        $session = $this->requestStack->getSession();
        $session->set('user_'.$userIdentifier, $userData);
    }

  	/** 
    * Custom user loader method to be able to distinguish oidc authentications 
    */
    public function loadOidcUser(string $userIdentifier): UserInterface
    {
    	return $this->loadUserByIdentifier($userIdentifier);	
    }
}
