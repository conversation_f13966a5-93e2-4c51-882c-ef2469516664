<?php

namespace App\Import;

use App\Entity\Brand;
use App\Entity\Channel;
use App\Entity\Country;
use App\Entity\Language;
use App\Entity\TranslationKey;
use App\Entity\Widget;
use App\Import\Model\CommonTranslationCsv;
use App\Import\Model\LocalTranslationCsv;
use App\Import\Model\ReferenceTranslationCsv;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

class CommonTranslationCsvValidator
{
    private EntityManagerInterface $entityManager;

    /** @var array<string, bool> | null */
    private ?array $checkedCultures = null;

    /** @var array<string, bool> */
    private array $validTranslationKeys = [];

    /** @var array<string, bool> */
    private array $validBrands;

    /** @var array<string, bool> */
    private array $validWidgets;

    /** @var array<string, bool> */
    private array $validLanguages;

    /** @var array<string, bool> */
    private array $validReferenceLanguages;

    private bool $isWarmedUp = false;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
        
    }

    public function warmUp(): void
    {
        // reset cache from an import operation and the previous one
        $this->coolOff();

        $this->validTranslationKeys = $this->loadValidTranslationKeys();
        $this->validBrands = $this->loadValidBrands();
        $this->validWidgets = $this->loadValidWidgets();
        $this->validLanguages = $this->loadValidLanguages();
        $this->validReferenceLanguages = $this->loadValidReferenceLanguages();
        
        $this->isWarmedUp = true;
    }

    public function coolOff(): void
    {
        $this->checkedCultures = null;
        $this->validTranslationKeys = [];
        $this->validBrands = [];
        $this->validWidgets = [];
        $this->validLanguages = [];
        $this->validReferenceLanguages = [];
        
        $this->isWarmedUp = false;
    }
    
    /**
     * @return array<string, bool>
    */
    private function loadValidTranslationKeys(): array
    {
        $translationKeys = $this->entityManager->getRepository(TranslationKey::class)->findAll();
        $validKeys = [];

        foreach ($translationKeys as $translationKey) {
            $validKeys[strtolower($translationKey->getLabelKey() ?? '')] = true;
        }

        return $validKeys;
    }

    public function validateKey(CommonTranslationCsv $csvData, ExecutionContextInterface $context, mixed $payload): void
    {
        $key = trim(strtolower($csvData->getKey()));
    }

    public function validateCulture(LocalTranslationCsv $csvData, ExecutionContextInterface $context, mixed $payload): void
    {
        $this->checkIfCacheIsReady();

        $culture = $csvData->getCulture();

        if (isset($this->checkedCultures[$culture])) {
            return;
        }

        [$languageCode, $countryCode] = explode('-', $culture);
        $languageCode = trim(strtoupper($languageCode));
        $countryCode = trim(strtoupper($countryCode));

        $language = $this->entityManager->getRepository(Language::class)->findOneBy(['code' => $languageCode]);
        $country = $this->entityManager->getRepository(Country::class)->findOneBy(['code' => $countryCode]);

        if (!$language) {
            $context->buildViolation('The language code does not exist.')
                ->atPath('culture')
                ->addViolation();
        }

        if (!$country) {
            $context->buildViolation('The country code does not exist.')
                ->atPath('culture')
                ->addViolation();
        }

        $this->checkedCultures[$culture] = true;
    }

    /**
     * @return array<string, bool>
     */
    private function loadValidReferenceLanguages(): array
    {
        $languages = $this->entityManager->getRepository(Language::class)->findBy(['isReference' => 1]);
        $validReferenceLanguages = [];

        foreach ($languages as $language) {
            $validReferenceLanguages[strtoupper($language->getCode() ?? '')] = true;
        }

        return $validReferenceLanguages;
    }

    public function validateReferenceLanguage(ReferenceTranslationCsv $value, ExecutionContextInterface $context, mixed $payload): void
    {
        $this->checkIfCacheIsReady();

        $language = trim(strtoupper($value->getLanguage()));

        if (!isset($this->validReferenceLanguages[$language])) {
            $context->buildViolation('The language does not exist in the Language table or is not a reference language.')
                ->atPath('language')
                ->addViolation();
        }
    }

    /**
     * @return array<string, bool>
     */
    private function loadValidLanguages(): array
    {
        $languages = $this->entityManager->getRepository(Language::class)->findAll();
        $validLanguages = [];

        foreach ($languages as $language) {
            $validLanguages[strtoupper($language->getCode() ?? '')] = true;
        }

        return $validLanguages;
    }

    public function validateLanguage(ReferenceTranslationCsv $value, ExecutionContextInterface $context, mixed $payload): void
    {
        $this->checkIfCacheIsReady();
        
        $language = trim(strtoupper($value->getLanguage()));

        if (!isset($this->validLanguages[$language])) {
            $context->buildViolation('The language does not exist in the Language table.')
                ->atPath('language')
                ->addViolation();
        }
    }

    /**
     * @return array<string, bool>
     */
    private function loadValidBrands(): array
    {
        $brands = $this->entityManager->getRepository(Brand::class)->findAll();
        $validBrands = [];

        foreach ($brands as $brand) {
            $validBrands[strtoupper($brand->getCode() ?? '')] = true;
        }

        return $validBrands;
    }

    public function validateBrand(ReferenceTranslationCsv $csvData, ExecutionContextInterface $context, mixed $payload): void
    {
        $this->checkIfCacheIsReady();

        $brand = trim(strtoupper($csvData->getBrand()));

        if (!isset($this->validBrands[$brand])) {
            $context->buildViolation('The brand does not exist in the Brand table.')
                ->atPath('brand')
                ->addViolation();
        }
    }

    /**
     * @return array<string, bool>
     */
    private function loadValidWidgets(): array
    {
        $widgets = $this->entityManager->getRepository(Widget::class)->findAll();
        $validWidgets = [];

        foreach ($widgets as $widget) {
            $validWidgets[strtolower($widget->getName() ?? '')] = true;
        }

        return $validWidgets;
    }

    public function validateWidget(CommonTranslationCsv $csvData, ExecutionContextInterface $context, mixed $payload): void
    {
        $this->checkIfCacheIsReady();

        $widget = trim(strtolower($csvData->getWidget()));

        // empty value is allowed
        if(empty($widget)) {
            return;
        }

        if (!isset($this->validWidgets[$widget])) {
            $context->buildViolation('The widget does not exist in the Widget table.')
                ->atPath('widget')
                ->addViolation();
        }
    }

    public function validateSource(CommonTranslationCsv $csvData, ExecutionContextInterface $context, mixed $payload): void
    {
        $source = trim(strtoupper($csvData->getSource()));
        $validSources = $this->entityManager->getRepository(Channel::class)->getChannelMap();

        if (!in_array($source, $validSources)) {
            $context->buildViolation('The source is invalid')
                ->atPath('source')
                ->addViolation();
        }
    }

    private function checkIfCacheIsReady(): void
    {
        if (!$this->isWarmedUp) {
            throw new \RuntimeException('The cache for '. self::class . ' is not ready. Call warmUp() first.');
        }
    }

}