<?php

namespace App\Service;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Symfony\Component\HttpFoundation\Response;



class LogIncidentManager
{
    public function __construct(private string $baseUrl, private Client $client)
    {
    }
    public function getLogIncidents($status, $brand, $country, $additionnalOptions = [], bool $isSuperAdmin = false): array
    {
        try {
            $options = [];
            if ($isSuperAdmin) {
                $brand = $country = 'CT';
            }
            if ($status)
                $options['status'] = $status;

            if ($brand)
                $options['brand'] = $brand;

            if ($country)
                $options['country'] = $country;

            $options = ['query' => array_merge($options, $additionnalOptions), 'headers' => [
                    'Accept' => 'application/json',
                ]];

            $response = $this->client->get(
                $this->baseUrl . "v1/logincident",
                $options
            );
            // $response = $this->client->get(
            //     "https://api-log-incident-integ.mym.awsmpsa.com/v1/logincident?brand=AC&country=fr&status=0&limit=10&offset=0&type=non-care"
                
            // );
            $data =  json_decode($response->getBody()->getContents(), true);

            return $data;
        } catch (RequestException $e) {
            $response = $e->getResponse();
            $code = Response::HTTP_GATEWAY_TIMEOUT;

            if ($e->hasResponse()) {
                $code = $response->getStatusCode();
            }

            return [$response, $code];
        }
    }

    public function getLogIncidentById(int $id, $brand, $country, bool $isSuperAdmin = false ): array
    {
        try {
            $options = [];
            if ($isSuperAdmin) {
                $brand = $country = 'CT';
            }
            if ($brand)
                $options['query']['brand'] = $brand->getCode();

            if ($country)
                $options['query']['country'] = $country->getCode();


            $response = $this->client->get(
                $this->baseUrl . "v1/logincident/" . $id,
                $options
            );
            return  json_decode($response->getBody()->getContents(), true);
        } catch (RequestException $e) {
            $response = $e->getResponse();
            $code = Response::HTTP_GATEWAY_TIMEOUT;

            if ($e->hasResponse()) {
                $code = $response->getStatusCode();
            }

            // Todo log exception
            return [$response, $code];
        }
    }

    public function deleteLogIncident($id, $brand, $country, bool $isSuperAdmin= false): array
    {
        try {

            $options = [];
            if ($isSuperAdmin) {
                $brand = $country = 'CT';
            }
            if ($brand)
                $options['query']['brand'] = $brand;

            if ($country)
                $options['query']['country'] = $country;


            $bodyData = [
                'status' => 1
            ];

            $options['body'] = json_encode($bodyData);
            $response = $this->client->get(
                $this->baseUrl . "v1/logincident/" . $id,
                $options
            );

            return  json_decode($response->getBody()->getContents(), true);
        } catch (RequestException $e) {
            $response = $e->getResponse();
            $code = Response::HTTP_GATEWAY_TIMEOUT;

            if ($e->hasResponse()) {
                $code = $response->getStatusCode();
            }

            // Todo log exception
            return [$response, $code];
        }
    }
}