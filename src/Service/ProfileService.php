<?php

namespace App\Service;

use App\Entity\Brand;
use App\Entity\Profile;
use App\Entity\Role;
use App\Entity\Menu;
use App\Entity\RoleMenu;
use App\Entity\Site;
use App\Repository\FavoriteRepository;
use App\Repository\MenuRepository;
use App\Repository\ProfileMenuRepository;
use App\Repository\ProfileRepository;
use App\Security\User;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class ProfileService
{
    public function __construct(
        private EntityManagerInterface $em,
        private ProfileRepository $profileRepository,
        private MenuRepository $menuRepository,
        private ProfileMenuRepository $profileMenuRepository,
        private FavoriteRepository $favoriteRepository,
        private TranslatorInterface $translator
    ) {
    }

    public function getAllProfilesOrderedBySite()
    {
        $profiles = $this->profileRepository->getAllProfiles();

        return $profiles;
    }

    public function grantRoleAccessToMenu(Role $role, Menu $menu, ?string $permission): void
    {
        $roleMenu = $this->em->getRepository(RoleMenu::class)->findOneBy(['role' => $role, 'menu' => $menu]);
        if (!$roleMenu) {
            $roleMenu = (new RoleMenu)->setMenu($menu)->setRole($role);
        }
        $roleMenu->setPermission($permission);

        $role->addRoleMenu($roleMenu);
        $this->em->flush();
    }

    public function removeRoleAccessToMenu(Role $role, Menu $menu): void
    {
        $roleMenu = $this->em->getRepository(RoleMenu::class)->findOneBy(['role' => $role, 'menu' => $menu]);
        if ($roleMenu) {
            $role->removeRoleMenu($roleMenu);
            $this->em->flush();
        }
    }

    public function getBrandCssPathFilename(?Brand $brand): string
    {
        $brandCode = $brand ? $brand->getCode() : 'xx';
        $brandCode = strtolower($brandCode);
        $brandCssPathFilename = '/css/' . $brandCode . '/style.css';

        return $brandCssPathFilename;
    }

    public function getLogoText(?Profile $profile): string
    {
        $textLogo = $profile?->getSite()?->getCountry()?->getName() ? $profile->getSite()->getCountry()->getName() : 'Administration';
        $textLogo = $profile?->getSite()?->getLabel() === 'ADMINISTRATION' ? '' : $textLogo;  # empty if no country is Administrator profile
        return $textLogo;
    }

    public function getBrand(?Profile $profile): string
    {
        $brand = $profile?->getSite()?->getBrand()?->getCode() ?? $profile->getBrand() ?? "XX";
        return $brand;
    }

    public function getMenuTreeNodesBySiteRole(Site $site, Role $role): array
    {
        $profile = $this->profileRepository->findOneBy(['site' => $site, 'role' => $role]);
        if (empty($profile)) {

            return [];
        }

        return $this->getMenuTreeNodes($profile);
    }

    /**
     * get the list of menu affected to a profile
     */
    public function getMenuTreeNodes(Profile $profile): array
    {
        $authorizedMenus = $this->getAuthorizedMenus($profile);

        $role = $profile->getRole();
        $roleMenus = $role->getRoleMenus();
        $menuTreeNodes = $menuNode = [];
        foreach ($roleMenus as $roleMenu) {
            $menu = $roleMenu->getMenu();
            $menuNode = $this->getParentNodes($menu, $authorizedMenus, []);
            $parentKey = array_search($menuNode['id'], array_column($menuTreeNodes, 'id'));
            if ($parentKey === false) {
                $menuTreeNodes[] = $menuNode;
            } else {
                $menuTreeNodes[$parentKey]['children'] = array_merge(
                    $menuTreeNodes[$parentKey]['children'],
                    $menuNode['children']
                );
            }
        }
        $menuTreeNodes = $this->nodesAsort($menuTreeNodes);

        return $menuTreeNodes;
    }

    /**
     * get clean menus data to avoid duplication
     */
    private function nodesAsort(array $menuTreeNodes): array
    {
        $menusNew = [];
        foreach ($menuTreeNodes as $key => $node) {
            $keySearch = array_search($node['id'], array_column($menusNew, 'id'));
            if (isset($node['children'])) {
                $node['children'] = $this->nodesAsort($node['children']);
            }
            if ($keySearch !== false) {
                $menusNew[$keySearch]['children'] = array_merge(
                    $menusNew[$keySearch]['children'] ?? [],
                    $node['children'] ?? []
                );
            } else {
                $menusNew[] = $node;
            }
        }
        return $menusNew;
    }

    /**
     * get parent nodes with the right structure requested by jsTree library
     * @param Menu $menu
     * @param array $authorizedMenus
     * @param array $parents (optional)
     * @return array
     */
    private function getParentNodes(Menu $menu, array $authorizedMenus, array $parents = []): array
    {
        $node = [
            'id' => $menu->getId(),
            'text' => $this->translator->trans($menu->getLabel()),
            'state' => ['opened' => true]
        ];

        if ($menu->getSubMenus()->count() == 0) {
            $node['state']["checked"] = true;
            if ($authorizedMenus) {
                $node['state']["checked"] = (in_array($menu->getId(), $authorizedMenus)) ? true : false;
            }
        }

        if (isset($authorizedMenus[$menu->getId()])) {
            // get current permission to current node data
            $node['data'] = ['dropdownSelectedId' => $authorizedMenus[$menu->getId()]];
        }

        $node['type'] = 'page';
        if ($parents) {
            $node['children'] = [$parents];
            $node['type'] = 'parent';
        }

        if (!is_null($menu->getParent())) {
            $menu = $menu->getParent();
            $node = $this->getParentNodes($menu, $authorizedMenus, $node);
        }

        return $node;
    }

    /**
     * get list of authorized menus from Roles and/or Profiles
     * @param Profile $profile
     * @return array
     */
    public function getAuthorizedMenus(Profile $profile): array
    {
        $role = $profile->getRole();
        $roleMenus = $role->getRoleMenus();
        $profileMenus = $profile->getProfileMenus();
        $authorizedMenus = [];
        /* check if we have profile menus to compare with roles */
        if ($profileMenus->count() > 0) {
            foreach ($roleMenus as $roleMenu) {
                foreach ($profileMenus as $profileMenu) {
                    if ($profileMenu->getMenu()->getId() == $roleMenu->getMenu()->getId()) {
                        $authorizedMenus[$profileMenu->getMenu()->getId()] = $profileMenu->getPermission();
                    } else {
                        if (isset($authorizedMenus[$roleMenu->getMenu()->getId()])) {
                            continue;
                        }
                        $authorizedMenus[$roleMenu->getMenu()->getId()] = $roleMenu->getPermission();
                    }
                }
            }
        } else {
            foreach ($roleMenus as $roleMenu) {
                $authorizedMenus[$roleMenu->getMenu()->getId()] = $roleMenu->getPermission();
            }
        }

        return $authorizedMenus;
    }

    /**
     * get list of authorized menus with parents
     * @param Profile $profile
     * @return array
     */
    public function getCleanAuthorizedMenus(Profile $profile): array
    {
        $authorizMenusIds = $this->getAuthorizedMenus($profile);
        $menusIds = [];
        foreach ($authorizMenusIds as $menuId => $permission) {
            if ($permission != 'D') {
                $menusIds[] = $menuId;
            }
        }
        $menusIds = $this->getAllMenusParentsIds($profile, $menusIds);

        return $menusIds;
    }

    /**
     * prepare the Menus data that will display in the view (respect jsTree format)
     * @param $menus
     * @return array
     */
    function getTreeNodes(Role $role): array
    {
        $menus = $this->menuRepository->getAllMenus();
        // get the menus of the selected role
        $selectedRoleMenus = $role->getRoleMenus()->toArray();
        $selectedMenus = [];
        foreach ($selectedRoleMenus as $roleMenu) {
            $selectedMenus[$roleMenu->getMenu()->getId()] = [
                'permission' => $roleMenu->getPermission(),
            ];
        }
        $menuTreeNodes = [];
        foreach ($menus as $menu) {
            $node = $this->getNodeChildren($menu->getSubMenus(), $selectedMenus);
            $menuTreeNodes[$menu->getLabel()] = [
                'id' => $menu->getId(),
                'text' => $this->translator->trans($menu->getLabel()),
                'label' => $menu->getLabel(),
                'type' => ($node) ? 'parent' : 'page',
                'state' => ['opened' => true, "checked" => (array_search($menu->getLabel(), array_column($selectedMenus, 'label')) !== false) ? true : false],
                'children' => $node,
            ];
            if ($menuTreeNodes[$menu->getLabel()]['type'] === 'page' && isset($selectedMenus[$menu->getId()])) {
                $menuTreeNodes[$menu->getLabel()]['data'] = ['dropdownSelectedId' => $selectedMenus[$menu->getId()]['permission']];
            }
        }
        $menuTreeNodes = $this->unsetFunctionMenusByRole($menuTreeNodes, $role);

        return array_values($menuTreeNodes);
    }

    /**
     * prepare the Menus data that will display in the view (respect jsTree format)
     * @param Collection $menus
     * @param array $selectedMenus
     * @return array
     */
    private function getNodeChildren(Collection $menus, array $selectedMenus): array
    {
        $treeMenu = [];
        foreach ($menus as $menu) {
            $node = [
                'id' => $menu->getId(),
                'label' => $menu->getLabel(),
                'state' => ['opened' => true, "checked" => (array_search($menu->getLabel(), array_column($selectedMenus, 'label')) !== false) ? true : false],
                'text' => $this->translator->trans($menu->getLabel()),
                'type' => ($menu->getSubMenus()->count() > 0) ? 'parent' : 'page',
                'children' => ($menu->getSubMenus()) ? $this->getNodeChildren($menu->getSubMenus(), $selectedMenus) : [],
            ];
            if (isset($selectedMenus[$menu->getId()])) {
                $node['data'] = ['dropdownSelectedId' => $selectedMenus[$menu->getId()]['permission']];
            }
            $treeMenu[] = $node;
        }

        return $treeMenu;
    }

    /**
     * get selected menus from database
     * @param array $menusIds
     * @return array
     */
    function getSelectedMenus(array $menusIds): array
    {
        $menus = $this->menuRepository->getSelectedMenus($menusIds);

        return $menus;
    }

    /**
     * get all menus parents 
     * @param Profile $profile
     * @param array $menusIds
     * @return array
     */
    public function getAllMenusParentsIds(Profile $profile, array $menusIds): array
    {
        $roleMenus = $profile->getRole()->getRoleMenus();
        foreach ($roleMenus as $item) {
            if (in_array($item->getMenu()->getId(), $menusIds)) {
                $this->getMenuParentId($item->getMenu(), $menusIds);
            }
        }

        return $menusIds;
    }

    /**
     * get parent menu recursively and add the current menu to the list
     * @param Menu $menu
     * @param array $menusIds
     * @return void
     */
    public function getMenuParentId(Menu $menu, &$menusIds): void
    {
        if (!in_array($menu->getId(), $menusIds)) {
            $menusIds[] = $menu->getId();
        }

        if ($menu->getParent() != null) {
            $this->getMenuParentId($menu->getParent(), $menusIds);
        }
    }

    public function removeOrphanData(Role $role, User $user): void
    {
        $username = $user->getUserIdentifier();
        $this->favoriteRepository->removeOrphanFavorites($role, $username);
        $this->profileMenuRepository->removeOrphanMenus($role);
    }

    /**
     * @return array<array-key, string>
     */
    public function getUserCultures(User $user): array
    {
        $profiles = $user->getProfiles();
        $brand = $user->getProfile()->getBrand() ?? $user->getProfile()->getSite()->getBrand();
        $country = $user->getProfile()->getSite()->getCountry();

        if ($user->getProfile()->isSuperAdministrator()) {
            $cultures = $this->profileRepository->getProfilesCulture($profiles);
        } elseif ($user->getProfile()->isLocalAdministrator()) {
            $cultures = $this->profileRepository->getProfilesCulture($profiles, $brand);
        } else {
            $cultures = $this->profileRepository->getProfilesCulture($profiles, $brand, $country);
        }

        return array_map(function ($culture) {
            return $culture['culture'];
        }, $cultures);
    }

    private function unsetFunctionMenusByRole(array $menuTreeNodes, Role $role): array
{
    $roleLabel = $role->getLabel();

    // Define the allowed roles for each feature
    $roleConditions = [
        FeatureSettingManager::GLOBAL_FEATURES_MENU => Role::GLOBAL_ADMIN_ROLES,
        FeatureSettingManager::BRAND_FEATURES_MENU => Role::BRAND_ADMIN_ROLES,
        FeatureSettingManager::LOCAL_FEATURES_MENU => Role::LOCAL_ADMIN_ROLES,
    ];

    foreach ($menuTreeNodes as &$menuTreeNode) {
        // Only process the 'features' node
        if ($menuTreeNode['label'] === 'features' && is_array($menuTreeNode['children'])) {
            foreach ($menuTreeNode['children'] as $key => $child) {
                if (isset($child['label'])) {
                    // Check if the child's label matches any in the role conditions
                    foreach ($roleConditions as $menuLabel => $allowedRoles) {
                        if ($child['label'] === $menuLabel && !in_array($roleLabel, $allowedRoles)) {
                            // Remove the child if the role doesn't match
                            unset($menuTreeNode['children'][$key]);
                        }
                    }
                }
            }

            // Reindex the children array after unsetting items
            $menuTreeNode['children'] = array_values($menuTreeNode['children']);
        }
    }

    return $menuTreeNodes;
}

}
