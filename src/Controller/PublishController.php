<?php

namespace App\Controller;

use App\Security\User;
use App\Form\PublishForm;
use App\Service\PublishManager;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use App\Repository\LocalKeyJsonReleaseRepository;
use App\Repository\SiteRepository;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Security\Http\Attribute\CurrentUser;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;

#[Route(path: '/publish', name: 'publish_')]
class PublishController extends AbstractController
{
    public function __construct(private LocalKeyJsonReleaseRepository $LocalKeyJsonReleaseRepository, private SiteRepository $siteRepository) {}

    #[Route(path: '/', name: 'list')]
    public function index(#[CurrentUser] User $user): Response
    {
        $profile = $user->getProfile();
        $site = $profile->getSite();
        $brand = $profile->getBrand() ?? $site->getBrand();
        $country = $site->getCountry();

        $criteria = [];
        if ($brand !== null) {
            $criteria['brand'] = $brand;
        }

        if ($country !== null) {
            $criteria['country'] = $country;
        }

        $keyList = !($profile->isSuperAdmin())
            ? $this->LocalKeyJsonReleaseRepository->findBy($criteria)
            : $this->LocalKeyJsonReleaseRepository->findAll();

        $keyList = $keyList ?: [];

        return $this->render('publish/index.html.twig', [
            'keyList' => $keyList,
            'site' => $site,
            'profile' => $profile
        ]);
    }

    #[Route(path: '/release', name: 'release')]
    public function publishMultiple(Request $request, PublishManager $publishManager, #[CurrentUser] User $user): Response|RedirectResponse
    {
        $profile = $user->getProfile();
        $site = $profile->getSite();
        $form = $this->createForm(PublishForm::class, null, ['site' => $site, 'profile' => $profile]);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid() && !$request->isXmlHttpRequest()) {
            $data = $form->getData();
            if ($profile->isSuperAdministrator()) {
                $brandList = isset($data['brandList']) ? $data['brandList'] : [];
            } else {
                $brand = $profile->isBrandAdministrator() ? $profile->getBrand()->getName() : $site->getBrand()->getName();
                $brandList = [$brand];
            }
            if (!$profile->isCountryUser()) {
                $countryList = isset($data['countryList']) ? $data['countryList'] : [];
            } else {
                $countryList = [$site->getCountry()->getName()];
            }
            $languageList = isset($data['languageList']) ? $data['languageList'] : [];
            $referenceLanguageList = isset($data['referenceLanguageList']) ? $data['referenceLanguageList'] : [];
            $source = isset($data['source']) ? $data['source'] : [];

            if (!empty($brandList) || !empty($countryList) || !empty($languageList) || !empty($referenceLanguageList) || !empty($source)) {
                $response = $publishManager->saveMultiple($brandList, $countryList, $languageList, $referenceLanguageList, $source);
                if ($response->getData() && $response->getCode() == Response::HTTP_OK) {
                    $this->addFlash('success', 'Successfully updated');
                } else {
                    $this->addFlash('danger', 'failed in synchronization');
                }
                return $this->redirectToRoute('publish_list');
            } else {
                $this->addFlash('danger', 'Please fill in at least one parameter');
            }
        }
        return $this->render('publish/publish.html.twig', [
            'form' => $form->createView(),
            'profile' => $profile
        ]);
    }

    #[Route(path: '/{id}/upload', name: 'upload')]
    public function upload(Request $request, PublishManager $publishManager): Response
    {
        $key = $this->LocalKeyJsonReleaseRepository->find($request->get('id'));
        $site = $this->siteRepository->findOneBy(['brand' => $key->getBrand(), 'country' => $key->getCountry()]);

        if ($site) {
            $response = $publishManager->save($key, $site);
            if ($response->getData() && $response->getCode() == Response::HTTP_OK) {
                $this->addFlash('success', 'Successfully updated AWS S3 and MongoDB ');
            } else {
                $this->addFlash('danger', 'failed in synchronization');
            }
        } else {
            $this->addFlash('danger', 'failed in synchronization');
        }
        return $this->redirectToRoute('publish_list');
    }

    #[Route(path: '/{id}/view', name: 'view')]
    public function view(Request $request, PublishManager $publishManager): Response
    {
        $key = $this->LocalKeyJsonReleaseRepository->find($request->get('id'));
        $site = $this->siteRepository->findOneBy(['brand' => $key->getBrand(), 'country' => $key->getCountry()]);

        $jsonPacket = $publishManager->getS3Resource($key, $site);

        return $this->render('publish/jsonViewer.html.twig', [
            'currentJson' => $jsonPacket['currentJson'],
            'releasedJson' => $jsonPacket['releasedJson'],
            'languageCode' => $jsonPacket['languageCode'],
            'filename' => "translations.json",
            'diffJson' => $jsonPacket['diff'],
            'path' => $jsonPacket['path'],
            'id' => $key->getId(),
            'status' => $key->getStatus()
        ]);
    }
}