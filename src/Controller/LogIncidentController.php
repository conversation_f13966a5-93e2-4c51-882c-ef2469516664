<?php

namespace App\Controller;

use App\Security\User;
use App\Form\LogIncidentFiltererType;
use App\Service\LogIncidentManager;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;

#[Route('/log-incident', name: 'log_incident_')]
class LogIncidentController extends AbstractController
{
    #[Route('/', name: 'list')]
    public function index(Request $request): Response
    {
        $form = $this->createForm(LogIncidentFiltererType::class)->handleRequest($request);
        return $this->render('log_incident/index.html.twig', [
            'form' => $form->createView(),
        ]);
    }
    #[Route('/paginate', name: 'paginate', methods: ['GET', 'POST'])]
    public function paginateAction(#[CurrentUser] User $user, Request $request, LogIncidentManager $manager): Response
    {
        try {
            // Get sorting parameters - use all() for arrays
            $orders = $request->query->all('order') ?? $request->request->all('order') ?? [];
            $columns = $request->query->all('columns') ?? $request->request->all('columns') ?? [];
            
            $orderBy = '';
            $orderDir = '';
            
            // Process ordering
            if (!empty($orders) && !empty($columns)) {
                foreach ($orders as $key => $order) {
                    if (isset($order['column']) && isset($columns[$order['column']]['data'])) {
                        $orderBy = $columns[$order['column']]['data'];
                        $orderDir = $order['dir'] ?? 'asc';
                        break;
                    }
                }
            }
            
            $profile = $user->getProfile();
            $site = $profile->getSite();
            $brand = $site->getBrand();
            $country = $site->getCountry();
            
            // Get pagination parameters
            $length = (int)($request->query->get('length') ?? $request->request->get('length') ?? 10);
            $start = (int)($request->query->get('start') ?? $request->request->get('start') ?? 0);
            
            // Properly handle the search parameter which can be an array
            $searchValue = '';
            $searchParam = $request->query->has('search') ? $request->query->all('search') : 
                        ($request->request->has('search') ? $request->request->all('search') : []);
            
            if (is_array($searchParam)) {
                $searchValue = $searchParam['value'] ?? '';
            } else {
                $searchValue = (string)$searchParam;
            }
            
            // Get filters
            $vin = (string)strtolower(($request->query->get('vin') ?? $request->request->get('vin') ?? ''));
            $email = (string)($request->query->get('email') ?? $request->request->get('email') ?? '');
            $date = (string)($request->query->get('creation_date') ?? $request->request->get('creation_date') ?? '');
            $end_date = (string)($request->query->get('end_date') ?? $request->request->get('end_date') ?? '');
            
            $additionalOptions = [
                'offset' => $start,
                'limit' => $length,
                'search' => $searchValue,
                'order_by' => $orderBy,
                'order_dir' => $orderDir,
                'vin' => $vin,
                'email' => $email,
                'start_date' => $date,
                'end_date' => $end_date,
            ];
            
            // Get data
            $logIncidents = $manager->getLogIncidents(
                '0',
                $brand->getCode() ?? '',
                $country->getCode() ?? '',
                $additionalOptions,
                $profile->isSuperAdmin()
            );
            
            // Process results
            $data = [];
            $count = 0;
            
            if (isset($logIncidents['success'])) {
                $data = $logIncidents['success']['data'] ?? [];
                $count = $logIncidents['success']['total'] ?? 0;
                
                // Format dates
                foreach ($data as &$item) {
                    if (isset($item['creation_date']) && !empty($item['creation_date'])) {
                        try {
                            $dateObj = new \DateTime($item['creation_date']);
                            $item['creation_date'] = $dateObj->format('Y-m-d H:i:s');
                        } catch (\Exception $e) {
                            // Keep original if date parsing fails
                        }
                    }
                }
            }
            
            // Prepare output for DataTables
            $draw = (int)($request->query->get('draw') ?? $request->request->get('draw') ?? 1);
            
            return new JsonResponse([
                'data' => $data,
                'recordsFiltered' => $count,
                'recordsTotal' => $count,
                'draw' => $draw
            ]);
            
        } catch (\Exception $e) {
            return new JsonResponse([
                'error' => true,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ], 500);
        }
    }

    #[Route('/view/{id}', name: 'show', methods: ['GET'])]
    public function show(int $id, #[CurrentUser] User $user, Request $request, LogIncidentManager $manager)
    {
        $profile = $user->getProfile();
        $site      = $profile->getSite();
        $brand = $site->getBrand();
        $country = $site->getCountry();

        $logIncident = $manager->getLogIncidentById(
            $id,
            $brand,
            $country
        );

        return $this->render('log_incident/show.html.twig', [
            'logIncident' => isset($logIncident['success']) ? $logIncident['success'] : array(),
        ]);
    }

    #[Route('/delete/{id}', name: 'delete', methods: ['GET', 'POST'])]
    public function delete(#[CurrentUser] User $user, int $id, Request $request, LogIncidentManager $manager): Response
    {
        if (!$this->isCsrfTokenValid('delete-log-incident', $request->request->get('_token')))
        {
            throw $this->createAccessDeniedException();

        }

        $profile = $user->getProfile();
        $site      = $profile->getSite();

        $brand = $site->getBrand();
        $country = $site->getCountry();

        $response = $manager->deleteLogIncident(
            $id,
            $brand->getCode(),
            $country->getCode(),
            $profile->isSuperAdmin()
        );

        if (isset($response['error'])) {
            $validation = $response['error'];
            throw new \Exception($validation['content']);
        }
        return $this->redirectToRoute('log_incident_list', [
        ]);
    }
}
