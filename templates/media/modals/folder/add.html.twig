<div class="modal fade" id="add-folder-modal" tabindex="-1" aria-labelledby="add-folder-modal-label" aria-hidden="true">
	<div class="modal-dialog modal-md modal-dialog-centered">
		<div class="modal-content">
			<div class="modal-header">
				<h5 class="modal-title" id="add-folder-modal-label">{{ 'add_folder'|trans }}</h5>
				<button class="close" type="button" data-bs-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">×</span>
				</button>
			</div>

			<div class="modal-body">
				<div class="alert alert-secondary mb-4 text-center d-none" id="alert-loading" role="alert">
					<img src="/images/loading.png" alt="loading" class="loading" width="35"/>
				</div>
				<div class="alert alert-success mb-4 d-none" id="alert-success" role="alert">
					<span class="title"></span>
				</div>
				<div class="alert alert-danger mb-4 d-none" id="alert-error" role="alert">
					<span class="title"></span>
				</div>
				<div id="bloc_add">
					<div class="row">
						<label for="add_input" class="fw-normal fs-6 col-form-label col-sm-4">{{ 'folder_name'|trans }}:</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control" id="add_input" placeholder="{{ 'folder_name'|trans }}">
                        </div>
						
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-primary save-add-folder btn-actions">
					<i class="fas fa-save ms-1"></i>
					{{ 'add'|trans }}
				</button>
				<button type="button" class="btn btn-secondary close-add-folder btn-actions" data-bs-dismiss="modal">
					<i class="fas fa-times-circle ms-1"></i>
					{{ 'btn_cancel'|trans }}
				</button>
			</div>
		</div>
	</div>
</div>
