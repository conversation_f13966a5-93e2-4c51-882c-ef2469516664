<?php

namespace App\Repository;

use App\Entity\EvRouting;
use App\Entity\Language;
use App\Entity\Site;
use App\Entity\VehicleLabel;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<EvRouting>
 *
 * @method EvRouting|null find($id, $lockMode = null, $lockVersion = null)
 * @method EvRouting|null findOneBy(array $criteria, array $orderBy = null)
 * @method EvRouting[]    findAll()
 * @method EvRouting[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class EvRoutingRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, EvRouting::class);
    }

    /**
     * Fetch vehicle labels with localized data for a site and language.
     *
     * @param Site|null $site
     * @param Language $language
     * @return array<mixed>
     */
    public function getVehicleLabelsWithLanguages(?Site $site, Language $language): array
    {
        return $this->createQueryBuilder('vehicleLabel')
            ->select(['vehicleLabel as label', 'localeVehicleLabel.localLabel as localLabel', 'localeVehicleLabel.creationDate'])
            ->leftJoin(
                VehicleLabel::class,
                'localeVehicleLabel',
                Join::WITH,
                'localeVehicleLabel.vehicleLabel = vehicleLabel AND localeVehicleLabel.language = :language AND localeVehicleLabel.site = :site'
            )
            ->where('vehicleLabel.brand = :brand')
            ->setParameter('site', $site)
            ->setParameter('language', $language)
            ->setParameter('brand', $site?->getBrand())
            ->getQuery()
            ->getResult();
    }

    /**
     * Find a VehicleLabel by brand and LCDV (substring match).
     *
     * @param string $brand
     * @param string $lcdv
     * @return VehicleLabel|null
     */
    public function findOneByBrandAndLcdv(string $brand, string $lcdv): ?VehicleLabel
    {
        return $this->createQueryBuilder('vehicleLabel')
            ->join('vehicleLabel.lcdvs', 'lcdv')
            ->where('vehicleLabel.brand = :brand')
            ->andWhere('lcdv.lcdv = substring(:lcdv, 1, length(lcdv.lcdv))')
            ->setParameter('brand', $brand)
            ->setParameter('lcdv', $lcdv)
            ->orderBy('length(lcdv.lcdv)', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * Find a VehicleLabel by brand and RPO (substring match).
     *
     * @param string $brand
     * @param string $rpo
     * @return VehicleLabel|null
     */
    public function findOneByBrandAndRpo(string $brand, string $rpo): ?VehicleLabel
    {
        return $this->createQueryBuilder('vehicleLabel')
            ->join('vehicleLabel.rpos', 'rpo')
            ->where('vehicleLabel.brand = :brand')
            ->andWhere('rpo.rpo = substring(:rpo, 1, length(rpo.rpo))')
            ->setParameter('brand', $brand)
            ->setParameter('rpo', $rpo)
            ->orderBy('length(rpo.rpo)', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }
}