<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">
<suite name="Group Test Suite" preserve-order="true">
    <listeners>
        <listener class-name="listeners.CustomTestListener"/>
    </listeners>
    <!-- <test name="Run Brands Tests" >
        <groups>
            <run>
                <include name="Brands Check"/>
            </run>
        </groups>
        <classes>
            <class name="Brands_Check"/>
        </classes>
    </test>
    <test name="Feature Setting Tests" >
        <groups>
            <run>
                <include name="Feature Setting"/>
            </run>
        </groups>
        <classes>
            <class name="Feature_Setting"/>
        </classes>
    </test>


    <test name="Language Preferences Tests" >
        <groups>
            <run>
                <include name="Language Preferences"/>
            </run>
        </groups>
        <classes>
            <class name="Language_Preferences"/>
        </classes>
    </test>


    <test name="Reference Label Release Tests" >
        <groups>
            <run>
                <include name="Reference Label Release"/>
            </run>
        </groups>
        <classes>
            <class name="Reference_Label_Release"/>
        </classes>
    </test>

    <test name="Sanity Tests" >
        <groups>
            <run>
                <include name="Sanity Test"/>
            </run>
        </groups>
        <classes>
            <class name="SanityTest"/>
        </classes>
    </test> -->

<!--        <test name="Reference Label Management Tests" >-->
<!--            <groups>-->
<!--                <run>-->
<!--                    <include name="Reference Label Management"/>-->
<!--                </run>-->
<!--            </groups>-->
<!--            <classes>-->
<!--                <class name="Reference_Label_Management"/>-->
<!--            </classes>-->
<!--        </test>-->
<!--    <test name="Media Library Tests">-->
<!--        <groups>-->
<!--            <run>-->
<!--                <include name="Media Library"/>-->
<!--            </run>-->
<!--        </groups>-->
<!--        <classes>-->
<!--            <class name="Media_Library"/>-->
<!--        </classes>-->
<!--    </test>-->
</suite>
