import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.testng.annotations.Test;
import java.util.concurrent.TimeUnit;

public class Bulk_Operation{
    @Test(groups = {"Bulk Operation"})
    public void Bulk_Operation() throws InterruptedException {
        Methods user = new Methods();
        user.login();
        Locators.Administration().click();
        TimeUnit.SECONDS.sleep(1);
       Locators.Bulk_Operation().click();
       Locators.Bulk_Brand_Citroen().click();
        Locators.Bulk_Country_UK().click();
        Locators.Bulk_Channel_APP().click();
        Locators.Bulk_LocalLang_Eng().click();
        Locators.Bulk_Translation_Status().click();
        TimeUnit.SECONDS.sleep(10);
        Locators.Bulk_FirstRow_Checkbox().click();
        Locators.Bulk_Copy().click();
        Locators.Bulk_Apply().click();
        WebElement nextStep1Button = user.explicitWaitVisible(By.xpath("//button[@id='bulkCopyStep1Next']"));
        nextStep1Button.click();
        WebElement nextStep2Button = user.explicitWaitVisible(By.xpath("//button[@id='bulkCopyStep2Next']"));
        Locators.Bulk_Country_India().click();
        nextStep2Button.click();
        WebElement Finish = user.explicitWaitVisible(By.xpath("//button[@id='bulkCopyStep3Finish']"));
        Finish.click();
        user.exit();
    }
}

