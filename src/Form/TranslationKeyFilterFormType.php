<?php
namespace App\Form;

use App\Entity\Brand;
use App\Entity\Channel;
use App\Entity\Language;
use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class TranslationKeyFilterFormType extends AbstractType
{
    public function __construct(private TranslatorInterface $translator)
    {
        $this->translator = $translator;
    }

    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('languages', EntityType::class, [
                'class' => Language::class,
                'choice_label' => function ($language) {
                    return $this->translator->trans($language->getLabel());
                },
                'choice_value' => 'code',
                'label' => 'reference_language',
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('language')
                            ->where('language.isReference = :reference')
                            ->setParameter('reference', true);
                },
                'data' => 'en',
                'required' => false,
                'placeholder' => 'choose_language'
            ])
            ->add('brand', EntityType::class, [
                'class' => Brand::class,
                'choice_label' => 'name',
                'choice_value' => 'name',
                'label' => 'brand',
                'placeholder' => 'choose_brand',
                'required' => false,
            ])
            ->add('channel', EntityType::class, [
                'class' => Channel::class,
                'choice_label' => 'name',
                'choice_value' => 'name',
                'label' => 'channel',
                'placeholder' => 'choose_channel',
                'required' => false
            ])
            ->add('feature', TextType::class, ['label' => 'feature'])
            ->add('widget', TextType::class, ['label' => 'widget']);
    }

}