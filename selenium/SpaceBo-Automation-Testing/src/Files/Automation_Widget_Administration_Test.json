{"labels": ["WA_assistance_title", "WA_roadside_assistance_title", "WA_roadside_assistance_text"], "sources": ["WEB", "APP"], "features": [{"code": "roadside", "title": "Roadside assistance", "fields": [{"name": "phone", "type": "TextType", "options": {"label": "Phone", "required": false}, "section": "default", "translatable": false}, {"name": "availability", "type": "TextType", "options": {"label": "availability", "required": false}, "section": "default", "translatable": true}, {"name": "datetimeavailable", "type": "DateType", "options": {"label": "availability date", "required": false}, "section": "default", "translatable": true}], "enabled": true}, {"code": "brand", "title": "Brand assistance", "fields": [{"name": "faqurl", "type": "TextType", "options": {"label": "Faq url", "required": false}, "section": "Online contact", "translatable": true}, {"name": "contactformurl", "type": "TextType", "options": {"label": "Contact form url", "required": false}, "section": "Online contact", "translatable": true}, {"name": "phone", "type": "TextType", "options": {"label": "phone", "required": false}, "section": "Phone contact", "translatable": false}], "enabled": true}, {"code": "dealer", "title": "My dealer", "fields": [{"name": "rearchurl", "type": "TextType", "options": {"label": "Search dealer url", "required": false}, "section": "default", "translatable": false}], "enabled": true}], "widgetname": "brand_assistance_test2"}