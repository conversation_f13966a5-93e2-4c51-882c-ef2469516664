<?php

namespace App\Entity;

use App\Repository\RoleMenuRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Attribute\Groups;

#[ORM\Entity(repositoryClass: RoleMenuRepository::class)]
class RoleMenu
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer', name: 'id')]
    #[Groups(['package_export'])]
    private $id;

    #[ORM\Column(length: 255, nullable: true)]
    #[Groups(['package_export'])]
    private ?string $permission = null;

    #[ORM\ManyToOne(targetEntity: Role::class, inversedBy: 'roleMenus', cascade: ['persist'])]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['package_export'])]
    private ?Role $role;

    #[ORM\ManyToOne(targetEntity: Menu::class, inversedBy: 'roleMenus', cascade: ['persist'])]
    #[ORM\JoinColumn(nullable: false)]
    private Menu $menu;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getPermission(): ?string
    {
        return $this->permission;
    }

    public function setPermission(?string $permission): self
    {
        $this->permission = $permission;

        return $this;
    }

    public function getRole(): ?Role
    {
        return $this->role;
    }

    public function setRole(?Role $role): self
    {
        $this->role = $role;

        return $this;
    }

    public function getMenu(): Menu
    {
        return $this->menu;
    }

    public function setMenu(?Menu $menu): self
    {
        $this->menu = $menu;

        return $this;
    }
}