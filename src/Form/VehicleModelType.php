<?php

namespace App\Form;

use App\Document\VehicleModel;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints as Assert;

class VehicleModelType extends AbstractType
{
    const BRANDS = [
        'Citroën' => 'AC',
        'Peugeot' => 'AP',
        'DS' => 'DS',
        'Opel' => 'OP',
        'Vauxhall' => 'VX',
        'Global' => 'XX',
        'SPOTICAR' => 'SP',
        'FIAT' => 'FT',
        'FIAT PROFESSIONAL' => 'FO',
        'ABARTH' => 'AH',
        'ALFA ROMEO' => 'AR',
        'CHRYSLER' => 'CY',
        'DODGE' => 'DG',
        'JEEP' => 'JE',
        'LANCIA' => 'LA',
        'RAM' => 'RM',
        'MASERATI' => 'MA',
    ];
    
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $isSuperAdmin = $options['is_super_admin'];
        $builder
            // Label field
            ->add('label', TextType::class, [
                'label' => 'Label',
                'required' => true,
            ])
            // Brand field (pre-filled select input)
            ->add('brand', ChoiceType::class, [
                'label' => 'Brand',
                'choices' => self::BRANDS,
                'required' => true,
                'disabled' => !$isSuperAdmin,
            ])
            // LCDV field (multiple values, text area)
            ->add('lcdv', CollectionType::class, [
                'label' => 'LCDV',
                'entry_type' => TextType::class, 
                'allow_add' => true, 
                'allow_delete' => true, 
                'prototype' => true, 
                'by_reference' => false, 
                'required' => true,
                'mapped' => true,
                'constraints' => [
                    new Assert\NotBlank(['message' => 'At least one LCDV code is required.']),
                ],
            ])
            ->add('defaultImage', TextType::class, [
                'label' => false,
                'row_attr' => [
                    'section' => 'default',
                    'hidden' => true
                ],
                'attr' => [
                    'class' => 'd-none image-type-error'
                ],
                'required' => true
            ])
            // isO2x field (checkbox)
            ->add('isO2x', CheckboxType::class, [
                'label' => 'Is O2X?',
                'required' => false,
            ])
            // SDP field (select input)
            ->add('sdp', ChoiceType::class, [
                'label' => 'SDP',
                'choices' => [
                    'SSDP' => 'SSDP',
                    'GSDP' => 'GSDP',
                    'CVMP' => 'CVMP',
                ],
                'required' => true,
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => VehicleModel::class,
            'is_super_admin' => false, 
        ]);
    }
}
