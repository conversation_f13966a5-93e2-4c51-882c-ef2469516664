<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{% block title %}Static Page{% endblock %}</title>
    <!-- Font Awesome -->
		<link href="{{ asset('plugins/fontawesome-free/css/all.min.css') }}" rel="stylesheet" type="text/css">
		<!-- Bootstrap -->
		<link href="{{ asset('css/bootstrap.min.css') }}" rel="stylesheet" type="text/css">
		<!-- Select2 -->
		<link href="{{ asset('plugins/select2/css/select2.min.css') }}" rel="stylesheet" type="text/css">
		<link href="{{ asset('plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css') }}" rel="stylesheet" type="text/css">
		<!-- SPACE BO style -->
		<link href="{{ asset('css/space-bo.css') }}" rel="stylesheet" type="text/css">
		<link href="{{ asset(app.session.get('brandCssPathFilename')?? '') }}" rel="stylesheet" type="text/css">
		<link href="{{ asset('css/datatables/jquery.dataTables.min.css') }}" rel="stylesheet" type="text/css">
		<link rel="stylesheet" href="{{ asset('css/date-picker/flatpickr.min.css') }}" rel="stylesheet" type="text/css">
		<link rel="stylesheet" href="{{ asset('css/date-picker/theme.css') }}" rel="stylesheet" type="text/css">
    {% block stylesheets %}{% endblock %}
</head>
<body>
    <div class="mt-4">
        {% block body %}{% endblock %}
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {% block javascripts %}
         <script>
         	function setAction(actionValue) {
                    document.getElementById('action_input').value = actionValue;
                }
            document.addEventListener('DOMContentLoaded', function() {
                const copyButton = document.getElementById('copyPathBtn');
                
                if (copyButton) {
                    copyButton.addEventListener('click', function() {
                        const path = this.getAttribute('data-path');
                        const fullPath = path; // Construct full URL
                        
                        // Try modern clipboard API first
                        if (navigator.clipboard && window.isSecureContext) {
                            navigator.clipboard.writeText(fullPath).then(() => {
                                showSuccess();
                            }).catch((err) => {
                                console.error('Clipboard API failed: ', err);
                                fallbackCopyMethod(fullPath);
                            });
                        } else {
                            // Use fallback method for non-secure contexts or older browsers
                            fallbackCopyMethod(fullPath);
                        }
                        
                        function fallbackCopyMethod(text) {
                            // Create a temporary textarea element
                            const textArea = document.createElement('textarea');
                            textArea.value = text;
                            textArea.style.position = 'fixed';  // Avoid scrolling to bottom
                            textArea.style.top = '0';
                            textArea.style.left = '0';
                            textArea.style.width = '2em';
                            textArea.style.height = '2em';
                            textArea.style.padding = '0';
                            textArea.style.border = 'none';
                            textArea.style.outline = 'none';
                            textArea.style.boxShadow = 'none';
                            textArea.style.background = 'transparent';
                            
                            document.body.appendChild(textArea);
                            textArea.focus();
                            textArea.select();
                            
                            try {
                                const successful = document.execCommand('copy');
                                if (successful) {
                                    showSuccess();
                                } else {
                                    console.error('Fallback copy method failed');
                                    alert('Could not copy the path. Please try manually selecting and copying it.');
                                }
                            } catch (err) {
                                console.error('Fallback copy method error:', err);
                                alert('Could not copy the path. Please try manually selecting and copying it.');
                            }
                            
                            document.body.removeChild(textArea);
                        }
                        
                        function showSuccess() {
                            // Change button appearance temporarily
                            const originalHtml = copyButton.innerHTML;
                            copyButton.innerHTML = '<i class="fas fa-check me-2"></i> Copied!';
                            copyButton.classList.remove('btn-outline-primary');
                            copyButton.classList.add('btn-outline-success');
                            
                            // Revert after 2 seconds
                            setTimeout(() => {
                                copyButton.innerHTML = originalHtml;
                                copyButton.classList.remove('btn-outline-success');
                                copyButton.classList.add('btn-outline-primary');
                            }, 2000);
                        }
                    });
                }
            });
</script>
    {% endblock %}
</body>
</html>