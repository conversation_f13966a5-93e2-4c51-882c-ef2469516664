<?php

namespace App\Entity;

use App\Repository\FeatureSettingRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Serializer\Attribute\Groups;
use App\Validator\Constraints\UniqueName;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;

#[ORM\Entity(repositoryClass: FeatureSettingRepository::class)]
#[UniqueEntity(fields: ['name'], groups: ['create'])]
class FeatureSetting
{
    #[Groups(['package_export'])]
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[Groups(['package_export'])]
    #[ORM\Column(length: 255, nullable: true)]
    private ?string $file = null;

    #[Groups(['package_export'])]
    #[ORM\Column(length: 255)]
    #[UniqueName]
    private ?string $name = null;

    #[Groups(['package_export'])]
    #[ORM\Column(type: 'string', length: 50, nullable: true)]
    private ?string $brand;

    #[Groups(['package_export'])]
    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $country;

    #[Groups(['package_export'])]
    #[ORM\OneToMany(mappedBy: 'featureSetting', targetEntity: Feature::class, cascade: ['persist', 'merge', 'remove'])]
    private Collection $features;

    #[Groups(['package_export'])]
    #[ORM\OneToMany(mappedBy: 'feature', targetEntity: Menu::class, cascade: ['persist', 'remove'])]
    private Collection $menus;

    #[Groups(['package_export'])]
    #[ORM\Column(nullable: true)]
    private ?array $content = [];

    public function __construct()
    {
        $this->menus = new ArrayCollection();
        $this->features = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getFile(): ?string
    {
        return $this->file;
    }

    public function setFile(?string $file): self
    {
        $this->file = $file;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getBrand(): ?string
    {
        return $this->brand;
    }

    public function setBrand(?string $brand): self
    {
        $this->brand = $brand;

        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): self
    {
        $this->country = $country;

        return $this;
    }

    /**
     * @return Collection<int, Feature>
     */
    public function getFeatures(): Collection
    {
        return $this->features;
    }

    public function addFeature(Feature $feature): self
    {
        if (!$this->features->contains($feature)) {
            $this->features[] = $feature;
            $feature->setFeatureSetting($this);
        }
    
        return $this;
    }


    public function getMenus(): Collection
    {
        return $this->menus;
    }

    public function addMenu(?Menu $menu): self
    {
        if (!$this->menus->contains($menu)) {
            $this->menus->add($menu);
            $menu->setFeature($this);
        }

        return $this;
    }

    public function removeMenu(Menu $menu): self
    {
        if ($this->menus->removeElement($menu)) {
            // set the owning side to null (unless already changed)
            if ($menu->getFeature() === $this) {
                $menu->setFeature(null);
            }
        }

        return $this;
    }

    public function getContent(): ?array
    {
        if (empty($this->content)) {
            return [];
        }
        return $this->content;
    }

    public function setContent(?array $content): self
    {
        $this->content = $content;

        return $this;
    }
}