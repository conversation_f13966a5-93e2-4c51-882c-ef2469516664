<?php

namespace App\Service;

use App\Document\VehicleModel;
use App\Repository\BrandRepository;
use Symfony\Component\Serializer\SerializerInterface;

class VehicleModelManager
{
    const COLLECTION = "vehicleLabel";
    public function __construct(
        private BrandRepository $brandRepository,
        private MongoAtlasQueryService $mongoAtlasQueryService,
        private SerializerInterface $serializerInterface 
    ) {}

    public function save(VehicleModel $vehicleModel)
    {
        $vehicleModelArr = $this->serializerInterface->normalize($vehicleModel);
        $this->mongoAtlasQueryService->insertOne(self::COLLECTION, $vehicleModelArr);
    }
    public function update(VehicleModel $vehicleModel, string $id)
    {
        $vehicleModelArr = $this->serializerInterface->normalize($vehicleModel);
        $this->mongoAtlasQueryService->updateOne(self::COLLECTION, ['_id' => ['$oid' => $id]],$vehicleModelArr);
    }

    public function findDuplicateKeys(array $lcdv): array
    {
        $duplicate = $this->mongoAtlasQueryService->findDuplicateKeys(self::COLLECTION, $lcdv);
        return $duplicate;
    }

    public function getVehicleModel(array $brand): array
    {
        $filter = ['brand' => ['$in' => $brand]];
        $response = $this->mongoAtlasQueryService->find(self::COLLECTION, $filter);
        if ($response->getCode() == 200) {
            return json_decode($response->getData(), true)['documents'] ?? [];
        }
        return [];
    }

    public function findModel(string $id): VehicleModel
    {
        $response = $this->mongoAtlasQueryService->find(self::COLLECTION, ['_id' => ['$oid' => $id]]);
        $data = json_decode($response->getData(), true);
        $documentArray = $data['documents'][0];
        $vehicleModel = $this->serializerInterface->deserialize(
            json_encode($documentArray),
            VehicleModel::class,
            'json'
        );
        return $vehicleModel;
    }

    public function removeModel(string $id)
    {
        $response = $this->mongoAtlasQueryService->delete(self::COLLECTION,['_id' => ['$oid' => $id]]);
        if ($response->getCode() == 200) {
            return true;
        }
        return false;
    }
}
