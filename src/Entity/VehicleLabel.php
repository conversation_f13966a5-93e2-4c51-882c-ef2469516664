<?php

namespace App\Entity;

use App\Entity\LcdvLabel;
use App\Entity\PsaLanguage;
use App\Entity\RpoLabel;
use App\Repository\VehicleLabelRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: VehicleLabelRepository::class)]
#[ORM\Table(name: 'vehicle_label')]
class VehicleLabel
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'string', length: 255, name: 'label')]
    #[Assert\NotBlank(message: 'lcdvlabel_not_blank')]
    private ?string $label = null;

    #[ORM\Column(type: 'string', length: 2)]
    private ?string $brand = null;

    #[ORM\Column(type: 'datetime', name: 'created_at', options: ['default' => 'CURRENT_TIMESTAMP'])]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(type: 'datetime', nullable: true, name: 'updated_at')]
    private ?\DateTimeInterface $updatedAt = null;

    #[ORM\OneToMany(mappedBy: 'vehicleLabel', targetEntity: LcdvLabel::class, orphanRemoval: true)]
    private Collection $lcdvs;

    #[ORM\OneToMany(mappedBy: 'vehicleLabel', targetEntity: RpoLabel::class, orphanRemoval: true)]
    private Collection $rpos;

    #[ORM\ManyToOne(targetEntity: Language::class)]
    #[ORM\JoinColumn(name: 'vl_language_id', referencedColumnName: 'id', columnDefinition: 'INT NOT NULL DEFAULT 1')]
    private ?Language $language = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    #[Assert\Length(
        max: 24,
        maxMessage: 'LCDV Example cannot be longer than {{ limit }} characters'
    )]
    private ?string $lcdvExample = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $defaultImage = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $view3D = null;

    public function __construct()
    {
        $this->lcdvs = new ArrayCollection();
        $this->rpos = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }
    public function getLabel(): ?string
    {
        return $this->label;
    }
    public function setLabel(string $label): self
    {
        $this->label = $label;
        return $this;
    }
    public function getBrand(): ?string
    {
        return $this->brand;
    }
    public function setBrand(string $brand): self
    {
        $this->brand = $brand;
        return $this;
    }
    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }
    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }
    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }
    public function setUpdatedAt(?\DateTimeInterface $updatedAt): self
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }
    public function getLanguage(): ?Language
    {
        return $this->language;
    }
    public function setLanguage(?Language $language): void
    {
        $this->language = $language;
    }
    public function getLcdvExample(): ?string
    {
        return $this->lcdvExample;
    }
    public function setLcdvExample(?string $lcdvExample): self
    {
        $this->lcdvExample = $lcdvExample;
        return $this;
    }
    public function getDefaultImage(): ?string
    {
        return $this->defaultImage;
    }
    public function setDefaultImage(?string $defaultImage): self
    {
        $this->defaultImage = $defaultImage;
        return $this;
    }
    public function getView3D(): ?string
    {
        return $this->view3D;
    }
    public function setView3D(?string $view3D): self
    {
        $this->view3D = $view3D;
        return $this;
    }

    public function getLcdvs(): Collection
    {
        return $this->lcdvs;
    }
    public function addLcdv(LcdvLabel $lcdv): self
    {
        if (!$this->lcdvs->contains($lcdv)) {
            $this->lcdvs[] = $lcdv;
            $lcdv->setVehicleLabel($this);
        }
        return $this;
    }
    public function removeLcdv(LcdvLabel $lcdv): self
    {
        if ($this->lcdvs->removeElement($lcdv) && $lcdv->getVehicleLabel() === $this) {
            $lcdv->setVehicleLabel(null);
        }
        return $this;
    }
    public function getStringLcdvs(): string
    {
        $stringLcdv = '';
        $this->lcdvs->map(function (LcdvLabel $label) use (&$stringLcdv) {
            $stringLcdv .= $label->getLcdv() . "\r\n";
        });
        return $stringLcdv;
    }

    public function getRpos(): Collection
    {
        return $this->rpos;
    }
    public function addRpo(RpoLabel $rpo): self
    {
        if (!$this->rpos->contains($rpo)) {
            $this->rpos[] = $rpo;
            $rpo->setVehicleLabel($this);
        }
        return $this;
    }
    public function removeRpo(RpoLabel $rpo): self
    {
        if ($this->rpos->removeElement($rpo) && $rpo->getVehicleLabel() === $this) {
            $rpo->setVehicleLabel(null);
        }
        return $this;
    }
    public function getStringRpos(): string
    {
        $stringRpo = '';
        $this->rpos->map(function (RpoLabel $label) use (&$stringRpo) {
            $stringRpo .= $label->getRpo() . "\r\n";
        });
        return $stringRpo;
    }
}