<?php

namespace App\Repository;

use App\Entity\Language;
use App\Entity\LocaleVehicleLabel;
use App\Entity\PsaLanguage;
use App\Entity\PsaSite;
use App\Entity\Site;
use App\Entity\VehicleLabel;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\Persistence\ManagerRegistry as PersistenceManagerRegistry;

/**
 * @method VehicleLabel|null find($id, $lockMode = null, $lockVersion = null)
 * @method VehicleLabel|null findOneBy(array $criteria, array $orderBy = null)
 * @method VehicleLabel[]    findAll()
 * @method VehicleLabel[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class VehicleLabelRepository extends ServiceEntityRepository
{
    public function __construct(PersistenceManagerRegistry $registry)
    {
        parent::__construct($registry, VehicleLabel::class);
    }

    /**
     * @param PsaSite $site
     * @param PsaLanguage $language
     * @return mixed
     */
    public function getVehicleLabelsWithLanguages(?Site $site, Language $language) {
        return $this
            ->createQueryBuilder('vehicleLabel')
            ->select(['vehicleLabel as label', 'localeVehicleLabel.localLabel as localLabel', 'localeVehicleLabel.creationDate'])
            ->leftJoin(
                VehicleLabel::class, 'localeVehicleLabel', Join::WITH, 'localeVehicleLabel.vehicleLabel = vehicleLabel 
                and localeVehicleLabel.language = :language and localeVehicleLabel.site = :site'
            )
            ->where('vehicleLabel.brand = :brand')
            ->setParameter('site', $site)
            ->setParameter('language', $language)
            ->setParameter('brand', $site->getBrand())
            ->getQuery()
            ->getResult()
            ;
    }
    
    /**
     * find one model by lcdv and brand
     * @param string $brand 
     * @param string $lcdv 
     * @return VehicleLabel
     */
    public function findOneByBrandAndLcdv(string $brand, string $lcdv)
    {
        return 
        $this->createQueryBuilder('vehicleLabel')
        ->join('vehicleLabel.lcdvs', 'lcdv')
        ->where('vehicleLabel.brand = :brand')
        ->andWhere('lcdv.lcdv = substring(:lcdv,1,length(lcdv.lcdv))')
        ->setParameter('brand', $brand)
        ->setParameter('lcdv', $lcdv)
        ->orderBy('length(lcdv.lcdv)', 'DESC')
        
        ->setMaxResults(1)
        ->getQuery()
        ->getOneOrNullResult();
       
    }

    /**
     * find one model by rpo and brand
     * @param string $brand 
     * @param string $rpo 
     * @return VehicleLabel
     */
    public function findOneByBrandAndRpo(string $brand, string $rpo)
    {
        return 
        $this->createQueryBuilder('vehicleLabel')
        ->join('vehicleLabel.rpos', 'rpo')
        ->where('vehicleLabel.brand = :brand')
        ->andWhere('rpo.rpo = substring(:rpo,1,length(rpo.rpo))')
        ->setParameter('brand', $brand)
        ->setParameter('rpo', $rpo)
        ->orderBy('length(rpo.rpo)', 'DESC')
        
        ->setMaxResults(1)
        ->getQuery()
        ->getOneOrNullResult();
       
    }
}