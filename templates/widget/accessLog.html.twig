{% block content %}
  <div class="card card-primary widget-info mb-4 shadow-sm">
    <div class="card-header">
      <h3 class="card-title fs-4">
        <i class="fas fa-info-circle me-2"></i>
        {{ 'access_logs'|trans }}
      </h3>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-striped dataTable row-border table-hover" id="accessLogsTable" role="grid" aria-describedby="accessLogsTable_info" width="100%" cellspacing="0" style="font-size: 14px; width: 100%;">
          <thead>
            <tr>
              <th id="access_log_username">{{ 'username'|trans }}</th>
              <th id="access_log_channel">{{ 'channel'|trans }}</th>
              <th id="access_log_featureName">{{ 'featureName'|trans }}</th>
              <th id="access_log_langauge">{{ 'language'|trans }}</th>
              <th id="access_log_attributeName">{{ 'attributeNames'|trans }}</th>
              <th id="access_log_before">{{ 'before'|trans }}</th>
              <th id="access_log_after">{{ 'after'|trans }}</th>
              <th id="access_log_date">{{ 'updated_at'|trans }}</th>
            </tr>
          </thead>
          <tbody>
            {# DataTables will populate this dynamically #}
          </tbody>
        </table>
      </div>
    </div>
  </div>
{% endblock %}

{% block script %}

  <script src="{{ asset('js/jquery.min.js') }}"></script>
  <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>

	<script>
		$(document).ready(function() {
			let table = $('#accessLogsTable').DataTable({
				"language": {
					"search": "{{ 'datatable.search'|trans }}",
					"lengthMenu": "{{ 'datatable.length_menu'|trans }}",
					"info": "{{ 'datatable.showing'|trans }}",
					"infoEmpty": "{{ 'datatable.no_records'|trans }}",
					"zeroRecords": "{{ 'datatable.no_records'|trans }}",
					"paginate": {
						"next": '<i class="fa fa-chevron-right fa-lg"></i>',
						"previous": '<i class="fa fa-chevron-left fa-lg"></i>'
					}
				},
				"processing": true,
				"serverSide": false,
				"ajax": {
					"url": "{{ path('widget_management_paginate')}}", 
					"type": "GET",
					"data": function(d) {
						return $.extend({}, d, {
							"widget": "{{ widget.id }}",
							"brand": "{{ brand.id }}",
							"country": "{{ country.id }}"
						});
					},
					"dataSrc": function(json) {
						let flatData = [];
						if (!json || json.length === 0) return flatData; 
						Object.keys(json.data).forEach(timestamp => {
							Object.keys(json.data[timestamp]).forEach(featureName => {
								Object.keys(json.data[timestamp][featureName]).forEach(language => {
									Object.keys(json.data[timestamp][featureName][language]).forEach(channel => {
										const record = json.data[timestamp][featureName][language][channel];
										flatData.push({
											username: record.username,
											featureName: featureName,
											language: language, 
											channel: channel,
											before: record.before,
											after: record.after,
											timestamp: record.timestamp
										});
									});
								});
							});
						});
						return flatData;
					}
				},
				"pageLength": 5,
				"paging": true,
				"info": true,
				"searching": true,
				"responsive": true,
				"stateSave": true,
				"columns": [
					{ "data": "username" },
					{ "data": "channel" },
					{ "data": "featureName" },
					{ "data": "language" },
					{
						"data": null,
						"render": function(data, type, row) {
							const keys = Object.keys(row.before || row.after || {});
							if (!keys.length) return '';
					
							let attributes = `
								<div class="changed-attributes border rounded p-2" style="width: 220px; max-height: 150px; overflow-y: auto; background: #f8f9fa;">
									<ul class="list-unstyled mb-0 px-2">`;
							
							keys.forEach(key => {
								attributes += `<li class="py-1 border-bottom text-secondary" style="font-size: 14px;">
												<i class="fa fa-check-circle text-success me-1"></i> ${key}
											</li>`;
							});
					
							attributes += `</ul></div>`;
							return attributes;
						}
					},
					{ 
						"data": "before",
						"render": function(data, type, row) {
							if (!data) return '';

							let beforeValues = `<div class="data-object p-2 bg-light rounded border" 
													style="width: 200px; max-height: 150px; overflow-y: auto; white-space: nowrap; text-overflow: ellipsis;">`;

							Object.entries(data).forEach(([key, value]) => {
								const displayValue = value === false ? 'false' : (value === true ? 'true' : (value || ''));
								beforeValues += `<div class="data-row mb-1">
													<span class="fw-bold text-secondary">${key}:</span> 
													<span class="ms-1">${displayValue}</span>
												</div>`;
							});

							beforeValues += '</div>';
							return beforeValues;
						}
					},
					{ 
						"data": "after",
						"render": function(data, type, row) {
							if (!data) return '';

							let afterValues = `<div class="data-object p-2 bg-light rounded border" 
													style="width: 200px; max-height: 150px; overflow-y: auto; white-space: nowrap; text-overflow: ellipsis;">`;

							Object.entries(data).forEach(([key, value]) => {
								const displayValue = value === false ? 'false' : (value === true ? 'true' : (value || ''));
								afterValues += `<div class="data-row mb-1">
													<span class="fw-bold text-secondary">${key}:</span> 
													<span class="ms-1">${displayValue}</span>
												</div>`;
							});

							afterValues += '</div>';
							return afterValues;
						}
					},
					{ 
						"data": "timestamp",
						"render": function(data, type, row) {
							if (type === 'display') {
								return new Date(data).toLocaleString();
							}
							return data;
						}
					}
				],
				"order": [[5, "desc"]], // Sort by timestamp by default
				"autoWidth": false,
				"columnDefs": [
					{ "width": "5%", "targets": 0 },
					{ "width": "3%", "targets": 1 },
					{ "width": "14%", "targets": 2 },
					{ "width": "8%", "targets": 3 },
					{ "width": "20%", "targets": 4 },
					{ "width": "20%", "targets": 5 },
					{ "width": "20%", "targets": 6 },
					{ "width": "10%", "targets": 7 },
				]
			});

			table.on('draw', function () {
				$(".truncate").each(function() {
					if ($(this).text().trim().length > 100) {
						let text = $(this).text().trim().substring(0, 100) + '...';
						$(this).html(text);
					}
				});
			});
		});
	</script>
{% endblock %}
