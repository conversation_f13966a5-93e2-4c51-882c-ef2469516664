{% extends '_layout/layout.html.twig' %}

{% block title %}
	{{ 'owners_manual' | trans | capitalize }}
{% endblock %}

{% block content %}
	<div class="card">
		{% include '_layout/form_card_header.html.twig' with {
            'title': 'owners_manual' | trans,
            'new_label': 'Create',
            'new_url': path('owners_manual_create'),
        } %}

		<div class="card-body">
			<div class="row mb-3">
				<div class="col-md-3 form-group p-2" id="search_name">
					<label for="name">{{ 'vehicleModelLabel'|trans }}</label>
					<input type="text" id="name" class="form-control" placeholder="{{ 'search_label'|trans }}">
				</div>
				<div class="col-md-3 form-group p-2" id="search_brand">
					<label for="brand">{{ 'brand'|trans }}</label>
					<input type="text" id="brand" class="form-control" placeholder="{{ 'search_brand'|trans }}">
				</div>
			</div>

			<table class="table table-striped table-bordered dataTable dtr-inline" id="model_table">
				<thead>
					<tr>
						<th id="name_column">{{ 'vehicleModelLabel' | trans }}</th>
						<th id="brand_column">{{ 'brand' | trans }}</th>
						<th id="lcdv_code_column">{{ 'lcdv_code' | trans }}</th>
						<th id="sdp_column">{{ 'sdp' | trans }}</th>
						<th id="updateAt_column">{{ 'updateAt' | trans }}</th>
						<th class="actions">{{ 'actions' | trans }}</th>
					</tr>
				</thead>
				<tbody>
					{% for model in models %}
						<tr>
							<td>{{ model.label | default('N/A') }}</td>
							<td>{{ model.brand | default('N/A') }}</td>
							<td>{{ model.lcdv is iterable ? model.lcdv|join(', ') : model.lcdv | default('N/A') }}</td>
							<td>{{ model.sdp | default('N/A') }}</td>
							<td>{{ model.updateAt | default('N/A') | date('Y-m-d H:i:s') }}</td>
							<td>
								<div class="btn-group">
									<div class="btn-wrapper mx-1">
										<a href="{{ path('owners_manual_edit', {'id': model._id}) }}" class="btn btn-warning" data-toggle="tooltip" title="{{ 'edit'|trans }}">
											<i class="fas fa-edit"></i>
										</a>
                                        <a href="#delete-vehicle-modal" class="btn btn-danger" data-vehicle-model-id="{{ model._id }}" data-toggle="tooltip" title="{{ 'edit'|trans }}">
											<i class="fas fa-trash"></i>
										</a>
									</div>
								</div>
							</td>
						</tr>
					{% endfor %}
				</tbody>
			</table>
		</div>
	</div>
    {{ include('owners_manual/_delete_modal.html.twig') }}
{% endblock %}

{% block script %}
	{% include "owners_manual/_dt_js.html.twig" %}
    {% include "owners_manual/_delete_modal_js.html.twig" %}
{% endblock %}
