<?php

namespace App\Entity;

use App\Repository\RpoLabelRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: RpoLabelRepository::class)]
#[ORM\Table(name: 'rpo_label')]
#[UniqueEntity(
    fields: ['rpo', 'vehicleLabel'],
    repositoryMethod: 'findOneRpoLabelByBrand',
    message: 'rpolabel_rpo_unique',
    ignoreNull: true
)]
class RpoLabel
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'string', length: 32, nullable: true)]
    #[Assert\Type(type: 'alnum', message: 'rpolabel_rpo_notvalid')]
    #[Assert\Length(
        max: 32,
        maxMessage: 'rpolabel_rpo_maxmessage',
        min: 2,
        minMessage: 'rpolabel_rpo_minmessage'
    )]
    private ?string $rpo = null;

    #[ORM\Column(type: 'datetime', name: 'created_at', options: ['default' => 'CURRENT_TIMESTAMP'])]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\ManyToOne(targetEntity: VehicleLabel::class, inversedBy: 'rpos', fetch: 'EAGER')]
    #[ORM\JoinColumn(nullable: false)]
    private ?VehicleLabel $vehicleLabel = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getRpo(): ?string
    {
        return $this->rpo;
    }

    public function setRpo(string $rpo): self
    {
        $this->rpo = $rpo;
        return $this;
    }

    public function getVehicleLabel(): ?VehicleLabel
    {
        return $this->vehicleLabel;
    }

    public function setVehicleLabel(?VehicleLabel $vehicleLabel): self
    {
        $this->vehicleLabel = $vehicleLabel;
        return $this;
    }
}