<?php

namespace App\Repository;

use App\Entity\Widget;
use App\Entity\WidgetFeatureAttribute;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<WidgetFeatureAttribute>
 *
 * @method WidgetFeatureAttribute|null find($id, $lockMode = null, $lockVersion = null)
 * @method WidgetFeatureAttribute|null findOneBy(array $criteria, array $orderBy = null)
 * @method WidgetFeatureAttribute[]    findAll()
 * @method WidgetFeatureAttribute[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class WidgetFeatureAttributeRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, WidgetFeatureAttribute::class);
    }

    public function save(WidgetFeatureAttribute $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(WidgetFeatureAttribute $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
    public function getIds(Widget $widget): array
    {
        $result = $this->createQueryBuilder('wfa')
            ->select('wfa.id')
            ->leftJoin('wfa.widgetFeature', 'wf')
            ->where('wf.widget = :widget')
            ->setParameter('widget', $widget->getId())
            ->getQuery()
            ->getScalarResult();

        return array_column($result, 'id');
    }
}
