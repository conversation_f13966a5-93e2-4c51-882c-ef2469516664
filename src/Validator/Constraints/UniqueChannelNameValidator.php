<?php

declare(strict_types=1);

namespace App\Validator\Constraints;

use App\Repository\ChannelRepository;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;

class UniqueChannelNameValidator extends ConstraintValidator
{
    public function __construct(
        private ChannelRepository $channelRepository
    ) {
    }

    public function validate($value, Constraint $constraint): void
    {
        if (!$constraint instanceof UniqueChannelName) {
            throw new UnexpectedTypeException($constraint, UniqueChannelName::class);
        }

        if (null === $value || '' === $value) {
            return;
        }

        $entity = $this->context->getRoot()->getData();
        $existingChannel = $this->channelRepository->findOneBy(['name' => $value]);

        // If no channel exists with this name, validation passes
        if (!$existingChannel) {
            return;
        }

        // If we're updating an existing channel and the name hasn't changed, validation passes
        if ($entity && method_exists($entity, 'getId') && $existingChannel->getId() === $entity->getId()) {
            return;
        }

        $this->context->buildViolation($constraint->message)
            ->setParameter('{{ value }}', $value)
            ->addViolation();
    }
}