<?php

namespace App\Entity;

use App\Repository\LcdvLabelRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: LcdvLabelRepository::class)]
#[ORM\Table(name: 'lcdv_label')]
#[UniqueEntity(
    fields: ['lcdv', 'vehicleLabel'],
    repositoryMethod: 'findOneLcdvLabelByBrand',
    message: 'lcdvlabel_lcdv_unique',
    ignoreNull: true
)]
class LcdvLabel
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'string', length: 32, nullable: true)]
    #[Assert\Type(type: 'alnum', message: 'lcdvlabel_lcdv_notvalid')]
    #[Assert\Length(
        max: 32,
        maxMessage: 'lcdvlabel_lcdv_maxmessage',
        min: 2,
        minMessage: 'lcdvlabel_lcdv_maxmessage'
    )]
    private ?string $lcdv = null;

    #[ORM\Column(type: 'datetime', name: 'created_at', options: ['default' => 'CURRENT_TIMESTAMP'])]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\ManyToOne(targetEntity: VehicleLabel::class, inversedBy: 'lcdvs', fetch: 'EAGER')]
    #[ORM\JoinColumn(nullable: false)]
    private ?VehicleLabel $vehicleLabel = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getLcdv(): ?string
    {
        return $this->lcdv;
    }

    public function setLcdv(string $lcdv): self
    {
        $this->lcdv = $lcdv;
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getVehicleLabel(): ?VehicleLabel
    {
        return $this->vehicleLabel;
    }

    public function setVehicleLabel(?VehicleLabel $vehicleLabel): self
    {
        $this->vehicleLabel = $vehicleLabel;
        return $this;
    }
}