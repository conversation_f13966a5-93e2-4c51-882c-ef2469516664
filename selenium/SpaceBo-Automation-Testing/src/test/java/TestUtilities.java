import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
public class TestUtilities {
    private static final String CONFIG_PATH = "src/Configuration/config.properties";
    private static final String Login_CONFIG_PATH = "src/Configuration/Logindetails.properties";
    private static final ConfigReader  configReader;
    private static WebDriver driver;

    // Static block to initialize configReader when the class is loaded
    static {
        configReader = new ConfigReader(CONFIG_PATH,Login_CONFIG_PATH);
    }

    // Method to get configReader instance
    public static ConfigReader getConfigReader() {
        return configReader;
    }

    // Method to initialize WebDriver (Chrome in this case)
    public static WebDriver getDriver() {
        if (driver == null) {
            // Set the system property for ChromeDriver
            System.setProperty(configReader.getProperty("chrome"), configReader.getProperty("chrome_driver_path"));
            ChromeOptions options = new ChromeOptions();
            options.addArguments("--incognito");
            // Enable headless mode based on configuration property
            String headless = configReader.getProperty("headless");
            if ("true".equalsIgnoreCase(headless)) {
                options.addArguments("--headless");
                options.addArguments("--disable-gpu"); // For compatibility with certain CI environments
                options.addArguments("--no-sandbox"); // Recommended for CI environments
                options.addArguments("--disable-dev-shm-usage"); // Overcomes limited resource problems
            }
            driver = new ChromeDriver(options);
        }
        return driver;
    }
    // Method to close the WebDriver
    public static void quitDriver() {
        if (driver != null) {
            driver.quit();
            driver = null; // Set to null after quitting
        }
    }
}
