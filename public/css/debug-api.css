/* Debug API Styles */

/* Vehicle Card Styling */
.vehicle-card {
    border: 1px solid #e3e6f0;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 15px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
    min-height: 120px;
}

.vehicle-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.vehicle-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.vehicle-card:hover::before {
    transform: scaleX(1);
}

.vehicle-card.selected {
    border: 3px solid #28a745 !important;
    background: linear-gradient(135deg, #f0fff4 0%, #e6ffed 100%) !important;
    box-shadow: 0 12px 40px rgba(40, 167, 69, 0.4) !important;
    transform: translateY(-5px) scale(1.02) !important;
}

.vehicle-card.selected::before {
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%) !important;
    transform: scaleX(1) !important;
    height: 6px !important;
}

.vehicle-card.selected .vehicle-title {
    font-weight: 700 !important;
    color: #155724 !important;
    font-size: 1.15rem !important;
}

.vehicle-card.selected .vehicle-meta {
    font-weight: 600 !important;
    color: #2d5a3d !important;
}

.vehicle-card.selected .vehicle-meta strong {
    color: #155724 !important;
}

.vehicle-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.vehicle-details {
    flex: 1;
}

.vehicle-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 12px;
    line-height: 1.3;
    transition: all 0.3s ease;
}

.vehicle-meta {
    font-size: 0.85rem;
    color: #6c757d;
    line-height: 1.6;
    transition: all 0.3s ease;
}

.vehicle-meta strong {
    color: #495057;
    font-weight: 600;
}

.vehicle-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
}

.brand-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.vehicle-card.selected .brand-badge {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.selection-indicator {
    color: #28a745;
    font-size: 1.5rem;
    opacity: 0;
    transform: scale(0.5);
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.vehicle-card.selected .selection-indicator {
    opacity: 1;
    transform: scale(1);
}

/* Response Card Styling */
.response-card {
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    overflow: hidden;
}

.response-header {
    background: linear-gradient(135deg, #010954 0%, #167fe8 100%);
    color: white;
    padding: 15px 25px;
    border: none;
    font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
}

.response-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.response-label {
    font-weight: 600;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.response-status {
    background: #27ae60;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
}

.response-controls .btn {
    border-color: rgba(255,255,255,0.3);
    color: white;
    margin-left: 2px;
}

.response-controls .btn:hover {
    background-color: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.5);
    color: white;
}

.response-body {
    background: #f8f9fc;
    padding: 25px;
}

/* Response Metadata */
.response-metadata {
    background: white;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e3e6f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.metadata-item {
    text-align: center;
}

.metadata-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
    margin-bottom: 5px;
}

.metadata-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2c3e50;
    font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
}

.status-success {
    color: #27ae60 !important;
}

/* JSON Output Section */
.json-output-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e3e6f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.section-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #ecf0f1;
}

.json-output-container {
    background: #fafbfc;
    border: 1px solid #e1e8ed;
    border-radius: 6px;
    padding: 15px;
    max-height: 600px;
    overflow: auto;
}

/* JSON Viewer Styling */
json-viewer {
    display: block;
    width: 100%;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Consolas', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
}

/* Custom scrollbar */
.json-output-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.json-output-container::-webkit-scrollbar-track {
    background: #f1f3f4;
    border-radius: 4px;
}

.json-output-container::-webkit-scrollbar-thumb {
    background: #c1c8cd;
    border-radius: 4px;
}

.json-output-container::-webkit-scrollbar-thumb:hover {
    background: #a8b2ba;
}
