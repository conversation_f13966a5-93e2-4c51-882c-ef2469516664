import org.openqa.selenium.Keys;
import org.openqa.selenium.WebElement;
import org.testng.annotations.Test;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class Reference_Label_Release {
    @Test(groups = {"Reference Label Release"})
    public void Label_Creation() throws InterruptedException {
        Methods user = new Methods();
       user.login();
        Locators.Administration().click();
        TimeUnit.SECONDS.sleep(1);
        Locators.Reference_Label_Release().click();
        for (int i = 0; i < 10; i++) {
            List<WebElement> release = Locators.Reference_Release();
            user.Scroll_Down();
            TimeUnit.SECONDS.sleep(3);
            release.get(i).click();
            TimeUnit.SECONDS.sleep(3);
        }
        Locators.Reference_Release_Btn().click();
        Locators.Reference_Right_Move_Btn().click();
        Locators.Settings_Release().click();
        Locators.Settings_Release_Source().sendKeys("APP", Keys.ENTER);
        Locators.Settings_Release_Source().sendKeys("WEB", Keys.ENTER);
        Locators.Settings_Release_Btn().click();
       user.exit();
    }
}
