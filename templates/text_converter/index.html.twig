{% extends '_layout/layout.html.twig' %}

{% block title %}
	{{ 'text_converter' | trans | capitalize }}
{% endblock %}

{% block content %}
	<div class="card">

		{% include '_layout/form_card_header.html.twig' with{
            'title': 'Static Pages' | trans,
			'new_label': 'New Page' | trans,
            'new_url': path('static_create'),
     }%}
		<div class="card-body">
			{# <div class="row">
				<div class="col-md-3 form-group" id="search_name">
					<label for="name">{{ 'name'|trans }}</label>
				</div>
				<div class="col-md-3 form-group" id="search_channel_type_name">
					<label for="channel_type_name">{{ 'channel_type_name'|trans }}</label>
				</div>
			</div> #}
			<table class="table table table-striped dataTable dtr-inline" id="pages_table">
				<thead>
					<th id="channel_column">{{ 'channel' | trans }}</th>
					<th id="title_column">{{ 'title' | trans }}</th>
					<th id="language_column">{{ 'language' | trans }}</th>
					<th id="status_column">{{ 'status' | trans }}</th>
					<th class='actions'>{{'actions' | trans}}</th>
				</thead>
				<tbody>
					{% for page in pages %}
						<tr>
							<td>{{page.channel.name}}</td>
							<td>{{page.getPageTitle()}}</td>
							<td>{{page.language.label ?? '-'}}</td>
							<td>{{page.status.name}}</td>
							<td>
								<div class="btn-group">
									<div class="btn-wrapper mx-2">
										<a href="{{ path('static_edit', {'id': page.id}) }}" class="btn btn-warning">
												<i class="fas fa-edit"></i>
                                                Edit
											</a>
									</div>
                                    <div class="btn-wrapper mx-2">
										<a href="{{ path('static_preview', {'id': page.id}) }}" class="btn btn-primary" data-toggle="tooltip">
											<i class="fas fa-eye"></i>
                                            View
										</a>
									</div>
                                    <div class="btn-wrapper mx-2">
										<a href="{{ path('static_publish', {'id': page.id}) }}" class="btn btn-success" data-toggle="tooltip">
											<i class="fas fa-paper-plane"></i>
                                            Publish
										</a>
									</div>
									<div class="btn-wrapper mx-2">
										<a href="#"
										class="btn btn-info open-preview"
										data-channel="{{ page.channel.name }}"
										data-brand="{{ page.brand.code }}"
										data-country="{{ page.country.code }}"
										data-title="{{ page.getPageTitle()|url_encode }}"
										data-language="{{ page.language.code ?? '' }}"
										target="_blank"
										data-toggle="tooltip">
											<i class="fas fa-external-link-alt"></i>
											Live Preview
										</a>
									</div>
									<div class="btn-wrapper mx-2">
										<button class="btn btn-outline-secondary copy-link-btn" data-id="{{ page.id }}" title="Copy link to clipboard">
											<i class="fas fa-copy"></i> Copy Link
										</button>
									</div>
								</div>
							</td>
						</tr>
					{% endfor %}
				</tbody>
			</table>
		</div>
	</div>
</div>
{% endblock %}
{% block script %}
    {% include "channel/_dt_js.html.twig" %}
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            document.querySelectorAll('.open-preview').forEach(function (button) {
                button.addEventListener('click', function (e) {
                    e.preventDefault();

                    const baseUrl = window.location.origin;
                    const channel = this.dataset.channel;
                    const brand = this.dataset.brand;
                    const country = this.dataset.country;
                    const title = this.dataset.title;
                    const language = this.dataset.language;

                    let url = `${baseUrl}/page/${channel}/${brand}/${country}/${title}`;
                    if (language) {
                        url += `/${language}`;
                    }

                    window.open(url, '_blank');
                });
            });

			 document.querySelectorAll('.copy-link-btn').forEach(button => {
				button.addEventListener('click', function () {
					const pageId = this.getAttribute('data-id');
					const originalText = this.innerHTML;
					const thisButton = this;
					thisButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Copying...';
					thisButton.disabled = true;

					const fetchUrl = `/static-page/copy/${pageId}`;
					const headers = {
						'X-Requested-With': 'XMLHttpRequest'
					};
					
					fetch(fetchUrl, {
						headers: headers,
						credentials: 'same-origin'
					})
						.then(response => {
							console.log('Server response status:', response.status);
							if (!response.ok) {
								throw new Error(`Server returned status code ${response.status}`);
							}
							return response.json();
						})
						.then(data => {
							console.log('Server response data:', data);
							
							if (!data || !data.url) {
								console.error('Response missing URL:', data);
								throw new Error('URL not provided in response');
							}
							
							function copyTextToClipboard(text) {
								return new Promise((resolve, reject) => {
									if (navigator.clipboard && navigator.clipboard.writeText) {
										navigator.clipboard.writeText(text)
											.then(resolve)
											.catch(error => {
												console.warn('Clipboard API failed, trying fallback', error);
												fallbackCopyTextToClipboard(text, resolve, reject);
											});
									} else {
										fallbackCopyTextToClipboard(text, resolve, reject);
									}
								});
							}
							
							function fallbackCopyTextToClipboard(text, resolve, reject) {
								try {
									const textArea = document.createElement('textarea');
									textArea.value = text;
									
									textArea.style.position = 'fixed';
									textArea.style.left = '-999999px';
									textArea.style.top = '-999999px';
									document.body.appendChild(textArea);
									
									// Focus and select the text
									textArea.focus();
									textArea.select();
									
									const successful = document.execCommand('copy');
									document.body.removeChild(textArea);
									
									if (successful) {
										resolve();
									} else {
										reject(new Error('execCommand copy failed'));
									}
								} catch (err) {
									reject(err);
								}
							}
							
							return copyTextToClipboard(data.url)
								.then(() => {
									thisButton.innerHTML = '<i class="fas fa-check"></i> Copied!';
									setTimeout(() => {
										thisButton.innerHTML = originalText;
										thisButton.disabled = false;
									}, 2000);
								});
						})
						.catch(error => {
							console.error('Error in copy process:', error);
							thisButton.innerHTML = originalText;
							thisButton.disabled = false;
							alert('Error copying link: ' + (error.message || 'Unknown error'));
						});
				});
			});
        });
    </script>
{% endblock %}


