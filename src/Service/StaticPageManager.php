<?php

namespace App\Service;

use App\Entity\Media;
use App\Entity\MediaDirectory;
use App\Entity\Profile;
use App\Entity\Site;
use App\Entity\StaticPage;
use App\Enum\StaticPageStatus;
use App\Service\AWSS3Service;
use Doctrine\ORM\EntityManagerInterface;

class StaticPageManager
{
    public function __construct(
        private EntityManagerInterface $em,
        private AWSS3Service $s3Service,
        private string $publishBucket,
        private string $baseUrl,
        private string $mediaBucket,
        private string $mediaUrl
    ) {
        $this->em = $em;
    }

    public function savePage(StaticPage $page, string $content, string $path)
    {
        $fileName = 'static_pages' . '/' . $path;
        $file_res = $this->s3Service->putObject($fileName, $content, $this->publishBucket);
        if ($file_res) {
            $this->em->persist($page);
            $this->em->flush();
            return true;
        }
        return false;
    }

    public function getHTML(StaticPage $page)
    {
        $path =  $this->getPath($page);
        $fileUrl = $this->baseUrl . '/' . 'static_pages' . '/' . $path;
        $content = @file_get_contents($fileUrl);
        return $content;
    }

    public function getPublishedHTML(StaticPage $page)
    {
        $path =  $this->getPath($page);
        $fileUrl = $this->baseUrl . '/' . 'static_pages' . '/' . $path;
        $content = @file_get_contents($fileUrl);
        return $content;
    }

    public function getPath(StaticPage $page)
    {
        $path = strtolower($page->getChannel()->getName()) . '/' .
            strtolower($page->getBrand()->getCode()) . '/' .
            strtolower($page->getCountry()->getCode());

        if ($page->getLanguage()) {
            $path .= '/' . strtolower($page->getLanguage()->getCode());
        }
        $path .= '/' . $page->getPageTitle() . '.html';
        return $path;
    }

    public function getMediaPath(StaticPage $page)
    {
        $path = 'uploads' . '/' . strtoupper($page->getChannel()->getName()) . '/' .
            strtoupper($page->getBrand()->getCode()) . '/' .
            $page->getCountry()->getCode();
        if ($page->getLanguage()) {
            $path .= '/' . strtolower($page->getLanguage()->getCode()). '/' . $page->getPageTitle() . '.html';
        } else {
            $path .= '/' . $page->getPageTitle() . '.html';
        }
        return $path;
    }

    public function getMediaLink(StaticPage $page)
    {
        $path = $this->getMediaPath($page);
        $fileLink = $this->mediaUrl . '/' . $path;
        return $fileLink;
    }
    
    public function publishPage(StaticPage $page, string $content, string $createdBy, Profile $profile)
    {
        $mediaPath = $this->getMediaPath($page);
        $settingsPath = $this->getPath($page);
        $mediaSync = $this->syncMediaLibrary($page, $createdBy, $profile);
        if ($mediaSync) {
            $fileName = 'static_pages' . '/' . $settingsPath;
            $settings_res = $this->s3Service->putObject($fileName, $content, $this->publishBucket, 'text/html');
            $media_res = $this->s3Service->putObject($mediaPath, $content, $this->mediaBucket, 'text/html');
            if ($media_res) {
                $page->setStatus(StaticPageStatus::PUBLISHED);
                $this->em->persist($page);
                $this->em->flush();
                return true;
            }
        }
        return false;
    }

    public function syncMediaLibrary(StaticPage $page, string $createdBy, Profile $profile)
    {
        try {
            $path = $this->getMediaPath($page);
            $channelDirectory = $this->em->getRepository(MediaDirectory::class)->findOneBy(['name' => $page->getChannel()->getName()]);
            $brandDirectory = $this->em->getRepository(MediaDirectory::class)->findOneBy(['parentDirectory' => $channelDirectory, 'name' => $page->getBrand()->getName()]);
            $countryDirectory = $this->em->getRepository(MediaDirectory::class)->findOneBy(['parentDirectory' => $brandDirectory, 'name' => $page->getCountry()->getName()]);
            $site = $this->em->getRepository(Site::class)->findOneBy(['brand' => $page->getBrand(), 'country' => $page->getCountry()]);
            $parent = $countryDirectory;
            if ($page->getLanguage()) {
                $languageDirectory = $this->em->getRepository(MediaDirectory::class)->findOneBy(['parentDirectory' => $countryDirectory, 'name' => $page->getLanguage()->getCode()]);
                if (!$languageDirectory) {
                    $path = $countryDirectory->getPath() . ' > ' . $page->getLanguage()->getCode();
                    $languageDirectory = new MediaDirectory();
                    $languageDirectory->setParentDirectory($countryDirectory);
                    $languageDirectory->setSite($site);
                    $languageDirectory->setReadOnly(0);
                    $languageDirectory->setChannel($page->getChannel());
                    $languageDirectory->setBrand(strtoupper($page->getBrand()->getCode()));
                    $languageDirectory->setPath($path);
                    $languageDirectory->setName($page->getLanguage()->getCode());
                    $languageDirectory->setLabel($page->getLanguage()->getCode());
                    $this->em->persist($languageDirectory);
                }
                $parent = $languageDirectory;
            }
            $mediaObj = $this->em->getRepository(Media::class)->findOneBy(['name' => $page->getPageTitle(), 'site' => $site, 'directory' => $parent]);
            if (!$mediaObj) {
                $mediaObj = new Media();
                $mediaObj->setSite($site);
                $mediaObj->setDirectory($parent);
                $mediaObj->setName($page->getPageTitle());
                $mediaObj->setExtension('html');
                $mediaObj->setPath($this->getMediaPath($page));
                $mediaObj->setSize(1024);
                $mediaObj->setCreatedBy($createdBy);
                $mediaObj->setProfile($profile);
            }
            $mediaObj->setLastUpdate(new \DateTime('now'));
            $this->em->persist($mediaObj);
            $this->em->flush();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
}
