<?php
namespace App\Service;

use App\Helpers\LoggerTrait;
use Aws\CloudFront\CloudFrontClient;
use Exception;

/**
 * AWSCloudFrontService
 */
class AWSCloudFrontService
{
	use LoggerTrait;
	function __construct(private AwsSdkService $awsSdkService)
	{}

	public function invalidateCloudFront(string $distributionId, array $pathsToInvalidate = []): string|bool
	{
		try {
			$cloudFront = $this->getCloudFrontClient();
			$result = $cloudFront->createInvalidation([
				'DistributionId' => $distributionId,
				'InvalidationBatch' => [
					'Paths' => [
						'Quantity' => count($pathsToInvalidate),
						'Items' => $pathsToInvalidate,
					],
					'CallerReference' => uniqid(), // Unique string for each invalidation request
				],
			]);			

			return $result['Invalidation']['Id'] ?? '';
		} catch (Exception $e) {
			$this->logger->error('[AWSS3Service] error while invalidating '.$e->getMessage());
			return false;
		}
	}

	private function getCloudFrontClient(): CloudFrontClient
	{
		return $this->awsSdkService->getCloudFrontClient();
	}
	

}
