<?php

declare(strict_types=1);

namespace App\Helpers;

/**
 * Class RoleHelper.
 */
class RoleHelper
{
     public const LOCAL_CENTRAL_ROLES = [
        ...self::CENTRAL_ROLES, ...self::LOCAL_ROLES
    ];

    public const ROLES = [
        'local_technical_admin' => 'local_technical_admin',
        'operations' => 'operations',
        'webmaster' => 'webmaster',
        'reader' => 'reader',
        'super_admin' => 'super_admin',
        'technical_admin' => 'technical_admin',
        'functional_admin' => 'functional_admin'
    ];
    
    public const GLOBAL_ROLES = [
        'super_admin',
    ];

    public const CENTRAL_ROLES = [
        'technical_admin',
        'functional_admin'
    ];

    public const LOCAL_ROLES = [
        'local_technical_admin',
        'operations',
        'webmaster',
        'reader'
    ];

    public const GLOBAL_CENTRAL_ROLES = [
        ...self::GLOBAL_ROLES, ...self::CENTRAL_ROLES
    ];
    
    public static function isLocalProfile(?string $role):bool
    {
        return in_array($role, self::LOCAL_ROLES);
    }
    public static function isSuperAdmin(?string $role):bool
    {
        return $role == self::ROLES['super_admin'] ?? false;
    }

    public static function isLocalCentralProfile(?string $role):bool
    {
        return in_array($role, self::LOCAL_CENTRAL_ROLES);
    }
}
