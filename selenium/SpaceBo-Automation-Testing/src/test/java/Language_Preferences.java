import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.Select;
import org.testng.annotations.Test;

import java.util.List;

public class Language_Preferences {
    @Test(groups = {"Language Preferences"})
    public void language() {
        Methods user = new Methods();
       user.login();
        Locators.User_Profile().click();
        Locators.Settings_Profile().click();
        Locators.User_Prefernces().click();
        List<WebElement> options = Locators.Language_Options();
        for (int i = 0; i < options.size(); i++) {
            Select select = new Select(Locators.Language_Select());
            select.selectByIndex(i);
            Locators.Language_Save().click();
            Locators.User_Profile().click();
            Locators.Settings_Profile().click();
            Locators.User_Prefernces().click();
        }
        Locators.Profile_exit_Btn().click();
       user.exit();
    }

}
