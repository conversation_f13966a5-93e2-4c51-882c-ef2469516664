{% extends '_layout/layout.html.twig' %}

{% block content %}
    <div class="container-fluid">
        <h1 class="h3 mb-4 text-gray-800">Incident Details</h1>

        <div class="card shadow-sm mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Incident Information</h5>
            </div>
            
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-3 font-weight-bold text-primary">
                        ID
                    </div>
                    <div class="col-md-9">
                        {{ logIncident.incidentid }}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-3 font-weight-bold text-primary">
                        Email
                    </div>
                    <div class="col-md-9">
                        {{ logIncident.email|default('-') }}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-3 font-weight-bold text-primary">
                        VIN
                    </div>
                    <div class="col-md-9">
                        {{ logIncident.vin|default('-') }}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-3 font-weight-bold text-primary">
                        Incident Title
                    </div>
                    <div class="col-md-9">
                        {{ logIncident.title|default('-') }}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-3 font-weight-bold text-primary">
                        Incident Description
                    </div>
                    <div class="col-md-9">
                        {{ logIncident.comment|default('No description provided') }}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-3 font-weight-bold text-primary">
                        Creation Date
                    </div>
                    <div class="col-md-9">
                        {{ logIncident.creation_date ? logIncident.creation_date|date('m/d/Y H:i') : '-' }}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-3 font-weight-bold text-primary">
                        Attachments
                    </div>
                    <div class="col-md-9">
                        {% if logIncident.files is not empty %}
                            <div class="list-group">
                                {% for file in logIncident.files %}
                                    <a href="{{ file }}" target="_blank" class="list-group-item list-group-item-action">
                                        <i class="fas fa-file-alt mr-2"></i> {{ file|split('/')|last }}
                                    </a>
                                {% endfor %}
                            </div>
                        {% else %}
                            <span class="text-muted">No attachments</span>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="card-footer d-flex justify-content-between">
                <button onclick="history.back()" class="btn btn-secondary">
                    <i class="fas fa-arrow-left mr-2"></i> Back
                </button>
                <button type="button" class="btn btn-danger" data-toggle="modal" data-target="#deleteModal" data-id="{{ logIncident.incidentid }}">
                    <i class="fas fa-trash-alt mr-2"></i> Delete
                </button>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteModalLabel">Confirm Deletion</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    Are you sure you want to delete this incident? This action cannot be undone.
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <form id="deleteForm" method="POST" action="{{ path('log_incident_delete', {'id': logIncident.incidentid}) }}">

                        <input type="hidden" name="_method" value="DELETE">
                        <input type="hidden" name="_token" value="{{ csrf_token('delete-log-incident') }}">
                        <button type="submit" class="btn btn-danger">Delete Incident</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block script %}
    {{ parent() }}
    <script>
        $(document).ready(function() {
            // Delete modal handling
            $('#deleteModal').on('show.bs.modal', function (event) {
                const button = $(event.relatedTarget);
                const incidentId = button.data('id');
                const form = $('#deleteForm');
                form.attr('action', form.attr('action').replace('__ID__', incidentId));
            });

            // Reset form action when modal is hidden
            $('#deleteModal').on('hidden.bs.modal', function () {
                const form = $('#deleteForm');
                form.attr('action', form.attr('action').replace(/\/\d+$/, '/__ID__'));
            });
        });
    </script>
{% endblock %}