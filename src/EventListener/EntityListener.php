<?php

namespace App\EventListener;

use App\Entity\AccessLog;
use App\Event\AccessLogSaveEvent;
use App\Helpers\LoggerTrait;
use App\Security\User;
use App\Service\AccessLogConfigService;
use DateTime;
use Doctrine\Common\Util\ClassUtils;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Event\LifecycleEventArgs;
use Doctrine\ORM\Event\PostPersistEventArgs;
use Doctrine\ORM\Event\PostRemoveEventArgs;
use Doctrine\ORM\Event\PostUpdateEventArgs;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Event\PreRemoveEventArgs;
use Doctrine\ORM\Event\PreUpdateEventArgs;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Bundle\SecurityBundle\Security;
use Exception;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Contracts\EventDispatcher\EventDispatcherInterface;
use Webmozart\Assert\Assert;

class EntityListener implements EventSubscriberInterface
{
    use LoggerTrait;
    private const ENTITY_ACTIONS_CREATE = 'create';
    private const ENTITY_ACTIONS_UPDATE = 'update';
    private const ENTITY_ACTIONS_REMOVE = 'remove';

    private array $context = [];

    public function __construct(
        private Security $security,
        private RequestStack $requestStack,
        private AccessLogConfigService $configService,
        private EntityManagerInterface $entityManager,
        private EventDispatcherInterface $eventDispatcher
    ) {
        $this->security = $security;
        $this->requestStack = $requestStack;
        $this->configService = $configService;
        $this->entityManager = $entityManager;
    }

    private function saveContext(LifecycleEventArgs $args): void
    {
        $entity = $args->getObject();
        $entityClass = ClassUtils::getClass($entity);

        $metadata = $this->entityManager->getClassMetadata($entityClass);
        $identifierFields = $metadata->getIdentifierFieldNames();

        // Get the values of the identifier fields
        $identifierValues = [];
        foreach ($identifierFields as $fieldName) {
            $value = $metadata->getFieldValue($entity, $fieldName);
            $identifierValues[$fieldName] = $value;
        }

        $changedFields = [];
        if ($args instanceof PreUpdateEventArgs) {
            $changedFields = $args->getEntityChangeSet();
        }
    
        $this->context[$entityClass] = [
            'entity' => $entity,
            'entityClass' => $entityClass,
            'entityIdFields' => implode('#', $identifierFields),
            'entityIdValues' => implode('#', $identifierValues),
            'changedFields' => $changedFields,            
            'entityFields' => $metadata->getFieldNames(),
        ];
    }

    // is public for testing purposes
    public function getContext(string $entityClass): array
    {
        return $this->context[$entityClass] ?? [];
    }

    // is public for testing purposes
    public function clearContext(): void
    {
        $this->context = [];
    }

    public static function getSubscribedEvents(): array
    {
        return [
            'preUpdate',
            'prePersist',
            'preRemove',
            'Update',
            'Persist',
            'Remove',
            'postUpdate',
            'postPersist',
            'postRemove',
        ];
    }

    public function preUpdate(PreUpdateEventArgs $args): void
    {
        $this->saveContext($args);
        return;
    }

    public function prePersist(PrePersistEventArgs $args): void
    {
        $this->saveContext($args);
    }

    public function preRemove(PreRemoveEventArgs $args): void
    {
        $this->saveContext($args);
    }

    public function postUpdate(PostUpdateEventArgs $args): void
    {
        $this->handleEvent(self::ENTITY_ACTIONS_UPDATE, $args);
        $this->clearContext();
    }

    public function postPersist(PostPersistEventArgs $args): void
    {
        $this->handleEvent(self::ENTITY_ACTIONS_CREATE, $args);
        $this->clearContext();
    }

    public function postRemove(PostRemoveEventArgs $args): void
    {
        $this->handleEvent(self::ENTITY_ACTIONS_REMOVE, $args);
        $this->clearContext();
    }

    private function handleEvent(string $action, $args): void
    {
        try {
            // Check if the event is a valid event
            if (!(
                $args instanceof PostRemoveEventArgs ||
                $args instanceof PostPersistEventArgs ||
                $args instanceof PostUpdateEventArgs
            )) {
                return;
            }

            $entity = $args->getObject();
            $entityClass = ClassUtils::getClass($entity);

            // prevent infinite loop
            if ($entity instanceof AccessLog) {
                return;
            }

            if (!$this->configService->isEnabled($entityClass)) {
                return;
            }

            // Get the current user (if available)
            $user = $this->security->getUser();

            // Get the request information (if available)
            $request = $this->requestStack->getCurrentRequest();
            $route = $request ? $request->getPathInfo() : null;
            $method = $request ? $request->getMethod() : null;

            // Check if entity should be excluded based on route, command, or username
            if ($this->shouldExclude($route, $method, $entityClass)) {
                return;
            }

            // Get the list of excluded fields for the entity
            $excludedFields = $this->configService->getExcludedFields($entityClass);

            // Get the list of fields for the entity
            $metadata = $this->entityManager->getClassMetadata($entityClass);

            // Calculate the list of included fields (excluding excluded fields)
            $includedFields = array_diff($this->getContext($entityClass)['entityFields'] ?? [], $excludedFields);
            
            // Replace each field name in $includedFields with a key-value pair
            $valuesAfter = [];
            $valuesBefore = [];
            $changes = $this->getContext($entityClass)['changedFields'] ?? [];
            if ($action === self::ENTITY_ACTIONS_UPDATE && !empty($changes)) {
                // save only updated fields
                foreach ($includedFields as $fieldName) {
                    if (array_key_exists($fieldName, $changes)) {
                        $valuesBefore[$fieldName] = $changes[$fieldName][0]; // get old value
                        $valuesAfter[$fieldName] = $changes[$fieldName][1]; // get new value
                    }
                }
            } else {
                // save all fields
                $valuesList = [];
                foreach ($includedFields as $fieldName) {
                    if ($metadata->hasField($fieldName)) {
                        $value = $metadata->getFieldValue($entity, $fieldName);
                        $valuesList[$fieldName] = $value;
                    }
                }

                if($action === self::ENTITY_ACTIONS_REMOVE) {
                    $valuesBefore = $valuesList;
                } else {
                    $valuesAfter = $valuesList;
                }

                // Update the identifier values if the entity is being created
                if($action === self::ENTITY_ACTIONS_CREATE && empty($this->context[$entityClass]['entityIdValues'])) {
                    $this->updateIdentifierValues($entityClass, $valuesAfter);
                }
            }

            // Log the action
            $accessLog = new AccessLog();
            $accessLog->setAction($action);
            $accessLog->setEntityClass($entityClass);
            $accessLog->setEntityId($this->context[$entityClass]['entityIdValues']);
            $accessLog->setUsername($user ? $user->getUserIdentifier() : null);
            $accessLog->setRoute($route);
            $accessLog->setMethod($method);
            $accessLog->setValuesBefore($valuesBefore); 
            $accessLog->setValuesAfter($valuesAfter); 
            $accessLog->setTimestamp(new DateTime());
            Assert::nullOrIsInstanceOf($user, User::class);
            $accessLog->setProfile($user ? $user->getProfile() : null);

            //$this->entityManager->persist($accessLog);
            // save the log outside of doctrine events
            $accessLogSaveEvent = new AccessLogSaveEvent($accessLog);
            $this->eventDispatcher->dispatch($accessLogSaveEvent, AccessLogSaveEvent::NAME);

        } catch (Exception $e) {
            $this->logger->error('Error logging entity action', ['action' => $action, 'args' => $args, 'exception' => $e]);
        }
    }

    private function shouldExclude(?string $route, ?string $method, string $entityClass): bool
    {
        $excludedRoutes = $this->configService->getExcludedRoutes($entityClass);
        $excludedCommands = $this->configService->getExcludedCommands($entityClass);

        if ($route && in_array($route, $excludedRoutes)) {
            return true;
        }

        if ($method === 'CLI' && in_array($method, $excludedCommands)) {
            return true;
        }

        return false;
    }

    private function updateIdentifierValues(string $entityClass, array $updatedValues = []): void
    {
        if(!isset($this->context[$entityClass])) {  
            return;
        }

        $metadata = $this->entityManager->getClassMetadata($entityClass);
        $identifierFields = $metadata->getIdentifierFieldNames();

        // Get the values of the identifier fields
        $identifierValues = [];
        foreach ($identifierFields as $fieldName) {
            $value =  $updatedValues[$fieldName] ?? null;
            $identifierValues[$fieldName] = $value;
        }

        $this->context[$entityClass]['entityIdFields'] = implode('#', $identifierFields);
        $this->context[$entityClass]['entityIdValues'] = implode('#', $identifierValues);
    }

}
