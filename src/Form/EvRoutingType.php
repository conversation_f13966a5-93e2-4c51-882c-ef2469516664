<?php

namespace App\Form;

use App\Helpers\BrandHelper;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;

class EvRoutingType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options)
    {
        $builder
            ->add('label', TextType::class, [
                'required' => false
            ])
            ->add('brand', ChoiceType::class, [
                'choices' => array_combine(
                    array_map(fn($code) => BrandHelper::getBrandName($code), BrandHelper::all()),
                    BrandHelper::all()
                ),
                'required' => true,
                'placeholder' => 'Select a brand',
            ])
            ->add('enabled', CheckboxType::class, array(
                'label_attr' => ['class' => 'checkbox-custom checkbox-inline'],
                'label' => false,
                'required' => false
            ))
            ->add('lcdv', TextareaType::class, [
                'required' => true,
                'attr' => [
                    'rows' => 5
                ]
            ])
            ->add('dvq', TextType::class, [
                    'required' => true,
                ])
            ->add('b0f', TextType::class, [
                    'required' => true,
                ])
            ->add('dar', TextType::class, [
                    'required' => true,
                ])
            ->add('engineType', TextType::class)

            ->add('constantSpeedConsumptionInkWhPerHundredkm', TextType::class, [
                    'required' => false,
                ])
            ->add('maxChargeInkWh', TextType::class, [
                    'required' => false,
                ])
            ->add('vehicleMaxSpeed', TextType::class, [
                'required' => false,
                ])
            ->add('vehicleWeight', TextType::class, [
                    'required' => false,
                ])
            ->add('vehicleAxleWeight', TextType::class, [
                    'required' => false,
                ])
            ->add('vehicleLength', TextType::class, [
                    'required' => false,
                ])
            ->add('vehicleWidth', TextType::class, [
                    'required' => false,
                ])
            ->add('vehicleHeight', TextType::class, [
                    'required' => false,
                ])
            ->add('accelerationEfficiency', TextType::class, [
                    'required' => false,
                ])
            ->add('decelerationEfficiency', TextType::class, [
                    'required' => false,
                ])
            ->add('uphillEfficiency', TextType::class, [
                    'required' => false,
                ])
            ->add('downhillEfficiency', TextType::class, [
                    'required' => false,
                ])
            ->add('chargingCurveArray', TextareaType::class, [
                    'required' => false,
                ]);



            if(in_array($options['data']['brand'] ?? '', ['OP', 'VX']))
            {
                $builder->add('rpo', TextareaType::class, [
                    'required' => false,
                    'attr' => [
                        'rows' => 5
                    ]
                ]);
            }
    }
}
