<?php

namespace App\Controller\Api;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController as SymfonyAbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Validator\ConstraintViolation;
use Symfony\Component\Validator\ConstraintViolationListInterface;

abstract class AbstractController extends SymfonyAbstractController
{
    /**
     * Standard response structure
     */
    protected const RESPONSE_FORMAT = [
        'success' => null,  // boolean
        'status' => null,   // int
        'message' => null,  // string
        'data' => null,     // mixed
        'errors' => null,   // array
        'meta' => null,     // array (pagination, etc.)
    ];

    /**
     * Extract validation error messages from constraint violations
     */
    protected function getValidationMessages(ConstraintViolationListInterface $errors): array
    {
        $messages = [];

        foreach ($errors as $error) {
            /** @var ConstraintViolation $error */
            $name = str_replace(['[', ']'], '', $error->getPropertyPath());
            $messages[$name] = $error->getMessage();
        }

        return $messages;
    }

    /**
     * Create a validation error response
     */
    protected function getValidationErrorResponse(
        array $messages,
        int $statusCode = JsonResponse::HTTP_UNPROCESSABLE_ENTITY
    ): JsonResponse {
        $response = self::RESPONSE_FORMAT;
        $response['success'] = false;
        $response['status'] = $statusCode;
        $response['message'] = 'Validation failed';
        $response['errors'] = $messages;

        return $this->json(array_filter($response), $statusCode);
    }

    /**
     * Create a successful response
     */
    protected function successResponse(
        mixed $data = null,
        string $message = 'Success',
        array $meta = [],
        int $statusCode = JsonResponse::HTTP_OK
    ): JsonResponse {
        $response = self::RESPONSE_FORMAT;
        $response['success'] = true;
        $response['status'] = $statusCode;
        $response['message'] = $message;
        $response['data'] = $data;

        if (!empty($meta)) {
            $response['meta'] = $meta;
        }

        return $this->json(array_filter($response), $statusCode);
    }

    /**
     * Create a created response (201)
     */
    protected function createdResponse(
        mixed $data = null,
        string $message = 'Resource created successfully',
        array $meta = []
    ): JsonResponse {
        return $this->successResponse($data, $message, $meta, JsonResponse::HTTP_CREATED);
    }

    /**
     * Create a not found response
     */
    protected function notFoundResponse(
        string $message = 'Resource not found'
    ): JsonResponse {
        $response = self::RESPONSE_FORMAT;
        $response['success'] = false;
        $response['status'] = JsonResponse::HTTP_NOT_FOUND;
        $response['message'] = $message;

        return $this->json(array_filter($response), JsonResponse::HTTP_NOT_FOUND);
    }

    /**
     * Create an unauthorized response
     */
    protected function unauthorizedResponse(
        string $message = 'Unauthorized access'
    ): JsonResponse {
        $response = self::RESPONSE_FORMAT;
        $response['success'] = false;
        $response['status'] = JsonResponse::HTTP_UNAUTHORIZED;
        $response['message'] = $message;

        return $this->json(array_filter($response), JsonResponse::HTTP_UNAUTHORIZED);
    }

    /**
     * Create a forbidden response
     */
    protected function forbiddenResponse(
        string $message = 'Access forbidden'
    ): JsonResponse {
        $response = self::RESPONSE_FORMAT;
        $response['success'] = false;
        $response['status'] = JsonResponse::HTTP_FORBIDDEN;
        $response['message'] = $message;

        return $this->json(array_filter($response), JsonResponse::HTTP_FORBIDDEN);
    }

    /**
     * Create a server error response
     */
    protected function serverErrorResponse(
        string $message = 'Internal server error',
        array $errors = []
    ): JsonResponse {
        $response = self::RESPONSE_FORMAT;
        $response['success'] = false;
        $response['status'] = JsonResponse::HTTP_INTERNAL_SERVER_ERROR;
        $response['message'] = $message;

        if (!empty($errors)) {
            $response['errors'] = $errors;
        }

        return $this->json(array_filter($response), JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
    }

    /**
     * Create a paginated response
     */
    protected function paginatedResponse(
        mixed $data,
        int $page,
        int $limit,
        int $total,
        string $message = 'Success'
    ): JsonResponse {
        $meta = [
            'pagination' => [
                'page' => $page,
                'limit' => $limit,
                'total' => $total,
                'pages' => ceil($total / $limit),
            ]
        ];

        return $this->successResponse($data, $message, $meta);
    }

    /**
     * Create a response with caching headers
     */
    protected function cachedResponse(
        mixed $data,
        int $maxAge = 3600,
        bool $isPublic = true
    ): JsonResponse {
        $response = $this->successResponse($data);

        $response->setMaxAge($maxAge);
        $response->setPublic($isPublic);
        $response->setEtag(md5($response->getContent()));

        return $response;
    }
}
