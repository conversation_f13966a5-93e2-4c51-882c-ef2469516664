<?php

namespace App\Service;

use Symfony\Component\Yaml\Yaml;

class DebugConsoleManager
{

    public function __construct(
        private string $env,
    ) {}

    /**
     * Load external APIs configuration from YAML file
     */
    public function loadExternalApisConfig(string $configPath): array
    {
        if ($this->env === 'prod') {
            $path = $configPath . 'api_config_prod.yaml';
        } else if ($this->env === 'preprod') {
            $path = $configPath . 'api_config_preprod.yaml';
        } else if ($this->env === 'integ') {
            $path = $configPath . 'api_config_dev.yaml';
        } else {
            $path = $configPath . 'api_config_dev.yaml';
        }

        if (!file_exists($path)) {
            return [];
        }

        try {
            return Yaml::parseFile($path);
        } catch (\Exception $e) {
            // Log error and return empty array as fallback
            return [];
        }
    }

    /**
     * Build MongoDB filter based on form data using the new document structure
     */
    public function buildMongoFilter(array $formData): array
    {
        $filter = [];
        $andConditions = [];

        // Filter by userId if provided
        if (!empty($formData['userID'])) {
            $andConditions[] = ['userId' => $formData['userID']];
        }

        // Filter by email if provided (search in profile.email)
        if (!empty($formData['email'])) {
            $andConditions[] = ['profile.email' => $formData['email']];
        }

        // Filter by VIN if provided (search in vehicle array)
        if (!empty($formData['vin'])) {
            $andConditions[] = ['vehicle.vin' => $formData['vin']];
        }

        // If multiple conditions, use $and operator for combined filtering
        if (count($andConditions) > 1) {
            $filter['$and'] = $andConditions;
        } elseif (count($andConditions) === 1) {
            $filter = $andConditions[0];
        }

        return $filter;
    }

    /**
     * Generate a descriptive label for the vehicle
     */
    public function generateVehicleLabel(array $vehicle, array $document): string
    {
        $brand = $vehicle['brand'] ?? 'Unknown';
        $vin = $vehicle['vin'] ?? 'N/A';
        $userName = trim(($document['profile']['firstName'] ?? '') . ' ' . ($document['profile']['lastName'] ?? ''));

        if (empty($userName)) {
            $userName = 'User';
        }

        return "{$brand} Vehicle - {$userName} ({$vin})";
    }
}
