import org.testng.annotations.Test;
import java.io.File;
import java.util.concurrent.TimeUnit;
public class Widget_Administration{
    @Test(groups = {"Widget Administration"})
    public void Create_Widget() throws InterruptedException {
        Methods user = new Methods();
        user.login();
        Locators.Administration().click();
        TimeUnit.SECONDS.sleep(1);
        Locators.Widget_Administration().click();
        Locators.Create_Features_Btn().click();
        Locators.Widget_Name().sendKeys("Widget Automation Test");
        Locators.Widget_Description().sendKeys("Automation Test Purpose");
        Locators.Widget_Version().sendKeys("1.1");
        String relativePath = "src/Files/Automation_Widget_Administration_Test.json";
        String absolutePath = new File(relativePath).getAbsolutePath();
        Locators.Widget_File_Upload().sendKeys(absolutePath);
        Locators.Widget_Save().click();

       user.exit();
    }
}

