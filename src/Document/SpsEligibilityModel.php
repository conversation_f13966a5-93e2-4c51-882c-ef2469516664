<?php

namespace App\Document;

/**
 * SPS Eligibility Model Document (Table 2)
 *
 * Represents eligibility rules for SPS (Smart Phone Services) based on vehicle models
 * and model years. This document defines which vehicle models are eligible for
 * specific SPS features based on their model year.
 *
 * This document is stored in MongoDB Atlas and represents the second table
 * of the SPS Eligibility system.
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
class SpsEligibilityModel
{
    /**
     * MongoDB document ID
     */
    private ?string $id = null;

    /**
     * Scope of eligibility (LCDV or MODEL)
     */
    private string $scope = 'MODEL';

    /**
     * Array of codes (model codes for MODEL scope)
     *
     * @var string[]
     */
    private array $codes = [];

    /**
     * Text description of eligibility rule
     */
    private ?string $eligibilityRule = null;

    /**
     * Minimum model year for eligibility (e.g., 2020)
     */
    private ?int $modelYearFrom = null;

    /**
     * Type/category of eligibility rule (e.g., "fiat500", "spsGeneric", "Premium")
     */
    private ?string $type = null;

    /**
     * Eligibility disclaimer (string: "YES" or "NO")
     */
    private ?string $eligibilityDisclaimer = null;

    /**
     * Document creation timestamp
     */
    private ?\DateTime $createdAt = null;

    /**
     * Document last update timestamp
     */
    private ?\DateTime $updatedAt = null;

    /**
     * Initialize document with current timestamps
     */
    public function __construct()
    {
        $this->createdAt = new \DateTime();
        $this->updatedAt = new \DateTime();
    }

    /**
     * Get MongoDB document ID
     *
     * @return string|null Document ID or null if not persisted
     */
    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * Set MongoDB document ID
     *
     * @param string|null $id Document ID
     * @return self
     */
    public function setId(?string $id): self
    {
        $this->id = $id;
        return $this;
    }

    /**
     * Get scope
     */
    public function getScope(): string
    {
        return $this->scope;
    }

    /**
     * Set scope
     */
    public function setScope(string $scope): self
    {
        $this->scope = $scope;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    /**
     * Get array of codes
     *
     * @return string[]
     */
    public function getCodes(): array
    {
        return $this->codes;
    }

    /**
     * Set array of codes
     *
     * @param string[] $codes
     * @return self
     */
    public function setCodes(array $codes): self
    {
        $this->codes = $codes;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    /**
     * Get codes as comma-separated string for display
     */
    public function getCodesString(): string
    {
        return implode(', ', $this->codes);
    }

    /**
     * Get array of vehicle model names (legacy method)
     *
     * @return string[] Array of vehicle model names
     */
    public function getModels(): array
    {
        return $this->codes;
    }

    /**
     * Set array of vehicle model names (legacy method)
     *
     * @param string[] $models Array of vehicle model names
     * @return self
     */
    public function setModels(array $models): self
    {
        $this->codes = $models;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    /**
     * Get models as comma-separated string for form display (legacy method)
     *
     * @return string Comma-separated model names
     */
    public function getModelsString(): string
    {
        return implode(', ', $this->codes);
    }

    /**
     * Set models from comma-separated string
     *
     * @param string $modelsString Comma-separated model names
     * @return self
     */
    public function setModelsString(string $modelsString): self
    {
        $models = array_map('trim', explode(',', $modelsString));
        $models = array_filter($models); // Remove empty values
        $this->setCodes($models); // Fixed: use setCodes instead of setModels
        $this->updatedAt = new \DateTime();
        return $this;
    }



    /**
     * Get eligibility rules description
     *
     * @return string|null Text description of eligibility rules
     */
    public function getEligibilityRules(): ?string
    {
        return $this->eligibilityRule;
    }

    /**
     * Set eligibility rules description
     *
     * @param string|null $eligibilityRules Text description of eligibility rules
     * @return self
     */
    public function setEligibilityRules(?string $eligibilityRules): self
    {
        $this->eligibilityRule = $eligibilityRules;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    /**
     * Get eligibility rule (new method)
     */
    public function getEligibilityRule(): ?string
    {
        return $this->eligibilityRule;
    }

    /**
     * Set eligibility rule (new method)
     */
    public function setEligibilityRule(?string $eligibilityRule): self
    {
        $this->eligibilityRule = $eligibilityRule;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    public function getModelYearFrom(): ?int
    {
        return $this->modelYearFrom;
    }

    public function setModelYearFrom(?int $modelYearFrom): self
    {
        $this->modelYearFrom = $modelYearFrom;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    public function getEligibilityDisclaimer(): ?string
    {
        return $this->eligibilityDisclaimer;
    }

    public function setEligibilityDisclaimer(?string $eligibilityDisclaimer): self
    {
        $this->eligibilityDisclaimer = $eligibilityDisclaimer;
        $this->updatedAt = new \DateTime();
        return $this;
    }

    // Legacy method for backward compatibility
    public function isEligibilityDisclaimer(): bool
    {
        return $this->eligibilityDisclaimer === 'YES';
    }

    public function getCreatedAt(): ?\DateTime
    {
        return $this->createdAt;
    }

    public function setCreatedAt(?\DateTime $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getUpdatedAt(): ?\DateTime
    {
        return $this->updatedAt;
    }

    public function setUpdatedAt(?\DateTime $updatedAt): self
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }

    /**
     * Convert to array for MongoDB storage
     */
    public function toArray(): array
    {
        $data = [
            'scope' => $this->scope,
            'codes' => $this->codes ?? [],
            'eligibilityRule' => $this->eligibilityRule,
            'modelYearFrom' => $this->modelYearFrom,
            'type' => $this->type,
            'eligibilityDisclaimer' => $this->eligibilityDisclaimer,
            'createdAt' => $this->createdAt?->getTimestamp() * 1000, // MongoDB timestamp format
            'updatedAt' => $this->updatedAt?->getTimestamp() * 1000,
        ];

        // Remove null values to avoid MongoDB issues
        return array_filter($data, function($value) {
            return $value !== null;
        });
    }

    /**
     * Create from MongoDB array
     */
    public static function fromArray(array $data): self
    {
        $document = new self();

        if (isset($data['_id'])) {
            $document->setId($data['_id']['$oid'] ?? $data['_id']);
        }

        // Handle new structure
        $document->setScope($data['scope'] ?? 'MODEL');
        $document->setCodes($data['codes'] ?? []);
        $document->setEligibilityRule($data['eligibilityRule'] ?? null);
        $document->setModelYearFrom($data['modelYearFrom'] ?? null);
        $document->setType($data['type'] ?? null);

        // Handle eligibility disclaimer - convert boolean values to string for backward compatibility
        if (isset($data['eligibilityDisclaimer'])) {
            $originalValue = $data['eligibilityDisclaimer'];
            if (is_bool($originalValue)) {
                // Convert boolean to string for backward compatibility
                $newValue = $originalValue ? 'YES' : 'NO';
                $document->setEligibilityDisclaimer($newValue);
            } else {
                // Handle string values directly
                if (is_string($originalValue)) {
                    $document->setEligibilityDisclaimer($originalValue);
                } else {
                    $document->setEligibilityDisclaimer(null);
                }
            }
        }

        // Handle legacy structure for backward compatibility
        if (isset($data['models'])) {
            $document->setCodes($data['models']);
        }
        if (isset($data['eligibilityRules'])) {
            $document->setEligibilityRule($data['eligibilityRules']);
        }

        // Handle timestamps (both string and numeric formats)
        if (isset($data['createdAt'])) {
            if (is_numeric($data['createdAt'])) {
                $document->setCreatedAt(new \DateTime('@' . intval($data['createdAt'] / 1000)));
            } else {
                $document->setCreatedAt(new \DateTime($data['createdAt']));
            }
        }

        if (isset($data['updatedAt'])) {
            if (is_numeric($data['updatedAt'])) {
                $document->setUpdatedAt(new \DateTime('@' . intval($data['updatedAt'] / 1000)));
            } else {
                $document->setUpdatedAt(new \DateTime($data['updatedAt']));
            }
        }

        return $document;
    }
}
