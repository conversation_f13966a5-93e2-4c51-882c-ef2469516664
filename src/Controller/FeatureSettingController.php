<?php

namespace App\Controller;

use App\Entity\FeatureSetting;
use App\Form\FeatureSettingType;
use App\Service\FeatureSettingManager;
use App\Service\FeatureManager;
use App\Repository\FeatureSettingRepository;
use App\Repository\SiteRepository;
use App\Security\User;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;
use Symfony\Contracts\Translation\TranslatorInterface as TranslationTranslatorInterface;
use Symfony\Component\Form\FormError;

#[Route('/feature-setting')]
class FeatureSettingController extends AbstractController
{
    #[Route('/', name: 'feature_setting_index', methods: ['GET', 'POST'])]
    public function index(
        #[CurrentUser] User $user,
        FeatureSettingRepository $featureSettingRepository,
        SiteRepository $siteRepository
    ): Response {
        $profile = $user->getProfile();
        $site = $profile->getSite();
        $role = $profile->getRole();

        $site = $siteRepository->find($site->getId());
        $brand = $site?->getBrand() ?? $profile->getBrand();
        $country = $site?->getCountry();

        return $this->render('feature_setting/index.html.twig', [
            'feature_settings' => $featureSettingRepository->findByLocalUserOrSuperAdmin($role, $brand, $country),
            'isSuperAdmin' => $profile->isSuperAdministrator()
        ]);
    }

    #[Route('/new', name: 'feature_setting_new', methods: ['GET', 'POST'])]
    public function new(
        #[CurrentUser] User $user,
        Request $request,
        FeatureSettingManager $featureSettingManager,
        SiteRepository $siteRepository,
        TranslationTranslatorInterface $translator
    ): Response {
        $profile = $user->getProfile();
        $site = $profile->getSite();

        $site = $siteRepository->find($site->getId());
        $brand = $site?->getBrand() ?? $profile->getBrand();
        $country = $site?->getCountry();
        $userRole = $user->getProfile()->getRole();

        $featureSetting = new FeatureSetting();
        $form = $this->createForm(FeatureSettingType::class, $featureSetting, ['isFileRequired' => true, 'role' => $userRole, 'validation_groups' => ['create']]);

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {

            $file = $form->get('file')->getData();

            $name = $featureSetting->getName();

            $existingFeature = $featureSettingManager->findFeatureSettingByName($name);

            if ($existingFeature) {
                $form->get('name')->addError(new FormError('The name is already taken.'));
            } else {
                if ($featureSettingManager->saveFeatureSetting($file, $featureSetting, $brand, $country)) {
                    $this->addFlash('success', $translator->trans('feature_save'));
                    return $this->redirectToRoute('feature_setting_index', [], Response::HTTP_SEE_OTHER);
                }
            }
        }

        return $this->render('feature_setting/new.html.twig', [
            'feature_setting' => $featureSetting,
            'form' => $form,
        ]);
    }

    #[Route('/{id}/edit', name: 'feature_setting_edit', methods: ['GET', 'POST'])]
    public function edit(
        #[CurrentUser] User $user,
        Request $request,
        FeatureSetting $featureSetting,
        FeatureSettingManager $featureSettingManager,
        SiteRepository $siteRepository,
        FeatureManager $featureManager,
        TranslationTranslatorInterface $translator
    ): Response {
        $profile = $user->getProfile();
        $site = $profile->getSite();

        $site = $siteRepository->find($site->getId());
        $brand = $site?->getBrand() ?? $profile->getBrand();
        $country = $site?->getCountry();
        $userRole = $user->getProfile()->getRole();

        $form = $this->createForm(FeatureSettingType::class, $featureSetting, ['isFileRequired' => false, 'role' => $userRole, 'validation_groups' => ['edit']]);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $file = $form->get('file')->getData();

            $featureSettingManager->editFeatureSetting($file, $featureSetting, $brand, $country);
            $featureManager->updateFeature($featureSetting, $brand, $country);
            $this->addFlash('success', $translator->trans('feature_update'));

            return $this->redirectToRoute('feature_setting_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('feature_setting/edit.html.twig', [
            'feature_setting' => $featureSetting,
            'form' => $form,
        ]);
    }

    #[Route('/download/{id}', name: 'feature_setting_download', methods: ['GET'])]
    public function download(FeatureSettingManager $featureSettingManager, FeatureSetting $featureSetting): Response
    {
        $response = new Response(json_encode($featureSetting->getContent()));

        // Set the appropriate headers for file download
        $response->headers->set('Content-Type', 'text/plain');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $featureSetting->getFile() . '"');

        return $response;
    }

    #[Route('/{id}', name: 'feature_setting_delete', methods: ['POST'])]
    public function delete(Request $request, FeatureSetting $featureSetting, FeatureSettingManager $featureSettingManager, FeatureManager $featureManager, TranslationTranslatorInterface $translator): Response
    {
        if ($this->isCsrfTokenValid('delete-feature-setting', $request->request->get('_token'))) {

            if ($featureManager->removeFeature($featureSetting) && $featureSettingManager->removeFeatureSetting($featureSetting)) {
                $this->addFlash('success', $translator->trans('feature_setting_deleted'));
            } else {
                $this->addFlash('error', 'failed to delete feature !');
            }
        }

        return $this->redirectToRoute('feature_setting_index', [], Response::HTTP_SEE_OTHER);
    }

    #[Route('/make_package/{id}', name: 'feature_setting_make_package', methods: ['POST'])]
    public function makePackage(Request $request, FeatureSetting $featureSetting, FeatureManager $featureManager): Response
    {
        $csrfToken = $request->request->get('_csrf_token');

        if (!$this->isCsrfTokenValid('feature_setting_make_package', $csrfToken)) {
            throw $this->createAccessDeniedException('Invalid CSRF token.');
        }

        $packageData = $featureManager->makePackage($featureSetting);

        $jsonContent = json_encode($packageData, JSON_PRETTY_PRINT);
        $compressedContent = gzencode($jsonContent);

        $filename = $featureSetting->getName() . '.json.gz';

        $response = new Response();
        $response->headers->set('Content-Type', 'application/zip');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $filename . '"');
        $response->setContent($compressedContent);

        return $response;
    }

    #[Route('/feature/import', name: 'feature_setting_import', methods: ['POST'])]
    public function importFeature(Request $request, FeatureManager $featureManager): Response
    {
        $csrfToken = $request->request->get('_csrf_token');

        if (!$this->isCsrfTokenValid('feature_setting_import', $csrfToken)) {
            throw $this->createAccessDeniedException('Invalid CSRF token.');
        }

        $file = $request->files->get('file');

        if ($file && $file->isValid()) {
            $content = file_get_contents($file->getPathname());
            $content = gzdecode($content);
            
            [$status, $message]= $featureManager->importFeature($content);
            if ($status) {
                $this->addFlash('success', $message);
            } else {
                $this->addFlash('error', $message);
            }
        } else {
            $this->addFlash('error', 'Invalid file uploaded.');
        }

        return $this->redirectToRoute('feature_setting_index', [], Response::HTTP_CREATED);
    }
        
}
