<?php

namespace App\Entity;

use App\Repository\WidgetFeatureAttributeRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Attribute\Groups;

#[ORM\Entity(repositoryClass: WidgetFeatureAttributeRepository::class)]
class WidgetFeatureAttribute
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    #[Groups(['package_export'])]
    private ?string $name = null;

    #[ORM\Column(type: 'text')]
    #[Groups(['package_export'])]
    private ?string $value = null;

    #[Groups(['package_export'])]
    #[ORM\ManyToOne(inversedBy: 'attributes', cascade: ["persist"])]
    #[ORM\JoinColumn(nullable: false)]
    private ?WidgetFeature $widgetFeature = null;

    #[Groups(['package_export'])]
    #[ORM\ManyToOne(cascade: ["persist"])]
    private ?Language $language = null;

    #[Groups(['package_export'])]
    #[ORM\Column(length: 255)]
    private ?string $type = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getValue(): ?string
    {
        return $this->value;
    }

    public function setValue(string $value): self
    {
        $this->value = $value;

        return $this;
    }

    public function getWidgetFeature(): ?WidgetFeature
    {
        return $this->widgetFeature;
    }

    public function setWidgetFeature(?WidgetFeature $widgetFeature): self
    {
        $this->widgetFeature = $widgetFeature;

        return $this;
    }

    public function getLanguage(): ?Language
    {
        return $this->language;
    }

    public function setLanguage(?Language $language): self
    {
        $this->language = $language;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): self
    {
        $this->type = $type;

        return $this;
    }
}
