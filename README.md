# Space BO

## Installation
First, clone the repository

```
<NAME_EMAIL>:D4UDigitalPlatform/space-bo.git
```

### Create an _init folder
On an _init folder you can declare all files needed to run a dev environment using docker

### Update the vhosts file:
Add `back-dev.space.com` to the hosts file.

On Windows systems, the file is `C:\Windows\System32\drivers\etc\hosts File.`
For Linux systems, the file is `/var/etc/hosts`

On the hosts file add this line:

```
127.0.0.1   back-dev.space.com
```
### Build the containers and run the environment
Run docker compose command to create containers and run the environment:

```
docker compose -f _init/docker/docker-compose.yml up -d --build
```

Check if all correctly running with following command:

```
docker ps
```
### Enter on webapp container
After the environment is running, enter into the container where httpd running:

```
sudo docker exec -ti -w /var/www/html app_space_back /bin/bash
```
This command also moves you to `/var`/www/html` folder, where the webapp code is copied.

 ### Load all dependencies
 The first time, all webapp dependencies must be loaded through the composer tool.
Run the following command:

```
composer install
```

### Create the database
Be sure you have already config the env `DATABASE_URL` var with the correct value for the database, both for `dev` and `test` app env.
For this, you probably need to update `.env.dev.local` and `.env.test.local` files.

Using Symfony console now can create the database, running the following commands:

```
	php bin/console doctrine:database:create --env=dev
	php bin/console doctrine:migrations:migrate --env=dev
```

If the database already exists and want just regenerate it, run the following command before the last one:

```
	php bin/console doctrine:cache:clear-metadata --env=dev
	php bin/console cache:clear --env=dev
	php bin/console doctrine:database:drop --env=dev --force
```

### Fill database config tables
Before running the space-bo webapp, some config tables must be filled. For now, you can ask for a database backup and restore the contents for these tables.

In the follow file you can take some settings copied from mymarque project:
[sdbbsack_added_configs.sql](doc/db/sdbback_added_configs.sql)


### Access to space-bo webapp
Now, you are ready to log in to the webapp. From the browser open the url `http://back-dev.space.com:5555`
For now, the authentication service is mocked, so can use the following account:
```
user: admin
password: ejFTwtpdny1
```

## Testing

### Running Tests

The application uses PHPUnit for testing. There are several ways to run the tests depending on your environment and needs.

#### Running All Tests

To run all tests:

```bash
# From the project root
php bin/phpunit
```

#### Running Specific Test Suites

To run a specific test suite:

```bash
# Run only API controller tests
php bin/phpunit tests/Controller/Api

# Run only service tests
php bin/phpunit tests/Service
```

#### Running Individual Test Files

To run a specific test file:

```bash
# Run a specific controller test
php bin/phpunit tests/Controller/Api/EvRoutingApiControllerTest.php

# Run a specific service test
php bin/phpunit tests/Service/EvRoutingServiceTest.php
```

#### Running Tests in Docker

If you're using Docker for development:

```bash
# Access the PHP shell in Docker
make php_shell

# Then run tests inside the container
php bin/phpunit
```

### Test Types

The project includes several types of tests:

1. **Unit Tests**: Test individual components in isolation (controllers, services, transformers)
2. **Functional Tests**: Test API endpoints with real HTTP requests
3. **Repository Tests**: Test database interactions

### Notes on Database Tests

Some tests require a database connection. To run these tests:

1. Make sure your test database is properly configured in `.env.test`
2. Run database migrations for the test environment:
   ```bash
   php bin/console doctrine:migrations:migrate --env=test
   ```

If you want to skip database tests, you can run:

```bash
php bin/phpunit --exclude-group database
```

### Troubleshooting Tests

If you encounter database-related test failures with messages like `Table 'sdbback_test.ev_routing' doesn't exist`, make sure:

1. Your test database is properly configured
2. Migrations have been run in the test environment
3. The test database has the correct schema