<?php

namespace App\Controller;

use App\Entity\Brand;
use App\Entity\Channel;
use App\Entity\Country;
use App\Entity\Language;
use App\Entity\StaticPage;
use App\Form\StaticPageFormType;
use App\Repository\SiteRepository;
use App\Repository\StaticPageRepository;
use App\Security\User;
use App\Service\StaticPageManager;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;

class StaticPageController extends AbstractController
{
    #[Route('/page/{channel}/{brandCode}/{countryCode}/{pageTitle}/{languageCode?}', name: 'static_render')]
    public function renderPage(
        StaticPageRepository $staticPageRepository,
        EntityManagerInterface $em,
        StaticPageManager $manager,
        string $brandCode,
        string $countryCode,
        ?string $languageCode,
        string $pageTitle,
        string $channel
    ): Response {
        $brand = $em->getRepository(Brand::class)->findOneBy(['code' => strtoupper($brandCode)]);
        $country = $em->getRepository(Country::class)->findOneBy(['code' => strtoupper($countryCode)]);
        $channel = $em->getRepository(Channel::class)->findOneBy(['name' => strtoupper($channel)]);
        $language = null;
        if ($languageCode) {
            $language = $em->getRepository(Language::class)->findOneBy(['code' => strtoupper($languageCode)]);
        }
        $criteria = ['brand' => $brand, 'country' => $country, 'channel' => $channel, 'page_title' => $pageTitle];
        if ($language) {
            $criteria['language'] = $language;
        }
        $page = $staticPageRepository->findOneBy($criteria);
        if (!$page) {
            throw $this->createNotFoundException("Page not found.");
        }
        $content = $manager->getPublishedHTML($page);
        return new Response($content);
    }


    #[Route('/static-page', name: 'static_index')]
    public function index(Request $request, #[CurrentUser] User $user, StaticPageRepository $staticPageRepository, SiteRepository $siteRepository): Response
    {
        $profile = $user->getProfile();
        $site = $profile->getSite();
        $site = $siteRepository->find($site->getId());
        $country = $site->getCountry();
        $brand = $site->getBrand();
        $pages = $staticPageRepository->findBy(['brand' => $brand, 'country' => $country]);
        return $this->render('text_converter/index.html.twig', [
            'pages' => $pages
        ]);
    }

    #[Route('/static-page/create', name: 'static_create')]
    public function create(Request $request, EntityManagerInterface $em,  #[CurrentUser] User $user, SiteRepository $siteRepository, StaticPageManager $manager): Response
    {
        $profile = $user->getProfile();
        $createdBy = $user->getUserIdentifier();
        $site = $profile->getSite();
        $site = $siteRepository->find($site->getId());
        $country = $site->getCountry();
        $brand = $site->getBrand();
        $languages = $site->getLanguages()->toArray();
        $page = new StaticPage();
        $page->setBrand($brand);
        $page->setCountry($country);
        $form = $this->createForm(StaticPageFormType::class, $page, ['brand' => $brand, 'country' => $country, 'languages' => $languages]);

        $form->handleRequest($request);
        $action = $request->request->get('action', $request->query->get('action'));

        if ($form->isSubmitted() && $form->isValid()) {
            $page = $form->getData();
            $content = $form->get('content')->getData();
            $path =  $manager->getPath($page);
            if ($action === 'preview') {
                return $this->render('text_converter/result.html.twig', [
                    'htmlContent' => $content ?? '<p>No content received</p>',
                    'path' => $path,
                    'form' => $form->createView(),
                    'formData' => $page
                ]);
            }
            if ($action === 'save') {
                $saved = $manager->savePage($page, $content, $path);
                if ($saved) {
                    $this->addFlash('success', 'Page saved successfully!');
                    return $this->redirectToRoute('static_index');
                } else {
                    $this->addFlash('danger', 'Page not saved');
                    return $this->render('text_converter/create.html.twig', [
                        'form' => $form->createView(),
                    ]);
                }
            }
            if ($action === 'publish') {
                $saved = $manager->publishPage($page, $content, $createdBy, $profile);
                if ($saved) {
                    $this->addFlash('success', 'Page published successfully!');
                    return $this->redirectToRoute('static_index');
                } else {
                    $this->addFlash('danger', 'Page not saved');
                    return $this->render('text_converter/create.html.twig', [
                        'form' => $form->createView(),
                    ]);
                }
            }
        }
        return $this->render('text_converter/create.html.twig', [
            'form' => $form->createView(),
        ]);
    }

    #[Route('/static-page/edit/{id}', name: 'static_edit')]
    public function edit(Request $request, #[CurrentUser] User $user, SiteRepository $siteRepository, StaticPageManager $manager, StaticPage $page): Response
    {
        $profile = $user->getProfile();
        $createdBy = $user->getUserIdentifier();
        $site = $profile->getSite();
        $site = $siteRepository->find($site->getId());
        $country = $site->getCountry();
        $brand = $site->getBrand();
        $languages = $site->getLanguages()->toArray();
        $content = $manager->getHTML($page);
        $form = $this->createForm(StaticPageFormType::class, $page, ['brand' => $brand, 'country' => $country, 'languages' => $languages, 'content' => $content]);
        $form->handleRequest($request);
        $action = $request->request->get('action', $request->query->get('action'));

        if ($form->isSubmitted() && $form->isValid()) {
            $page = $form->getData();
            $content = $form->get('content')->getData();
            $path =  $manager->getPath($page);
            if ($action === 'preview') {
                return $this->render('text_converter/result.html.twig', [
                    'htmlContent' => $content ?? '<p>No content received</p>',
                    'path' => 'BASE_URL' . '/' . $path,
                    'form' => $form->createView(),
                    'formData' => $page
                ]);
            }
            if ($action === 'save') {
                $saved = $manager->savePage($page, $content, $path);
                if ($saved) {
                    $this->addFlash('success', 'Page edited successfully!');
                    return $this->redirectToRoute('static_index');
                } else {
                    $this->addFlash('danger', 'Unable to edit the page');
                    return $this->render('text_converter/create.html.twig', [
                        'form' => $form->createView(),
                    ]);
                }
            }
            if ($action === 'publish') {
                $saved = $manager->publishPage($page, $content, $createdBy, $profile);
                if ($saved) {
                    $this->addFlash('success', 'Page published successfully!');
                    return $this->redirectToRoute('static_index');
                } else {
                    $this->addFlash('danger', 'Page not saved');
                    return $this->render('text_converter/create.html.twig', [
                        'form' => $form->createView(),
                    ]);
                }
            }
        }
        return $this->render('text_converter/create.html.twig', [
            'form' => $form->createView(),
        ]);
    }

    #[Route('/static-page/preview/{id}', name: 'static_preview')]
    public function preview(Request $request, #[CurrentUser] User $user, SiteRepository $siteRepository, StaticPageManager $manager, StaticPage $page): Response
    {
        $profile = $user->getProfile();
        $site = $profile->getSite();
        $site = $siteRepository->find($site->getId());
        $content = $manager->getHTML($page);
        $path =  $manager->getPath($page);
        return $this->render('text_converter/preview.html.twig', [
            'htmlContent' => $content ?? '<p>No content received</p>',
            'path' => $path,
        ]);
    }

    #[Route('/static-page/publish/{id}', name: 'static_publish')]
    public function publish(Request $request, #[CurrentUser] User $user, SiteRepository $siteRepository, StaticPageManager $manager, StaticPage $page): Response
    {
        $profile = $user->getProfile();
        $createdBy = $user->getUserIdentifier();
        $site = $profile->getSite();
        $site = $siteRepository->find($site->getId());
        $content = $manager->getHTML($page);
        $saved = $manager->publishPage($page, $content, $createdBy, $profile);
        if ($saved) {
            $this->addFlash('success', 'Page published successfully!');
            return $this->redirectToRoute('static_index');
        } else {
            $this->addFlash('danger', 'Page not saved');
            return $this->redirectToRoute('static_index');
        }
    }

    #[Route('/static-page/copy/{id}', name: 'static_copy')]
    public function copyLink(StaticPage $page,  StaticPageManager $manager): JsonResponse
    {
        $url = $manager->getMediaLink($page) ;
        return new JsonResponse(['url' => $url]);
    }
}
