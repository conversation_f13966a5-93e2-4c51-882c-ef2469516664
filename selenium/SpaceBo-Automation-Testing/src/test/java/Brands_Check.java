import org.openqa.selenium.WebElement;
import org.testng.Assert;
import org.testng.annotations.Test;
import java.util.List;
public class Brands_Check {
    Methods user = new Methods();
    @Test(groups = {"Brands Check"})
    public void AllBrands() {

        user.login();
        String[] expectedTexts = {
                "ABARTH", "ALFA ROMEO", "CHRYSLER", "CITROËN", "DODGE",
                "DS", "FIAT", "FIAT PROFESSIONAL", "GLO<PERSON>L", "JEEP",
                "LANCIA", "MASERATI", "OPEL", "PEUGEOT","RAM","SPOTICAR","VAUXHALL"
        };
        Locators.Media_Library().click();
        Locators.APP().click();
        List<WebElement> App_items = Locators.Leaf_APP();
        Assert.assertEquals(App_items.size(), expectedTexts.length, "Mismatch in number of list items!");
        for (int i = 0; i < App_items.size(); i++) {
            WebElement element = App_items.get(i);
            // Try using getText(), if it doesn't work, try getAttribute("textContent")
            String actualText = element.getText().trim();  // Use trim() to avoid extra whitespace

            // If getText() doesn't return the text, try to fetch the text content
            if (actualText.isEmpty()) {
                actualText = element.getAttribute("textContent").trim();
            }
            String expectedText = expectedTexts[i];
            Assert.assertEquals(actualText, expectedText, "Text does not match for item " + (i + 1));
        }
        Locators.APP().click();
       Locators.WEB().click();
        List<WebElement> WEB_items = Locators.Leaf_Web();
        Assert.assertEquals(WEB_items.size(), expectedTexts.length, "Mismatch in number of list items!");
        for (int i = 0; i < WEB_items.size(); i++) {
            WebElement element = WEB_items.get(i);
            // Try using getText(), if it doesn't work, try getAttribute("textContent")
            String actualText = element.getText().trim();  // Use trim() to avoid extra whitespace
            // If getText() doesn't return the text, try to fetch the text content
            if (actualText.isEmpty()) {
                actualText = element.getAttribute("textContent").trim();
            }
            String expectedText = expectedTexts[i];
            Assert.assertEquals(actualText, expectedText, "Text does not match for item " + (i + 1));
        }
        Locators.WEB().click();
      user.exit();
    }

}
