<?php

namespace App\Entity;

use App\Repository\ChannelRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use App\Validator\UniqueChannelName;
use App\Validator\Constraints as AppAssert;

#[ORM\Entity(repositoryClass: ChannelRepository::class)]
#[UniqueEntity(
    fields: ['name'],
    message: 'This channel name is already in use.',
    errorPath: 'name'
)]

class Channel
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private int $id;

    #[ORM\Column(length: 255)]
    private string $name;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $description = null;

    /**
     * @var Collection<int, MediaDirectory>
     */
    #[ORM\OneToMany(mappedBy: 'channel', targetEntity: MediaDirectory::class)]
    private Collection $mediaDirectories;

    #[ORM\ManyToOne(inversedBy: 'channel', cascade: ['persist'])]
    private ?ChannelType $channelType = null;

    public function __construct()
    {
        $this->mediaDirectories = new ArrayCollection();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    /**
     * @return Collection<int, MediaDirectory>
     */
    public function getMediaDirectories(): Collection
    {
        return $this->mediaDirectories;
    }

    public function addMediaDirectory(MediaDirectory $mediaDirectory): self
    {
        if (!$this->mediaDirectories->contains($mediaDirectory)) {
            $this->mediaDirectories->add($mediaDirectory);
            $mediaDirectory->setChannel($this);
        }

        return $this;
    }

    public function removeMediaDirectory(MediaDirectory $mediaDirectory): self
    {
        if ($this->mediaDirectories->removeElement($mediaDirectory)) {
            // set the owning side to null (unless already changed)
            if ($mediaDirectory->getChannel() === $this) {
                $mediaDirectory->setChannel(null);
            }
        }

        return $this;
    }

    public function getChannelType(): ?ChannelType
    {
        return $this->channelType;
    }

    public function setChannelType(?ChannelType $channelType): self
    {
        $this->channelType = $channelType;

        return $this;
    }
}
