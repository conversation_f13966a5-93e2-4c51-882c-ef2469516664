import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.openqa.selenium.WebDriver;
import java.time.Duration;

public class Methods {
    WebDriver driver = TestUtilities.getDriver();
    ConfigReader configReader = TestUtilities.getConfigReader();
    public void login() {
            driver.get(configReader.getProperty("integration_base_url"));
            driver.manage().window().maximize();
            WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(4));
            WebElement Username = wait.until(ExpectedConditions.visibilityOfElementLocated(By.id("username")));
            Username.sendKeys(configReader.getProperty("username"));
            WebElement Password = driver.findElement(By.id("password"));
            Password.sendKeys(configReader.getProperty("password"));
            WebElement Submit = driver.findElement(By.id("signOnButton"));
            Submit.click();
    }
    public boolean isTextPresent(WebElement element, String expectedText) {
        // Get the text content of the passed element
        String elementText = element.getText();
        // Check if the expected text is present in the element text
        return elementText.equals(expectedText);
    }
    public void exit(){
        driver.quit();
    }

    public void Scroll_Down(){
        JavascriptExecutor js = (JavascriptExecutor) driver;
        js.executeScript("window.scrollBy(0,document.body.scrollHeight)");
    }
    public void Scroll_Up(){
        JavascriptExecutor js = (JavascriptExecutor) driver;
        js.executeScript("window.scrollBy(0,0)");
    }
    public void Refresh_Page(){
        driver.navigate().refresh();
    }
    public WebElement explicitWaitVisible(By locator) {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(10));
        return wait.until(ExpectedConditions.visibilityOfElementLocated(locator));
    }

}
