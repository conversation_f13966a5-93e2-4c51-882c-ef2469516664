<div class="modal fade" id="edit-media-modal" tabindex="-1" role="dialog" aria-labelledby="edit" aria-hidden="true">
	<form id="edit-media-form" onsubmit="return false;">
		<div class="modal-dialog modal-lg" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="delete">{{ 'edit_image'|trans }}</h5>
					<button class="close" type="button" data-bs-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">×</span>
					</button>
				</div>
				{{ form_start(formEdit)}}
				<div class="modal-body">
					<div class="alert alert-secondary mb-4 mx-3 text-center d-none" id="alert-loading" role="alert">
						<img src="/images/loading.png" alt="loading" class="loading" width="35"/>
					</div>
					<div class="alert alert-success mb-4 mx-3 d-none" id="alert-success" role="alert">
						<span class="title"></span>
					</div>
					<div class="alert alert-danger mb-4 mx-3 d-none" id="alert-error" role="alert">
						<span class="title"></span>
					</div>
					 <div id="uppy-container-edit" class="mx-4"></div>
					 <ul id="uppy-messages-edit" class="text-danger"></ul>
					 {% include "media/modals/media/format_type_content.html.twig" %}
					<div class="formulaire mx-4">
						<div class="form-group">
							<div class="row">
								<input type="hidden" id="edit_medias_form_id" name="edit_medias_form_id">
								{{ form_row(formEdit._token) }}
								<div class="col-md-3">
									{{ form_label(formEdit.name) }}
									<span class="mandatory">*</span>
								</div>
								<div class="col-md-7">
									{{ form_widget(formEdit.name, {'attr': {'class': 'edit_medias_form_name'}}) }}
								</div>
							</div>
						</div>

						<div class="form-group">
							<div class="row">
								<div class="col-md-3">
									{{ form_label(formEdit.textAlt) }}
								</div>
								<div class="col-md-7">
									{{ form_widget(formEdit.textAlt, {'attr': {'class': 'edit_medias_form_textAlt'}}) }}
								</div>
							</div>
						</div>

						<div class="form-group">
							<div class="row">
								<div class="col-md-3">
									{{ form_label(formEdit.copyright) }}
								</div>
								<div class="col-md-7">
									{{ form_widget(formEdit.copyright, {'attr': {'class': 'edit_medias_form_copyright'}}) }}
								</div>
							</div>
						</div>

						<div class="form-group">
							<div class="row">
								<div class="col-md-3">
									{{ form_label(formEdit.comment) }}
								</div>
								<div class="col-md-7">
									{{ form_widget(formEdit.comment, {'attr': {'class': 'edit_medias_form_comment'}}) }}
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<button class="btn btn-info save-edit-media btn-actions" id="media-form-edit-button"><i class="fas fa-save ms-1"></i> {{ 'edit'|trans }}</button>
					<button class="btn btn-secondary btn-actions" type="button" data-bs-dismiss="modal"><i class="fas fa-times-circle ms-1"></i> {{ 'btn_cancel'|trans }}</button>
				</div>
				{{ form_end(formEdit, {render_rest: false}) }}
			</div>
		</div>
	</form>
</div>