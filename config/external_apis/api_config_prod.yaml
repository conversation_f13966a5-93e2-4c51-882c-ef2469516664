api_groups:
  garage_management:
    name: "Garage Management API"
    description: "Garage Management API to get all vehicle's information"
    base_url: "https://api-proc-user-veh.space.awsmpsa.com/"
    endpoints:
      list_v2_vehicles:
        name: "V2 Vehicles by UserID"
        path: "v2/vehicles"
        method: "GET"
        description: >
          Get all the v2 vehicles as per user ID
        parameters:
          - name: "userId"
            type: "string"
            required: true
            header: true
            description: "User ID"
          - name: "country"
            type: "string"
            required: true
            description: "Country Code"
          - name: "language"
            type: "string"
            required: true
            description: "Language Code"
          - name: "brand"
            type: "string"
            required: true
            description: "Brand Code"
      info_v2_vehicles:
        name: "V2 Vehicles Info"
        path: "v2/vehicles/info"
        method: "GET"
        description: >
          Get all the v2 vehicle info
        parameters:
          - name: "userId"
            type: "string"
            required: true
            header: true
            description: "User ID"
          - name: "country"
            type: "string"
            required: true
            description: "Country Code"
          - name: "language"
            type: "string"
            required: true
            description: "Language Code"
          - name: "source"
            type: "string"
            required: true
            description: "Source"
          - name: "id"
            type: "string"
            required: false
            description: "vehicle ID"
          - name: "vin"
            type: "string"
            header: true
            required: false
            description: "VIN"

  sams_data:
    name: "SAMS Data API"
    description: "Space SAMS Data Management APIs"
    base_url: "https://api-sys-sams-data.space.awsmpsa.com"
    endpoints:
      catalog:
        name: "Catalog API"
        path: "/v1/catalog"
        method: "GET"
        description: "Retrieve catalog information for user and vehicle"
        parameters:
          - name: "userId"
            type: "string"
            required: true
            header: true
            description: "User DB ID identifier"
          - name: "country"
            type: "string"
            required: true
            description: "Country code"
          - name: "language"
            type: "string"
            required: true
            description: "Language code"
          - name: "brand"
            type: "string"
            required: true
            description: "Vehicle brand"
          - name: "vin"
            type: "string"
            required: true
            header: true
            description: "Vehicle identification number"

      contrib_by_ids:
        name: "Contributions by ID's"
        path: "/v1/sams/contrib/getInfoByProduct"
        method: "GET"
        description: "Get contribs by ID's"
        parameters:
          - name: "productIds"
            type: "string"
            required: true
            description: "Product IDs"
          - name: "brand"
            type: "string"
            required: false
            description: "Brand Code"
          - name: "culture"
            type: "string"
            required: false
            description: "LanguageCode-CountryCode"

      contrib_by_id:
        name: "Contribution by ID"
        path: "/v1/sams/contrib/getInfoByProduct/${productId}"
        method: "GET"
        description: "Get contribs by ID's"
        parameters:
          - name: "productId"
            type: "string"
            required: true
            query: false
            description: "Product IDs"
          - name: "brand"
            type: "string"
            required: false
            description: "Brand Code"
          - name: "culture"
            type: "string"
            required: false
            description: "LanguageCode-CountryCode"
          - name: "source"
            type: "string"
            required: false
            description: "Source"

  corvet:
    name: "Corvet API"
    description: "This service is responsible for retrieving vehicle data from the Corvet API"
    base_url: "https://api-sys-corvet-data.space.awsmpsa.com"
    endpoints:
      get_vehicle_data:
        name: "Get Vehicle Data"
        path: "/v1/corvet/${vin}/data"
        method: "GET"
        description: >
          Get vehicle data from Corvet API
        parameters:
          - name: "vin"
            type: "string"
            required: true
            query: false
            description: "Vehicle identification number"

