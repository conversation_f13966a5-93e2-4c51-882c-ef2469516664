<?php

namespace App\Repository;

use App\Entity\RpoLabel;
use App\Entity\VehicleLabel;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Persistence\ManagerRegistry as PersistenceManagerRegistry;

/**
 * @method LcdvLabel|null find($id, $lockMode = null, $lockVersion = null)
 * @method LcdvLabel|null findOneBy(array $criteria, array $orderBy = null)
 * @method LcdvLabel[]    findAll()
 * @method LcdvLabel[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class RpoLabelRepository extends ServiceEntityRepository
{
    public function __construct(PersistenceManagerRegistry $registry)
    {
        parent::__construct($registry, RpoLabel::class);
    }

    public function deleteByBrandAndLabel($brand, $label) {
        try {
            return $this->createQueryBuilder('vehicle_model')
                ->delete()
                ->where('vehicle_model.label = :label')
                ->andWhere('vehicle_model.brand = :brand')
                ->setParameter('label', $label)
                ->setParameter('brand', $brand)
                ->getQuery()->execute();
        }
        catch (\Exception $e) {
            return 0;
        }
    }

    public function checkLabelUsageForUpdateValidation($label, $brand, $ids)
    {
        return $this->createQueryBuilder('l')
            ->where('l.label = :label')
            ->andWhere('l.brand = :brand')
            ->andWhere('l.id not in (:ids)')
            ->setParameter('label', $label)
            ->setParameter('brand', $brand)
            ->setParameter('ids', $ids)
            ->getQuery()
            ->getResult();
    }

    public function getRPOs($rpo)
    {
        $rpo = substr($rpo, 0, 2);

        return $this->createQueryBuilder('l')
            ->where("l.rpo LIKE :rpo")
            ->setParameter('rpo', $rpo.'%')
            ->getQuery()
            ->getResult();
    }

    /**
     * @param  string  $rpo
     *
     * @return \Doctrine\Common\Collections\ArrayCollection
     */
    public function getRpoLabelsWhereInRpo(string $rpo): ArrayCollection
    {
        // Prepare the parameter
        $rpos = [];

        foreach (range(2, strlen($rpo)) as $length) {
            $rpos[] = substr($rpo, 0, $length);
        }

        // Query
        $qb = $this->createQueryBuilder('l');

        $results = $qb
            ->where($qb->expr()->in('l.rpo', ':rpos'))
            ->setParameter('rpos', array_filter($rpos))
            ->getQuery()
            ->getResult();

        return new ArrayCollection($results);
    }

    public function findOneRpoLabelByBrand($fields)
    {
        return $this->createQueryBuilder('l')
            ->andWhere('l.rpo =:rpo')
            ->join('l.vehicleLabel', 'lv')
            ->andWhere('lv.brand = :brand')
            ->setParameter('rpo', $fields['rpo'])
            ->setParameter('brand', (isset($fields['vehicleLabel']) && $fields['vehicleLabel'] instanceof VehicleLabel) ? $fields['vehicleLabel']->getBrand() : null)
            ->getQuery()
            ->getResult();
    }
}