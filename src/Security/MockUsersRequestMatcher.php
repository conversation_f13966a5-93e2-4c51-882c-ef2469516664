<?php

namespace App\Security;

use App\Helpers\LoggerTrait;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestMatcherInterface;
use Symfony\Component\HttpKernel\KernelInterface;

/**
 *
 * Class used by the security firewall to check if we want to load mockUsers security firewall
 *
 */
class MockUsersRequestMatcher implements RequestMatcherInterface
{
    use LoggerTrait;

    /**
     * Construct user entiy from the xml data
     * @param string    
     */
    public function __construct(private bool $useMockUsers, private string $environment)
    {
    }

    /**
     * Construct user entiy from the xml data
     * @param Request $request
     * @return bool
     */
    public function matches(Request $request) : bool
    {   
        if(!($this->useMockUsers || $this->environment === 'dev')){
            $this->logger->debug(__METHOD__.': matches', [
                'request' => $request->query->all(),
                'useMockUsers' => $this->useMockUsers,
                'environment' => $this->environment
            ]);
        }
        return $this->useMockUsers || $this->environment === 'dev';
    }
}
