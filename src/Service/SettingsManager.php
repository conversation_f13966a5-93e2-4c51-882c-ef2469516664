<?php

namespace App\Service;

use App\Entity\FeatureSetting;
use App\Helpers\CultureHelper;
use App\Security\User;
use App\Helpers\WSResponse;
use App\Helpers\LoggerTrait;
use App\Model\SettingsModel;
use App\Service\AWSS3Service;
use <PERSON><PERSON><PERSON>gmann\Diff\Differ;
use App\Repository\WidgetRepository;
use App\Repository\FeatureRepository;
use App\Repository\FeatureSettingRepository;
use Doctrine\Common\Collections\Collection;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use Webmozart\Assert\Assert;

/**
 * SettingsManager
 */
class SettingsManager
{
    use LoggerTrait;

    const XP = [
        'AP',
        'OP',
        'AC',
        'DS',
        'VX'
    ];
    const GLOBAL_BRAND = 'XX';

    public function __construct(
        private SettingsService $settingsService,
        private FeatureRepository $featureRepository,
        private FeatureSettingRepository $featureSettingRepository,
        private WidgetRepository $widgetRepository,
        private AWSS3Service $s3Service,
        private SerializerInterface $serializer
    ) {}

    public function getSettings(User $user, ?array $sources): array
    {
        $profile = $user->getProfile();
        $settings = [];
        if ($profile->isSuperAdministrator()) {
            $settings = $this->getGlobalSettings($sources);
            $iconCode = 'xx';
            $iconUrl = 'images/' . $iconCode . '/global.png';
        } elseif ($profile->isLocalAdministrator()) {
            $brand = $profile->getBrand()->getCode();
            $iconCode = strtolower($brand);
            $settings = $this->getBrandSettings(/** @scrutinizer ignore-type */ $brand, $sources);
            $iconUrl = 'images/' . $iconCode . '/logo.png';
        } else {
            $site = $profile->getSite();
            $brand = $site->getBrand()->getCode();
            $country = $site->getCountry()->getCode();
            $language = $site->getPreferedLanguage()->getCode();
            $languages = $this->getCodesFromLanguages($site->getLanguages());
            $iconCode = $language;
            $settings = $this->getLocalSettings(/** @scrutinizer ignore-type */ $brand, /** @scrutinizer ignore-type */ $country, /** @scrutinizer ignore-type */ $languages, $sources, $language);
            $iconUrl = 'images/flags/' . $iconCode . '.svg';
        }
        $region = $profile->getSite()->getRegion()->getCode() ?? null;
        $settings = $this->getUserManualData($region, $settings);
        return [
            'settings' => $settings,
            'iconUrl' => $iconUrl,
        ];
    }

    public function saveSettings(User $user, ?array $sources): WSResponse
    {
        try {
            $settings = $this->getSettings($user, $sources)['settings'];
            return $this->settingsService->insertSettings($user, $settings);
        } catch (\Exception $e) {
            $this->logger->error("Error occured while sync settings : {$e->getMessage()}");
            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    public function createPacket(User $user, ?array $sources): array
    {
        try {
            $settingsArray = $this->getSettings($user, $sources);
            Assert::notEmpty($settingsArray['settings'], 'Settings not found');
            Assert::isInstanceOf($settingsArray['settings'][0], SettingsModel::class, 'Settings not found');
            $settingsModel = $settingsArray['settings'][0];
            $settingsData = $this->settingsService->getFormatedFeatureParameters($settingsModel->getSettingsData());
            $settingsJson = json_encode($settingsData, JSON_PRETTY_PRINT);
            $iconUrl = $settingsArray['iconUrl'];
            $fileUrl = $this->settingsService->getFileName($user, $settingsModel);
            $uploadedSettings = $this->settingsService->getUploadedJson($fileUrl);
            $differ = new Differ();
            $packet = [
                'diff' => $differ->diff($uploadedSettings, $settingsJson),
                'currentJson' => $settingsJson,
                'releasedJson' => $uploadedSettings,
                'path' => $fileUrl,
                'iconUrl' => $iconUrl,
                'code' => Response::HTTP_FOUND,
            ];
            return $packet;
        } catch (\Exception $e) {
            return ['code' => Response::HTTP_NO_CONTENT];
        }
    }

    public function getLocalSettings(
        string $brand, 
        string $country, 
        array $languages, 
        array $sources, 
        string $defaultLanguage = ''
    ): array
    {
        $features = $this->featureRepository->findAllLocalSettings($brand, $country, $languages, $sources);
        $widgets = $this->widgetRepository->findAllWidgetSettings($brand, $country, $sources);

        $globalFeatures = $this->featureRepository->findAllGlobalSettings($sources);
        $brandFeatures = $this->featureRepository->findAllBrandSettings($brand, $sources);

        $settings = [];
        $featuresSettings = [];
        if (count($features) == 0) {
            $culture = $defaultLanguage . '-' . $country;
            foreach ($sources as $source) {
                $settings['widgets'] = $this->findWidgetByCountryBrandSource(
                    $brand,
                    $country,
                    $source,
                    $widgets
                );
                $featuresSettings[] = (new SettingsModel())
                    ->setBrand($brand)
                    ->setSource($source)
                    ->setCulture($culture)
                    ->setSettingsData($settings);
                $settings = [];
            }
        } else {
            foreach ($features as $feature) {
                $featureName = str_replace(' ', '_', $feature['label']);

                foreach ($globalFeatures as $global) {
                    if ($global['label'] === $feature['label'] && $global['name'] === $feature['name']) {
                        $globalFeature = $global;
                        break;
                    }
                }

                foreach ($brandFeatures as $brand) {
                    if ($brand['label'] === $feature['label'] && $brand['name'] === $feature['name']) {
                        $brandFeature = $brand;
                        break;
                    }
                }

                $settings[$featureName][$feature['name']] = in_array($feature['name'], ['enabled', 'isEarlyAdopters'])
                    ? $feature['value']
                    : (
                        !empty($feature['value'])
                        ? $feature['value']
                        : (
                            isset($brandFeature) && !empty($brandFeature['value'])
                            ? $brandFeature['value']
                            : (isset($globalFeature) ? $globalFeature['value'] : "")
                        )
                    );

                $culture = $feature['language'] . '-' . $feature['country'];

                $key = $this->findSettingsKey($feature['brand'], $culture, $feature['source'], $featuresSettings);
                if (isset($key)) {
                    $featuresSettings[$key] = $featuresSettings[$key]->addSetting(
                        $settings[$featureName],
                        $featureName
                    );
                } else {
                    $settings['widgets'] = $this->findWidgetByCountryBrandSource(
                        $feature['brand'],
                        $feature['country'],
                        $feature['source'],
                        $widgets
                    );
                    $featuresSettings[] = (new SettingsModel())
                        ->setBrand($feature['brand'])
                        ->setSource($feature['source'])
                        ->setCulture($culture)
                        ->setSettingsData($settings);
                }
                $settings = [];
            }
        }
        return $featuresSettings;
    }

    public function getGlobalSettings(array $sources): array
    {
        $features = $this->featureRepository->findAllGlobalSettings($sources);

        $settings = [];
        $featuresSettings = [];
        if (count($features) == 0) {
            foreach ($sources as $source) {
                $featuresSettings[] = (new SettingsModel())
                    ->setBrand('XX')
                    ->setSource($source)
                    ->setCulture('')
                    ->setSettingsData([]);
            }
        } else {
            foreach ($features as $feature) {
                $featureName = str_replace(' ', '_', $feature['label']);
                $settings[$featureName][$feature['name']] = $feature['value'];

                $key = $this->findSettingsKey($feature['brand'], null, $feature['source'], $featuresSettings);
                if (isset($key)) {
                    $featureSetting = $this->featureSettingRepository->findOneBy(['name' => $feature['label']]);
                    $fieldExist = $this->isFieldExist($featureSetting, $feature['name']);

                    if ($fieldExist) {
                        $featuresSettings[$key] = $featuresSettings[$key]->addSetting(
                            $settings[$featureName],
                            $featureName
                        );
                    }
                } else {
                    $featuresSettings[] = (new SettingsModel())
                        ->setBrand($feature['brand'])
                        ->setSource($feature['source'])
                        ->setCulture('')
                        ->setSettingsData($settings);
                }
                $settings = [];
            }
        }
        return $featuresSettings;
    }

    public function getBrandSettings(string $brand, array $sources): array
    {
        $features = $this->featureRepository->findAllBrandSettings($brand, $sources);
        $featuresGlobal = $this->featureRepository->findAllGlobalSettings($sources);

        $settings = [];
        $featuresSettings = [];
        if (count($features) == 0) {
            foreach ($sources as $source) {
                $featuresSettings[] = (new SettingsModel())
                    ->setBrand($brand)
                    ->setSource($source)
                    ->setCulture('')
                    ->setSettingsData([]);
            }
        } else {
            foreach ($features as $feature) {
                $featureName = str_replace(' ', '_', $feature['label']);

                foreach ($featuresGlobal as $global) {
                    if ($global['label'] == $feature['label'] && $global['name'] == $feature['name']) {
                        $globalFeature = $global;
                        break;
                    }
                }

                $settings[$featureName][$feature['name']] = in_array($feature['name'], ['enabled', 'isEarlyAdopters'])
                    ? $feature['value']
                    : (!empty($feature['value'])
                        ? $feature['value']
                        : (isset($globalFeature) ? $globalFeature['value'] : ""));

                $key = $this->findSettingsKey($feature['brand'], null, $feature['source'], $featuresSettings);
                if (isset($key)) {
                    $featureSetting = $this->featureSettingRepository->findOneBy(['name' => $feature['label']]);
                    $fieldExist = $this->isFieldExist($featureSetting, $feature['name']);

                    if ($fieldExist) {
                        $featuresSettings[$key] = $featuresSettings[$key]->addSetting(
                            $settings[$featureName],
                            $featureName
                        );
                    }
                } else {
                    $featuresSettings[] = (new SettingsModel())
                        ->setBrand($feature['brand'])
                        ->setSource($feature['source'])
                        ->setCulture('')
                        ->setSettingsData($settings);
                }
                $settings = [];
            }
        }
        return $featuresSettings;
    }

    private function findWidgetByCountryBrandSource(
        ?string $brand,
        ?string $country,
        string $source,
        array $data
    ): array {
        return array_filter($data, function ($d) use ($brand, $country, $source) {
            return $d['country'] == $country && $d['brand'] == $brand && $d['source'] == $source;
        });
    }

    private function findSettingsKey(
        ?string $brand,
        ?string $culture,
        string $source,
        array $data
    ) {
        foreach ($data as $key => $value) {
            if ($value->getBrand() == $brand && $value->getCulture() == $culture && $value->getSource() == $source) {
                return $key;
            }
        }
    }

    private function isFieldExist(?FeatureSetting $featureSetting, string $featureName): bool
    {
        if (!$featureSetting) {
            return true;
        }

        $content = $featureSetting->getContent();
        $fields = isset($content['form']['fields']) ? $content['form']['fields'] : [];

        $fieldsNames = array_merge(
            ['code', 'enabled', 'isEarlyAdopters'],
            array_map(function ($field) {
                return str_replace(' ', '', $field['name']);
            }, $fields)
        );

        return in_array($featureName, $fieldsNames);
    }

    /**
     * @param Collection $languages
     *
     * @return array
     */
    private function getCodesFromLanguages(Collection $languages): array
    {
        $codes = [];
        foreach ($languages as $language) {
            $codes[] = $language->getCode();
        }
        return $codes;
    }

    private function getUserManualData(string $region, array $settings = [])
    {
        foreach ($settings as $setting) {
            $userManualLocalUrl = $setting->getSettingsData()['User_Manual_Local']['url'] ?? null;
            if ($setting->getBrand() != self::GLOBAL_BRAND && empty($userManualLocalUrl)) {
                $globalSettings = $this->getGlobalSettings([$setting->getSource()]);
                if (in_array($setting->getBrand(), self::XP)) {
                    $this->getXpUserManualSettings($globalSettings, $setting);
                } else {
                    $this->getXfUserManualSettings($globalSettings, $setting, $region);
                }
            }
        }
        return $settings;
    }

    private function getXpUserManualSettings($globalSettings, &$setting)
    {
        $globalUrl = '';
        $result = [];
        foreach ($globalSettings as $globalSetting) {
            if ($globalSetting->getBrand() == self::GLOBAL_BRAND) {
                $globalUrl = $globalSetting->getSettingsData()['User_Manual_Global'][$setting->getBrand()] ?? null;
                if (isset($setting->getSettingsData()['User_Manual_Local'])) {
                    $result = $setting->getSettingsData();
                    $language = CultureHelper::getLanguage($setting->getCulture());
                    $country = CultureHelper::getCountry($setting->getCulture());
                    $result['User_Manual_Local']['url'] = str_replace('{$culture}', $language . '_' . $country, $globalUrl);
                    $setting->setSettingsData($result);
                    $result = [];
                }
            }
        }
    }

    private function getXfUserManualSettings($globalSettings, &$setting, $region)
    {
        $globalUrl = '';
        foreach ($globalSettings as $globalSetting) {
            if ($globalSetting->getBrand() == self::GLOBAL_BRAND) {
                $regionKey = strtolower($region) . 'Url';
                $globalUrl = $globalSetting->getSettingsData()['User_Manual_Global'][$regionKey] ?? null;
                break;
            }
        }
        if (isset($setting->getSettingsData()['User_Manual_Local'])) {
            $result = $setting->getSettingsData();
            $language = CultureHelper::getLanguage($setting->getCulture());
            $result['User_Manual_Local']['url'] = str_replace('{$lang}', $language, $globalUrl);
            $setting->setSettingsData($result);
        }
    }
}