{% extends '_layout/layout.html.twig' %}
{% block title %}
	{{ 'channel_management' | trans | capitalize }}
{% endblock %}
{% block content %}
	<h1 class="h3 mb-4 text-gray-800">LogIncidents</h1>


	<div class="card shadow-sm mb-4">
		<div
			class="card-body">
			<!-- Filters -->
			{{ form_start(form, {'attr': {'class': 'mb-2'}}) }}
			<div class="row">
				<div class="col-md-3 col-sm-12 pt-2">
					{{ form_label(form.vin) }}
				</div>
				<div class="col-md-3 col-sm-12 pt-2">
					{{ form_label(form.email) }}
				</div>
				<div class="col-md-3 col-sm-12 pt-2">
					{{ form_label(form.date) }}
				</div>
				<div class="col-md-3 col-sm-12 pt-2">
					{{ form_label(form.end_date) }}
				</div>
			</div>
			<div class="row">
				<div class="col-md-3">
					<div class="form-group">
						{{ form_widget(form.vin) }}
					</div>
				</div>
				<div class="col-md-3">
					<div class="form-group">
						{{ form_widget(form.email) }}
					</div>
				</div>
				<div class="col-md-3">
					<div class="form-group">
						{{ form_widget(form.date) }}
					</div>
				</div>
				<div class="col-md-3">
					<div class="form-group">
						{{ form_widget(form.end_date) }}
					</div>
				</div>
				<div class="col-md-1 text-right">
					<button class="btn btn-info" type="submit">{{ button_label|default('filter') |trans }}</button>
				</div>
			</div>
			{{ form_end(form) }}

			<div class="table-responsive">
				<table class="table table-bordered cell-border compact stripe dataTable" id="dataTable" width="100%" cellspacing="0" role="grid" aria-describedby="dataTable_info" style="font-size: 14px; width: 100%;">
					<thead>
						<tr>
							<th class="">
								<small class="font-weight-bold">Ticket ID</small>
							</th>
							<th class="">
								<small class="font-weight-bold">Site Code</small>
							</th>
							<th class="">
								<small class="font-weight-bold">VIN</small>
							</th>
							<th class="">
								<small class="font-weight-bold">Title</small>
							</th>
							<th class="">
								<small class="font-weight-bold">Date</small>
							</th>
							<th class=" text-right">
								<small class="font-weight-bold">Actions</small>
							</th>
						</tr>
					</thead>
					<tbody class="modals_triggers"></tbody>
				</table>
			</div>
		</div>
	</div>
{% endblock %}

{% block script %}
	{{ parent() }}
 <script src="{{ asset('js/datatables/jquery.dataTables.min.js') }}"></script>
	 <script src="{{ asset('js/datatables/dataTables.bootstrap4.min.js') }}"></script>
	 <script src="{{ asset('js/date-picker/flatpickr.min.js') }}"></script>
	 <script>
	       $(document).ready(function() {
    // Debug log to verify initialization
    console.log("Initializing DataTable...");
    
    // Initialize DataTable with CSRF token support
    let table = $('#dataTable').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "{{ path('log_incident_paginate') }}",
            "type": "POST",
            "headers": {
                'X-Requested-With': 'XMLHttpRequest',
                // Include CSRF token if you have CSRF protection enabled
                // 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            "data": function(d) {
                console.log("Sending request with data:", d);
                // Add filter values
                d.vin = $('#log_incident_filterer_vin').val();
                d.email = $('#log_incident_filterer_email').val();
                d.creation_date = $('#log_incident_filterer_date').val();
                d.end_date = $('#log_incident_filterer_end_date').val();
                
                return d;
            },
            "error": function(xhr, error, thrown) {
                console.error("DataTable AJAX error:", error, thrown);
                console.log("Response:", xhr.responseText);
                alert("An error occurred while loading data. Please check the console for details.");
            },
            "dataSrc": function(json) {
                console.log("Received response:", json);
                if (json.error) {
                    console.error("Server error:", json.message);
                    return [];
                }
                return json.data || [];
            }
        },
        "columns": [
            { "data": "incidentid", "defaultContent": "" },
            { "data": "site_code", "defaultContent": "" },
            { "data": "vin", "defaultContent": "" },
            { "data": "title", "defaultContent": "" },
            { "data": "creation_date", "defaultContent": "" },
            { 
                "data": null,
                "defaultContent": "",
                "render": function(data, type, row) {
                    if (!row || !row.incidentid) return "N/A";
                    
                    let url = "{{ path('log_incident_show', {'id': 'ID_PLACEHOLDER'}) }}";
                    url = url.replace('ID_PLACEHOLDER', row.incidentid);
                    
                    return '<a href="' + url + '" class="btn btn-sm btn-warning"><small>View</small></a>';
                }
            }
        ],
        "rowCallback": function(row, data) {
            if (!data) return;
            
            if (data.incidentid) {
                $('td:eq(0)', row).html(data.incidentid);
            }
            
            if (data.bgcTranslationStatus) {
                $('td:eq(0)', row).css('background-color', data.bgcTranslationStatus);
            }
            
            if (data.translationStatusText) {
                $('td:eq(0)', row).attr('title', data.translationStatusText);
            }
        },
        "order": [[0, "desc"]],
        "pageLength": 25,
        "paging": true,
        "info": true,
        "searching": true,
        "responsive": true,
        "stateSave": true,
        "autoWidth": true,
        "dom": 'lfrtip', // Standard layout with search/filter at top
        "columnDefs": [
            {"orderable": false, "targets": [5]}
        ]
    });

    // Handle truncation after each draw
    table.on('draw', function() {
        console.log("Table redraw event triggered");
        $(".truncate").each(function() {
            if ($(this).text().trim().length > 100) {
                let text = $(this).text().trim().substring(0, 100) + '...';
                $(this).html(text);
            }
        });
    });
    
    // Initialize datepicker plugins
    $('#log_incident_filterer_date').datepicker({
        clearBtn: true,
        format: "dd-mm-yyyy",
        autoclose: true
    });

    $('#log_incident_filterer_end_date').datepicker({
        clearBtn: true,
        format: "dd-mm-yyyy",
        autoclose: true
    });
});
	    </script>
{% endblock %}
