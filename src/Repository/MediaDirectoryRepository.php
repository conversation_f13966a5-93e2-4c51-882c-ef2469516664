<?php

namespace App\Repository;

use App\Entity\MediaDirectory;
use App\Entity\Profile;
use App\Service\MediaManager;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use PDO;

/**
 * @method MediaDirectory|null find($id, $lockMode = null, $lockVersion = null)
 * @method MediaDirectory|null findOneBy(array $criteria, array $orderBy = null)
 * @method MediaDirectory[]    findAll()
 * @method MediaDirectory[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class MediaDirectoryRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, MediaDirectory::class);
    }

    public function getSourceParent(): ?array
    {
        return $this->findBy(['parentDirectory' => null]);
    }

    public function getMediaDirectories(string $brand, string $country, Profile $profile): array
    {
        $type = ($brand == 'XX' && $country != 'XX') ? 'GLOBAL' : 'BRAND';
        $query = $this->createQueryBuilder('d')
            ->select('d.id, d.path, d.label, d.name AS text, pd.id as parent, cht.name as channel')
            ->join('d.channel', 'ch')
            ->leftjoin('d.parentDirectory', 'pd')
            ->join('ch.channelType', 'cht');

        if ($profile->isCentralAdministrator()) {
            $query->leftJoin('d.site', 's')
                ->leftJoin('s.country', 'c')
                ->where('cht.name = :channelType')
                ->andWhere(
                    $query->expr()->orX(
                        'd.brand = :brand',
                        'd.site IS NULL',
                        'd.parentDirectory IS NULL'
                    )
                )
                ->setParameter('channelType', $type)
                ->setParameter('brand', $brand);
        }

        if ($profile->isLocalProfile()) {
            $query->leftJoin('d.site', 's')
                ->leftJoin('s.country', 'c')
                ->where('cht.name = :channelType')
                ->andWhere(
                    $query->expr()->orX(
                        $query->expr()->andX(
                            'd.brand = :brand',
                            'c.code = :country'
                        ),
                        $query->expr()->andX(
                            'd.site IS NULL',
                            'd.brand = :brand'
                        ),
                        'd.parentDirectory IS NULL'
                    )
                )
                ->setParameter('channelType', $type)
                ->setParameter('brand', $brand)
                ->setParameter('country', $country);
        }

        return $query->orderBy('d.name', 'asc')
            ->getQuery()
            ->getScalarResult();
    }
}
