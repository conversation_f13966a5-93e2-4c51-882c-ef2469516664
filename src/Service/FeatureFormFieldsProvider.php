<?php

namespace App\Service;

use App\DataTransformers\ArrayToStringTransformer;
use App\DataTransformers\StringToBooleanTransformer;
use App\Helpers\StringHelper;
use Symfony\Component\Validator\Constraints\NotBlank;

/**
 * Parse feature config file to config and list all form fields
 */
class FeatureFormFieldsProvider
{
	/**
	 * Get list fields from config file
	 * 
	 * @return array
	 */
	public static function getFields(array $featureStructure): array
	{
		$fields = [];
		//add activation section
		if (isset($featureStructure['enabled'])) {
			$fields[] = [
				'name' => 'enabled',
				'type' => 'CheckboxType',
				'options' => ['row_attr' => ['section' => 'default'], 'required' => false]
			];
		}

		if (isset($featureStructure['code'])) {
			$fields[] = [
				'name' => 'code',
				'type' => 'HiddenType',
				'options' => ['data' => $featureStructure['code']]
			];
		}
		if (isset($featureStructure['title'])) {
			$fields[] = [
				'name' => 'title',
				'type' => 'HiddenType',
				'options' => ['data' => $featureStructure['title']]
			];
		}

		//add early_adopters section if enabled
		if (isset($featureStructure['early_adopters']['enabled'])) {
			$fields[] = [
				'name' => 'isEarlyAdopters',
				'type' => 'CheckboxType',
				'transformer' => StringToBooleanTransformer::class,
				'options' => ['row_attr' => ['section' => $featureStructure['early_adopters']['section'] ?? 'default']]
			];
			$fields[] = [
				'name' => 'earlyAdoptersList',
				'type' => 'TextareaType',
				'transformer' => ArrayToStringTransformer::class,
				'options' => [
					'label' => $featureStructure['early_adopters']['label'] ?? "early_adopters",
					'row_attr' => ['section' => $featureStructure['early_adopters']['section'] ?? 'default']
				]
			];
		}

		//asign section to each field
		$formFields = $featureStructure['form']['fields'] ?? $featureStructure['fields'] ?? [];
		if ($formFields) {
			$fields = array_merge($fields, $formFields);
			foreach ($fields as $index => $field) {
				$fields[$index]['name'] = StringHelper::cleanString($fields[$index]['name']);
				if (strtolower($fields[$index]["type"]) == "checkboxtype") {
					$fields[$index]['transformer'] = StringToBooleanTransformer::class;
					$fields[$index]['options']['required'] = false;
				}

				if($index===2) {
					$c=0;
				}

				if(isset($field['options']['dynamic'])) {
					$fields[$index]['options']['row_attr']['dynamic'] = $field['options']['dynamic'];
					unset($fields[$index]['options']['dynamic']);
				}

				// Add required constraint if defined in JSON
				if (isset($field['options']['required']) && $field['options']['required'] === true) {
					$fields[$index]['options']['constraints'][] = new NotBlank([
						'message' => 'This field is required.',
					]);
				} else {
					$fields[$index]['options']['required'] = false;
				}

				//convert the media type to textType that gonna hold the path of the choosen image 
				if ($fields[$index]["type"] == "MediaType") {
					$fields[$index]["type"] = 'MediaType';
					$fields[$index]["options"] = array_merge($fields[$index]["options"], ['row_attr' => ['type' => 'mediaType']]);
				}
				if ($fields[$index]["type"] == "SelectType") {
					$fields[$index]["type"] = 'ChoiceType';
					$fields[$index]["options"] = array_merge($fields[$index]["options"], [
						'choices' => array_flip($field['values']),
						'placeholder' => $fields[$index]['options']['placeholder'] ?? 'Select an option'
					]);
				}
				if ($fields[$index]["type"] == "DateType") {
					$fields[$index]["type"] = 'TextType';
					$fields[$index]['options']['attr']['class'] = 'date-picker';
				}
				$fields[$index]['options']['row_attr']['section'] = $field['section'] ?? 'default';
			}
		}

		return $fields;
	}

	public static function getSources(array $featureStructure): array
	{
		return $featureStructure['sources'] ?? [];
	}
}