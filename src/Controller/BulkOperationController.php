<?php

namespace App\Controller;

use App\Form\BulkCopyWizardStep2Type;
use App\Form\MassiveOperationFilter;
use App\Repository\BrandRepository;
use App\Repository\CountryRepository;
use App\Repository\LanguageRepository;
use App\Repository\TranslationKeyRepository;
use App\Service\LabelTranslationService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Request;
use App\Helpers\StringHelper;
use App\Repository\LocalTranslationRepository;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

#[Route('/bulk-operation', name: 'bulk_operation_')]
class BulkOperationController extends AbstractController
{
    public function __construct(
        private LanguageRepository $languageRepository,
        private BrandRepository $brandRepository,
        private CountryRepository $countryRepository,
        private LabelTranslationService $labelTranslationService,
        private TranslationKeyRepository $translationKeyRepository,
        private UrlGeneratorInterface $urlGenerator
    ) {}

    private function generateAvailableBulkOperationsList(): array
    {
        return [
            ['value' => '', 'text' => '', 'js_function' => 'void(0)'],
            ['value' => 'bulk_copy', 'text' => 'Copy', 'js_function' => 'showBulkCopyWizard'],
            ['value' => 'bulk_delete', 'text' => 'Delete', 'js_function' => 'showBulkDeleteWizard'],
            // Add more options as needed
        ];
    }

    // same in bulk
    #[Route(path: '/summary-box/data', name: 'summary_box_data', methods: ['POST'])]
    public function summaryBoxData(Request $request, LabelTranslationService $labelTranslationService): JsonResponse
    {
        $params = $request->request->all();
        $languageCode = $this->languageRepository->getAllLanguageCodes($params);
        $brandCode = $this->brandRepository->getAllBrandCodes($params);
        $countryCode = $this->countryRepository->getAllCountryCodes($params);
        $keyIds = $this->translationKeyRepository->getAllKeyIds();
        $channels = !empty($params['channel']) ? $params['channel'] : ["APP", "WEB"];
        $key = $labelTranslationService->getFormattedTranslationData($brandCode, $countryCode, $channels, $languageCode, $keyIds);
        $data = [];
        foreach ($key as $brand => $countries) {
            foreach ($countries as $country => $channels) {
                foreach ($channels as $channel => $languages) {
                    foreach ($languages as $language => $stats) {
                        $data[] = [
                            'brand' => $brand,
                            'country' => $country,
                            'channel' => $channel,
                            'language' => $language,
                            'total' => $stats['totDefinedKeys'],
                            'set' => $stats['totTranslatedKeys'],
                            'unset' => $stats['totNotYetTranslatedKey']
                        ];
                    }
                }
            }
        }

        return new JsonResponse($data);
    }

    #[Route(path: '/form', name: 'form', methods: ['GET', 'POST'])]
    public function bulkOperationsForm(): Response
    {
        $comboBoxOptions = $this->generateAvailableBulkOperationsList();
        $form = $this->createForm(MassiveOperationFilter::class);

        return $this->render('bulk_operation/bulk_operations/form.html.twig', [
            'comboBoxOptions' => $comboBoxOptions,
            'form' => $form->createView(),
        ]);
    }

    #[Route(path: '/bulk-copy-wizard-step-1', name: 'copy_step_1', methods: ['GET'])]
    public function bulkCopyWizardStep1(
        Request $request,
        TranslationKeyRepository $translationKeyRepository,
        LocalTranslationRepository $localTranslationRepository,
    ): Response {
        $selectedLocalTranslationIds = StringHelper::stringIdsToIntArray($request->query->get('selected_local_translation_ids', ''));
        $selectedLocalTranslationIds = empty($selectedLocalTranslationIds) ? [-1] : $selectedLocalTranslationIds;

        $params = ['local_translation_ids' => $selectedLocalTranslationIds];
        $localTranslations = $localTranslationRepository->getLocalTranslationsByIds($params);

        $params = array_merge($params, [
            'start' => 0,
            'length' => 1000,
        ]);

        $languages = $this->languageRepository->getAllLanguageCodes($params);
        $brands = $this->brandRepository->getAllBrandCodes($params);
        $countries = $this->countryRepository->getAllCountryCodes($params);
        $channels = ["APP", "WEB"];
        $referenceLanguages = $this->languageRepository->getAllReferenceLanguageCodes($params);

        $dtData = $translationKeyRepository->getLocalLanguageTranslationKeys(
            $brands,
            $countries,
            $channels,
            $languages,
            $localTranslations,
            $referenceLanguages,
            $params
        ); 

        return $this->render('bulk_operation/bulk_copy_wizard/bulk_copy_wizard_step_1.html.twig', [
            'selectedLocalTranslationIds' => implode(',', $selectedLocalTranslationIds),
            'dtData' => $dtData,
        ]);
    }

    #[Route(path: '/bulk-copy-wizard-step-2', name: 'copy_step_2', methods: ['GET'])]
    public function bulkCopyWizardStep2(
        Request $request,
        BrandRepository $brandRepository,
        CountryRepository $countryRepository,
    ): Response {

        $selectedLocalTranslationIds = StringHelper::stringIdsToIntArray($request->query->get('selected_local_translation_ids', ''));
        $targetBrandIdsList = $request->query->get('target_brand_ids', '');
        $targetCountryIdsList = $request->query->get('target_country_ids', '');

        $targetChannels = StringHelper::stringListToArray($request->query->get('target_channels', ''));

        $data = [
            'selectedLocalTranslationIds' => implode(',', $selectedLocalTranslationIds),
            'targetBrands' => $brandRepository->getBrandByIds($targetBrandIdsList),
            'targetCountries' => $countryRepository->getCountryByIds($targetCountryIdsList),
            'targetChannels' => $targetChannels,
        ];

        $form = $this->createForm(BulkCopyWizardStep2Type::class, $data);

        return $this->render('bulk_operation/bulk_copy_wizard/bulk_copy_wizard_step_2.html.twig', [
            'form' => $form->createView(),
            'selectedLocalTranslationIds' => $selectedLocalTranslationIds,
            'targetBrandIds' => $targetBrandIdsList,
            'targetCountryIds' => $targetCountryIdsList,
            'targetChannels' => $targetChannels,
        ]);
    }

    #[Route(path: '/bulk-copy-wizard-step-3', name: 'copy_step_3', methods: ['GET'])]
    public function bulkCopyWizardStep3(Request $request): Response
    {
        $selectedLocalTranslationIds = StringHelper::stringIdsToIntArray($request->query->get('selected_local_translation_ids', ''));
        $targetBrandIds = StringHelper::stringIdsToIntArray($request->query->get('target_brand_ids', ''));
        $targetCountryIds = StringHelper::stringIdsToIntArray($request->query->get('target_country_ids', ''));
        $targetChannels = StringHelper::stringListToArray($request->query->get('target_channels', ''));

        $msg = '';
        $dtData = $this->labelTranslationService->performLocalTranslationsBulkCopyDryRun(
            $selectedLocalTranslationIds,
            $targetBrandIds,
            $targetCountryIds,
            $targetChannels,
            $msg
        );

        return $this->render('bulk_operation/bulk_copy_wizard/bulk_copy_wizard_step_3.html.twig', [
            'selectedLocalTranslationIds' => $request->query->get('selected_local_translation_ids', ''),
            'dtData' => $dtData,
            'msg' => $msg,
        ]);
    }

    #[Route(path: '/bulk-copy', name: 'copy', methods: ['GET'])]
    public function bulkCopy(
        Request $request,
        TranslatorInterface $translator
    ): JsonResponse {
        $selectedLocalTranslationIds = StringHelper::stringIdsToIntArray($request->query->get('selected_local_translation_ids', ''));
        $targetBrandIds = StringHelper::stringIdsToIntArray($request->query->get('target_brand_ids', ''));
        $targetCountryIds = StringHelper::stringIdsToIntArray($request->query->get('target_country_ids', ''));
        $targetChannels = StringHelper::stringListToArray($request->query->get('target_channels', ''));

        $msg = '';
        $result = $this->labelTranslationService->performLocalTranslationsBulkCopy(
            $selectedLocalTranslationIds,
            $targetBrandIds,
            $targetCountryIds,
            $targetChannels,
            $msg
        );

        if ($result === false) {
            $this->addFlash('success', $translator->trans('massive.info.final_report_head', [
                '%operation%' => $translator->trans('bulk_operation.copy')
            ]) . "\n" . $msg);
        } else {
            $this->addFlash('success', $translator->trans('massive.info.final_report_head', [
                '%operation%' => $translator->trans('bulk_operation.copy')
            ]) . "\n" . $msg);
        }

        return new JsonResponse(['success' => true]);
    }

    #[Route(path: '/form/data', name: 'form_data', methods: ['POST', 'GET'])]
    public function bulkOperationsFormData(
        Request $request,
        TranslationKeyRepository $translationKeyRepository,
    ): JsonResponse {
        $params = $request->request->all();
        $languages = $this->languageRepository->getAllLanguageCodes($params);
        $brands = $this->brandRepository->getAllBrandCodes($params);
        $countries = $this->countryRepository->getAllCountryCodes($params);
        $channels = !empty($params['channel']) ? $params['channel'] : ["APP", "WEB"];
        $referenceLanguages = $this->languageRepository->getAllReferenceLanguageCodes($params);

        $data = $translationKeyRepository->getLocalLanguageTranslationKeys(
            $brands,
            $countries,
            $channels,
            $languages,
            null,
            $referenceLanguages,
            $params
        );

        return new JsonResponse($data);
    }

    #[Route(path: '/bulk-delete-preview', name: 'delete_preview', methods: ['GET'])]
    public function bulkDeletePreview(
        Request $request,
        TranslationKeyRepository $translationKeyRepository,
        LocalTranslationRepository $localTranslationRepository,
    ): Response {
        $selectedLocalTranslationIds = StringHelper::stringIdsToIntArray($request->query->get('selected_local_translation_ids', ''));
        $selectedLocalTranslationIds = empty($selectedLocalTranslationIds) ? [-1] : $selectedLocalTranslationIds;

        $params = ['local_translation_ids' => $selectedLocalTranslationIds];
        $localTranslations = $localTranslationRepository->getLocalTranslationsByIds($params);

        $params = array_merge($params, [
            'start' => 0,
            'length' => 1000,
        ]);

        $languages = $this->languageRepository->getAllLanguageCodes($params);
        $brands = $this->brandRepository->getAllBrandCodes($params);
        $countries = $this->countryRepository->getAllCountryCodes($params);
        $channels = ["APP", "WEB"];
        $referenceLanguages = $this->languageRepository->getAllReferenceLanguageCodes($params);

        $dtData = $translationKeyRepository->getLocalLanguageTranslationKeys(
            $brands,
            $countries,
            $channels,
            $languages,
            $localTranslations,
            $referenceLanguages,
            $params
        );

        return $this->render('bulk_operation/bulk_delete/bulk_delete_preview.html.twig', [
            'selectedLocalTranslationIds' => implode(',', $selectedLocalTranslationIds),
            'dtData' => $dtData,
        ]);
    }

    #[Route(path: '/bulk-delete', name: 'delete', methods: ['GET'])]
    public function bulkDelete(
        Request $request,
        TranslatorInterface $translator
    ): JsonResponse {
        $selectedLocalTranslationIds = StringHelper::stringIdsToIntArray($request->query->get('selected_local_translation_ids', ''));
        $msg = '';

        $result = $this->labelTranslationService->executeBulkDeleteForLocalTranslations(
            $selectedLocalTranslationIds,
            $msg
        );

        if ($result === false) {
            $this->addFlash('success', $translator->trans('massive.info.final_report_head', [
                '%operation%' => $translator->trans('bulk_operation.delete')
            ]) . "\n" . $msg);
        } else {
            $this->addFlash('success', $translator->trans('massive.info.final_report_head', [
                '%operation%' => $translator->trans('bulk_operation.delete')
            ]) . "\n" . $msg);
        }

        return new JsonResponse(['success' => true]);
    }
}
