<?php

namespace App\Service;

use App\Repository\FeatureRepository;
use App\Entity\Feature;
use App\Entity\FeatureSetting;
use App\Model\FeatureModel;
use App\Form\FeatureFormType;
use App\Entity\FeatureParameter;
use App\Entity\Menu;
use App\Entity\RoleMenu;
use App\Helpers\LoggerTrait;
use App\Repository\ChannelRepository;
use App\Repository\FeatureParameterRepository;
use App\Repository\FeatureSettingRepository;
use App\Repository\MenuRepository;
use App\Repository\RoleRepository;
use App\Security\User;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\Extension\Validator\ValidatorExtension;
use Symfony\Component\Form\Form;
use Symfony\Component\Security\Http\Attribute\CurrentUser;
use Symfony\Component\Yaml\Exception\ParseException;
use Symfony\Component\Form\Forms;
use Symfony\Component\Serializer\Encoder\JsonEncoder;
use Symfony\Component\Serializer\Normalizer\AbstractNormalizer;
use Symfony\Component\Serializer\Serializer;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Translation\Translator;
use Symfony\Component\Validator\Validation;
use Symfony\Component\Translation\Loader\YamlFileLoader;

/**
 * FeatureManager
 */
class FeatureManager
{
    use LoggerTrait;

    const TRANSLATION_DOMAINE = 'validators';

    function __construct(
        private FeatureRepository $featureRepository,
        private FeatureParameterRepository $featureParameterRepository,
        private EntityManagerInterface $objectManager,
        private FeatureSettingManager $featureSettingManager,
        private FeatureSettingRepository $featureSettingRepository,
        private SerializerInterface $serializer,
        private NormalizerInterface $normalizer,
        private ChannelRepository $channelRepository,
        private MenuRepository $menuRepository,
        private RoleRepository $roleRepository,
    ) {}

    public function buildFeature(
        #[CurrentUser] User $user,
        FeatureSetting $featureSetting,
        ?string $brand,
        ?string $country,
        ?Collection $languages,
        ?string $lang = 'en'
    ): FeatureModel {
        try {
            $fileContent = $featureSetting->getContent();
            $featureStructure = $fileContent;
            $translator = new Translator($lang);
            $translator->addLoader('yaml', new YamlFileLoader());
            $translator->addResource(
                'yaml',
                __DIR__ . "/../../translations/" . self::TRANSLATION_DOMAINE . ".{$lang}.yaml",
                $lang,
                self::TRANSLATION_DOMAINE
            );
            $validatorBuilder = Validation::createValidatorBuilder();
            $validatorBuilder->setTranslator($translator);
            $validatorBuilder->setTranslationDomain(self::TRANSLATION_DOMAINE);
            $validator = $validatorBuilder->getValidator();
            $formFactory = Forms::createFormFactoryBuilder()
                ->addExtension(new ValidatorExtension($validator))
                ->getFormFactory();
            $forms = [];
            $options['level'] = $featureStructure['form']['level'] ?? [];
            $options['profile'] = $user->getProfile()->isSuperAdministrator() ? 'SuperAdmin' : ($user->getProfile()->isBrandAdministrator() ? 'BrandAdmin' : 'LocalAdmin');

            $options['languages'] = $languages;
            $options['fields'] = FeatureFormFieldsProvider::getFields($featureStructure);

            $sources = FeatureFormFieldsProvider::getSources($featureStructure);
            $channelNames = $this->channelRepository->getChannelMap();

            foreach ($sources as $source) {
                $source = strtoupper($source);
                if (!in_array($source, $channelNames, true)) {
                    continue;
                }
                $data = $this->getFormData($featureSetting, $brand, $country, $source, $options);
                $forms[$source] = $formFactory->createNamed('feature_' . $featureSetting->getId() . $source, FeatureFormType::class, $data, $options);
            }

            return (new FeatureModel())
                ->setTitle($featureStructure['form']['title'] ?? '')
                ->setTemplate($featureStructure['template'] ?? 'default')
                ->setForm($forms);
        } catch (ParseException $e) {
            $this->logger->error('ParseException occured while building the feature: ' . $e->getMessage());
            throw new \Exception('Feature not found or has configuration errors', 404);
        } catch (\Exception $e) {
            $this->logger->error('error occured while building the feature: ' . $e->getMessage());
            throw $e;
        }
    }

    public function saveFeature(FeatureSetting $featureSetting, Form $form, ?string $brand, ?string $country, ?Collection $languages): Feature
    {
        $data = $form->getData();
        $source = $data['source'] ?? 'APP';

        $service = $this->featureRepository->findOneBy(['brand' => $brand, 'country' => $country, 'code' => $featureSetting->getId(), 'source' => $source]);

        if (!$service) {
            $service = new Feature();
            $service->setFeatureSetting($featureSetting ?? null)
                ->setLabel($featureSetting->getName())
                ->setBrand($brand)
                ->setCountry($country)
                ->setSource($source)
                ->setLanguage('default')
                ->setFeatureSetting($featureSetting);
            $this->objectManager->persist($service);
        }
        $service->setEnabled($data['enabled'] ?? false);
        $service->setIsEarlyAdopter($data['isEarlyAdopters'] ?? false);
        $service->setEarlyAdoptersList($data['earlyAdoptersList'] ?? []);
        $data['enabled'] = (bool) ($data['enabled'] ?? false);
        $data['isEarlyAdopters'] = (bool) ($data['isEarlyAdopters'] ?? false);

        unset($data['tooltipValues']);
        unset($data['placeholderData']);
        unset($data['isEarlyAdopter']);
        unset($data['earlyAdoptersList']);
        unset($data['source']);

        foreach ($data as $item => $value) {
            if ($form->has($item)) {
                $field = $form->get($item);
                $field_hidden = $form->has($field->getName() . '_hidden') ? $form->get($field->getName() . '_hidden') : null;

                if($field->getConfig()->getType()->getBlockPrefix() == "checkbox" 
                    && $field_hidden?->getData() !== 'disabled') {
                    $data[$item] = $value;//boolval($value);
                }

                if($field->getConfig()->getType()->getBlockPrefix() == "checkbox" 
                    && $field->isDisabled()) {
                        $data[$item] = '';
                    }
            }
        }

        $this->manageParameters($data, $service, $languages);

        $this->objectManager->flush();
        return $service;
    }

    public function updateFeature(FeatureSetting $featureSetting, ?string $brand, ?string $country): bool
    {
        $channelNames = $this->channelRepository->getChannelMap();
        foreach ($channelNames as $channel) {
            $service = $this->featureRepository->findOneBy(['brand' => $brand, 'country' => $country, 'code' => $featureSetting->getId(), 'source' => $channel]);
            if ($service) {
                $service->setLabel($featureSetting->getName());
            }
        }
        $this->objectManager->flush();
        return true;
    }

    public function getFormData(FeatureSetting $featureSetting, ?string $brand, ?string $country, ?string $source, array $options = null): array
    {
        $featuresByName = $this->featureRepository->findBy(['label' => $featureSetting->getName(), 'source' => $source]) ?? [];
        $globalFeature = [];
        $brandFeature = [];
        $feature = null;
        foreach ($featuresByName as $featureByName) {
            $featureData = $this->getFeatureSpecificValues($featureByName);
            if ($featureByName->getBrand() == 'XX') {
                $globalFeature = $featureData;
            } else if ($featureByName->getBrand() != 'XX' && is_null($featureByName->getCountry())) {
                $brandFeature[$featureByName->getBrand()] = $featureData;
            }

            if ($featureByName->getBrand() == $brand && $featureByName->getCountry() == $country) {
                $feature = $featureByName;
            }
        }

        $previousValues = array(
            'global' => $globalFeature,
            'brand' => $brandFeature[$brand] ?? null
        );
        $placeholderValues = $lastKnownValues = $previousValues;
        $tooltipOldValues = [];
        if ($brand == 'XX') {
            $placeholderValues = null;
            $tooltipOldValues = null;
            $lastKnownValues = null;
        } else if($brand != null && $brand != 'XX' && $country == null) {
            $tooltipOldValues = $lastKnownValues = $previousValues['global'];
            $placeholderValues = $globalFeature;
        } else if ($brand != null && $country != null) {
            $tooltipOldValues = $previousValues;
        }

        $formData = [
            'source' => $source,
            'enabled' => $feature?->isEnabled(),
            'isEarlyAdopters' => $feature?->isIsEarlyAdopter(),
            'earlyAdoptersList' => $feature?->getEarlyAdoptersList(),
            'placeholderData' => $placeholderValues,
            'tooltipValues' => $tooltipOldValues
        ];

        if ($feature) {
            $formDbValues = $this->indexObjectsByParamValue($feature->getFeatureParameters());
        } else {
            $formDbValues = [];
        }

        foreach ($options['fields'] as $field) {
            $fieldName = $field['name'];
            $isCheckbox = strtolower($field['type']) === 'checkboxtype';
            
            if (isset($lastKnownValues[$fieldName]) && $isCheckbox) {
                if(!isset($formDbValues[$fieldName]) || (isset($field['overwrite']) && $field['overwrite'] == false)) {
                    $formDbValues[$fieldName] = $lastKnownValues[$fieldName];
                }
            } else if(isset($lastKnownValues['brand']) && $isCheckbox) {
                if( !empty($lastKnownValues['brand'][$fieldName]) 
                    && !isset($formDbValues[$fieldName])) {
                    $formDbValues[$fieldName] = $lastKnownValues['brand'][$fieldName];
                } else if(!empty($lastKnownValues['global'][$fieldName]) 
                    && !isset($formDbValues[$fieldName])) {
                    $formDbValues[$fieldName] = $lastKnownValues['global'][$fieldName];
                }
            }
        }

        $formData = array_merge($formData, $formDbValues);
        return $formData;
    }

    private function getFeatureSpecificValues(Feature $feature): array
    {
        return array_merge(
            ['earlyAdoptersList' => $feature->getEarlyAdoptersList() ?? []],
            $this->indexObjectsByParamValue($feature),
            $this->indexObjectsByParamValue($feature->getFeatureParameters())
        );
    }

    public function getFeaturesForMenu(): array
    {
        return $this->featureSettingRepository->findAll();
    }

    private function manageParameters(array $data, Feature $feature, Collection $languages): void
    {
        $parameters = $feature->getFeatureParameters();
        array_walk($data, function ($value, $field) use ($parameters, $feature, $languages) {
            $parsedField = $this->parseField($field, $languages);
            $field = $parsedField['name'];
            $language = $parsedField['language'];
            if (!$featureParameter = $this->featureParameterRepository->findOneBy(['name' => $field, 'language' => $language, 'feature' => $feature])) {
                $featureParameter = new FeatureParameter();
                $featureParameter->setFeature($feature);
                $featureParameter->setName($field);
                $featureParameter->setLanguage($language);
                $this->objectManager->persist($featureParameter);
            }
            if (is_array($value)) {
                if (isset($value['choicesData'])) {
                    $value = $value['choicesData'] ?? '';
                } else {
                    $multiFieldsData = [];
                    foreach ($value as $item) {
                        if (isset($item['type']) && $item['type'] == 'isMultifield') {
                            $multiFieldsData[] = $item;
                        }
                    }
                    $value = json_encode($multiFieldsData, true);
                }
            }
            $featureParameter->setValue($value ?? '');
        });
    }

    private function indexObjectsByParamValue($parameters): array
    {
        $data = [];
        foreach ($parameters as $parameter) {
            if ($this->isTranslatable($parameter)) {
                $data[$parameter->getName() . '__' . $parameter->getLanguage()] = $parameter->getValue();
            } else {
                $data[$parameter->getName()] = $parameter->getValue();
            }
        }
        return $data;
    }

    private function parseField(string $fieldName, Collection $languages): array
    {
        $field = [];
        $fieldParts = explode('__', $fieldName);
        $field['name'] = $fieldParts[0];
        if (str_ends_with($field['name'], '_hidden')) {
            $field['name'] = substr($field['name'], 0, -7);
        }
        $field['language'] = $fieldParts[1] ?? 'default';
        $field['language'] = $this->isValidLanguage($field['language'], $languages) ? $field['language'] : 'default';
        return $field;
    }

    private function isValidLanguage(?string $code, Collection $languages): bool
    {
        return
            $languages->exists(function ($key, $language) use ($code) {
                return $language->getCode() == $code;
            });
    }

    private function isTranslatable(FeatureParameter $parameter): bool
    {
        return $parameter->getLanguage() !== 'default';
    }

    public function removeFeature(FeatureSetting $featureSetting): bool
    {
        $channelNames = $this->channelRepository->getChannelMap();
        foreach ($channelNames as $channel) {
            $service = $this->featureRepository->findOneBy(['code' => $featureSetting->getId(), 'source' => $channel]);
            if ($service) {
                $this->objectManager->remove($service);
            }
        }
        $this->objectManager->flush();
        return true;
    }

    public function makePackage(FeatureSetting $featureSetting): array
    {
        $featureSettingRows = $this->featureSettingRepository->findBy(['name' => $featureSetting->getName()]) ?? [];
        $featureSettingData = $this->normalizer->normalize($featureSettingRows, null, ['groups' => 'package_export']);
        return $featureSettingData;
    }

    public function importFeature(string $jsonContent): array
    {
        if (json_last_error() !== JSON_ERROR_NONE) {
            return [false, "Invalid Json format"];
        }
        try {
            $encoders = [new JsonEncoder()];

            $normalizers = [$this->normalizer];

            $serializer = new Serializer($normalizers, $encoders);

            $featureSettings   = json_decode($jsonContent, true);

            foreach($featureSettings as $fs) {
                $featureSetting = $serializer->denormalize($fs, FeatureSetting::class, 'json', [
                    'groups' => ['package_export'],
                    AbstractNormalizer::IGNORED_ATTRIBUTES => ['menus', 'features']
                ]);

                $featureSetting->setName($fs['name'] . ' - Copy');
                $this->objectManager->persist($featureSetting);
                $this->objectManager->flush();

                if(!empty($fs['features'])) {
                    foreach($fs['features'] as $feature) { 
                        $feature = $serializer->denormalize($feature, Feature::class, 'json', [
                            'groups' => ['package_export'],
                        ]);

                        $feature->setCode($featureSetting->getId());
                        $feature->setLabel($featureSetting->getName());

                        $featureSetting->addFeature($feature);
                        $this->objectManager->persist($feature);
                    }
                }
                
                if(!empty($fs['menus'])) {
                    foreach($fs['menus'] as $menu) {
                        $parent = $menu['parent'];
                        $roleMenus = $menu['roleMenus'];
                        $menu = $serializer->denormalize($menu, Menu::class, 'json', [
                            'groups' => ['package_export'],
                            AbstractNormalizer::IGNORED_ATTRIBUTES => ['parent', 'roleMenus']
                        ]);

                        $parentMenu = $this->menuRepository->findOneBy(['label' => $parent['label']]);
                        $menu->setParent($parentMenu);
                        $menu->setLabel($featureSetting->getName());

                        foreach($roleMenus as $roleMenu) {
                            $role = $roleMenu['role'];
                            $roleMenu = $serializer->denormalize($roleMenu, RoleMenu::class, 'json', [
                                'groups' => ['package_export'],
                                AbstractNormalizer::IGNORED_ATTRIBUTES => ['role']
                            ]);
                            
                            $role = $this->roleRepository->findOneBy(['label' => $role['label']]);
                            $roleMenu->setRole($role);

                            $menu->addRoleMenu($roleMenu);
                            $this->objectManager->persist($roleMenu);
                        }

                        $featureSetting->addMenu($menu);
                        $this->objectManager->persist($menu);
                    }
                }

                $this->objectManager->persist($featureSetting);
            }
            $this->objectManager->flush();
        
            return [true, "Feature imported successfully"];
        } catch (\Exception $e) {
            return [false, $e->getMessage()];
        }
    }
}
