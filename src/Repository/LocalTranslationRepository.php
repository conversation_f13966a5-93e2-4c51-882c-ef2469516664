<?php

namespace App\Repository;

use App\Entity\Site;
use App\Entity\Language;
use App\Entity\LocalTranslation;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Locale;

/**
 * @extends ServiceEntityRepository<LocalTranslation>
 *
 * @method LocalTranslation|null find($id, $lockMode = null, $lockVersion = null)
 * @method LocalTranslation|null findOneBy(array $criteria, array $orderBy = null)
 * @method LocalTranslation[]    findAll()
 * @method LocalTranslation[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LocalTranslationRepository extends ServiceEntityRepository
{
    public function __construct(
        ManagerRegistry $registry, 
        private TranslationKeyRepository $translationKeyRepository
    ){
        parent::__construct($registry, LocalTranslation::class);
    }

    /**
     * Get all local translations filtered by the given site
     *
     * @return array
     */
    public function findBySite(Site $site): array
    {
        return $this
            ->createQueryBuilder('localTranslation')
            ->where('localTranslation.site = :site')
            ->setParameter('site', $site)
            ->getQuery()
            ->getResult();
    }

    public function getAllWithUniqueKey(array $selectedKey): array
    {
        $queryBuilder = $this->createQueryBuilder('lt');
        $queryRows = $queryBuilder
            ->select('lt')
            ->addSelect('UPPER(CONCAT(tk.labelKey, \'|\', b.code, \'|\', tk.channel, \'|\', slang.code, \'|\', c.code, \'|\', COALESCE(w.name, \'\'))) AS uniqueKey')
            ->innerJoin('lt.translationKey', 'tk')
            ->innerJoin('tk.brand', 'b')
            ->leftJoin('tk.widget', 'w')
            ->innerJoin('lt.site', 'site')
            ->innerJoin('site.country', 'c')
            ->innerJoin('site.languages', 'slang')
            ->where($queryBuilder->expr()->in('UPPER(CONCAT(tk.labelKey, \'|\', b.code, \'|\', tk.channel, \'|\', slang.code, \'|\', c.code, \'|\', COALESCE(w.name, \'\')))', ':uniqueKeys'))
            ->setParameter('uniqueKeys', $selectedKey)
            ->getQuery()
            ->getResult();

        return $queryRows;
    }

    public function getLocalTranslations(Site $site, Language $language, string $channel): array
    {
        return $this->createQueryBuilder('lt')
            ->select('lt', 'rt.translation as referenceTranslation')
            ->leftJoin('lt.translationKey', 't')
            ->leftJoin('t.referenceTranslations', 'rt')
            ->leftJoin('rt.language', 'referencelanguage')
            ->where('lt.site = :site')
            ->andWhere('t.channel = :channel')
            ->andWhere('lt.language = :language')
            ->andWhere('referencelanguage = :preferedLanguage')
            ->setParameter('site', $site)
            ->setParameter('channel', $channel)
            ->setParameter('language', $language)
            ->setParameter('preferedLanguage', $site->getPreferedLanguage())
            ->getQuery()
            ->getResult();
    }

    public function getLocalTranslationsMassive(array $params): array
    {
        $start = isset($params['start']) ? (int)$params['start'] : 0;
        $length = isset($params['length']) ? (int)$params['length'] : 10;
        
        $recordsTotal = $this->createQueryBuilder('lt')
            ->select('COUNT(lt.id)')
            ->getQuery()
            ->getSingleScalarResult();

        $queryBuilder = $this->createQueryBuilder('lt')
            ->select(
                'tk.labelKey as label_key',
                'tk.channel as channel', 
                'rt.translation as reference_translation',
                'rl.code as reference_language_code',
                's.label as site_label',
                'b.code as brand_code',
                'c.code as country_code',
                'll.code as language_code',
                'lt'
            )
            ->innerJoin('lt.translationKey', 'tk')
            ->innerJoin('lt.site', 's')
            ->innerJoin('s.preferedLanguage', 'rl', 'WITH', 'rl.isReference = 1')
            ->innerJoin('lt.language', 'll')
            ->innerJoin('s.brand','b')
            ->innerJoin('s.country','c')
            ->innerJoin('App\Entity\ReferenceTranslation', 'rt', 'WITH', 'tk = rt.translationKey')
            ->setFirstResult($start)
            ->setMaxResults($length);

        if (!empty($params['local_languages'])) {
            $queryBuilder
                ->andWhere('ll.code IN (:locallanguages)')
                ->setParameter('locallanguages', $params['local_languages']);
        }

        if (!empty($params['channel'])) {
            $queryBuilder
                ->andWhere('tk.channel IN (:channel)')
                ->setParameter('channel', $params['channel']);
        }

        if (!empty($params['reference_languages'])) {
            $queryBuilder
                ->andWhere('rl.code IN (:reflanguages)')
                ->setParameter('reflanguages', $params['reference_languages']);
        }

        if (!empty($params['brand'])) {
            $queryBuilder
                ->andWhere('b.code IN (:brands)')
                ->setParameter('brands', $params['brand']);
        }

        if (!empty($params['country'])) {
            $queryBuilder
                ->andWhere('c.code IN (:country)')
                ->setParameter('country', $params['country']);
        }

        if (!empty($params['status']) && count($params['status']) === 1) {
            if ($params['status'][0] == '1') {
                
                $queryBuilder->andWhere($queryBuilder->expr()->isNotNull('lt.translation'));
            } else {
                $queryBuilder->andWhere($queryBuilder->expr()->isNull('lt.translation'));
            }
        }
        $paginator = new Paginator($queryBuilder, fetchJoinCollection: true);
        $results = [
            'data' => [],
            'recordsFiltered' => $paginator->count(),
            'recordsTotal' => $recordsTotal,
        ];
        foreach ($paginator as $item) {
            $item['updatedAt'] = $item[0]->getUpdatedAt()->format('m/d/Y');
            $item['translation'] = $item[0]->getTranslation();
            $item['id'] = $item[0]->getId();
            $results['data'][] = $item;
        }

        return $results;
    }

    /**
     * @param array<array-key, int> $localTranslationIds
     * @return array
     */
    public function getLabelTranslationsBulkCopySource(array $localTranslationIds): array
    {
        $qb = $this->createQueryBuilder('sourcelt');
       
        $qb->select([
                'sourceSite.id AS source_site_id',
                'brand.id AS source_brand_id',
                'brand.name source_brand_name',
                'country.id AS source_country_id',
                'country.name source_country_name',
                'translationLanguage.id AS source_translation_language_id',
                'translationLanguage.code AS source_translation_language_code',
                'sourcetk.channel AS source_channel',
                'sourcetk.id AS source_translation_key_id',
                'sourcetk.labelKey AS source_label_key',
                'sourcelt.id AS source_local_translation_id',
                'sourcelt.translation AS source_translation'
            ])
            ->innerJoin('sourcelt.translationKey', 'sourcetk')
            ->innerJoin('sourcelt.site', 'sourceSite')
            ->innerJoin('sourceSite.brand', 'brand')
            ->innerJoin('sourceSite.country', 'country')
            ->innerJoin('sourcelt.language', 'translationLanguage')
            ->where($qb->expr()->in('sourcelt.id', ':localTranslationIds'))
            ->setParameter('localTranslationIds', $localTranslationIds);

        return $qb->getQuery()->getArrayResult();
    }

    public function getLocalTranslationsByIds(array $params): array
    {
        if (empty($params['local_translation_ids'])) {
            return [];
        }
        
        $qb = $this->createQueryBuilder('lt');
        $qb->select('lt')
            ->where($qb->expr()->in('lt.id', ':ids'))
            ->setParameter('ids', $params['local_translation_ids']);

        return $qb->getQuery()->getResult();
    }

    public function deleteBySiteAndLanguages(Site $site, array $languages): void
    {
        $queryBuilder = $this->createQueryBuilder('lt');
        $queryBuilder->delete('App\Entity\LocalTranslation', 'lt')
            ->where('lt.site = :site')
            ->andWhere('lt.language IN (:languages)')
            ->setParameter('site', $site)
            ->setParameter('languages', $languages)
            ->getQuery()
            ->execute();
    }
}