<?php

namespace App\Service;

use App\Document\OwnersManual;
use App\Repository\BrandRepository;
use Symfony\Component\Serializer\SerializerInterface;

class OwnersManualManager
{
    const COLLECTION = "ownersManual";
    public function __construct(
        private BrandRepository $brandRepository,
        private MongoAtlasQueryService $mongoAtlasQueryService,
        private SerializerInterface $serializerInterface 
    ) {}

    public function save(OwnersManual $ownersManual)
    {
        $vehicleModelArr = $this->serializerInterface->normalize($ownersManual);
        $this->mongoAtlasQueryService->insertOne(self::COLLECTION, $vehicleModelArr);
    }
    public function update(OwnersManual $ownersManual, string $id)
    {
        $vehicleModelArr = $this->serializerInterface->normalize($ownersManual);
        $this->mongoAtlasQueryService->updateOne(self::COLLECTION, ['_id' => ['$oid' => $id]],$vehicleModelArr);
    }

    public function findDuplicateKeys(array $lcdv): array
    {
        $duplicate = $this->mongoAtlasQueryService->findDuplicateKeys(self::COLLECTION, $lcdv);
        return $duplicate;
    }

    public function getVehicleModel(array $brand): array
    {
        $filter = ['brand' => ['$in' => $brand]];
        $response = $this->mongoAtlasQueryService->find(self::COLLECTION, $filter);
        if ($response->getCode() == 200) {
            return json_decode($response->getData(), true)['documents'] ?? [];
        }
        return [];
    }

    public function findModel(string $id): OwnersManual
    {
        $response = $this->mongoAtlasQueryService->find(self::COLLECTION, ['_id' => ['$oid' => $id]]);
        $data = json_decode($response->getData(), true);
        $documentArray = $data['documents'][0];
        $ownersManual = $this->serializerInterface->deserialize(
            json_encode($documentArray),
            OwnersManual::class,
            'json'
        );
        return $ownersManual;
    }

    public function removeModel(string $id)
    {
        $response = $this->mongoAtlasQueryService->delete(self::COLLECTION,['_id' => ['$oid' => $id]]);
        if ($response->getCode() == 200) {
            return true;
        }
        return false;
    }
}
