/**
Create a script to clean from Features folder all the menu items linked to a removed features!
**/

SELECT rm.*
FROM role_menu rm 
LEFT JOIN menu m ON rm.menu_id = m.id
LEFT JOIN feature_setting f ON m.feature_id = f.id
WHERE m.feature_id IS NOT NULL AND f.id IS NULL;

DELETE rm
FROM role_menu rm 
LEFT JOIN menu m ON rm.menu_id = m.id
LEFT JOIN feature_setting f ON m.feature_id = f.id
WHERE m.feature_id IS NOT NULL AND f.id IS NULL;


SELECT pm.*
FROM profile_menu pm 
LEFT JOIN menu m ON pm.menu_id = m.id
LEFT JOIN feature_setting f ON m.feature_id = f.id
WHERE m.feature_id IS NOT NULL AND f.id IS NULL;

DELETE pm
FROM profile_menu pm 
LEFT JOIN menu m ON pm.menu_id = m.id
LEFT JOIN feature_setting f ON m.feature_id = f.id
WHERE m.feature_id IS NOT NULL AND f.id IS NULL;


SELECT m.*
FROM menu m
LEFT JOIN feature_setting f ON m.feature_id = f.id
WHERE m.feature_id IS NOT NULL AND f.id IS NULL;

DELETE m
FROM menu m
LEFT JOIN feature_setting f ON m.feature_id = f.id
WHERE m.feature_id IS NOT NULL AND f.id IS NULL;



