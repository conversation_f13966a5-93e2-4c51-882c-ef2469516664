<?php
namespace App\Form\FormDataTransformers;

use Symfony\Component\Form\DataTransformerInterface;

class CheckboxToStringTransformer implements DataTransformerInterface
{

    public function transform($value)
    {
        // Transform the database value to form value
        return $value === 'true';
    }

    public function reverseTransform($value)
    {
        // Otherwise, handle as normal: return "1" if checked, "0" if unchecked
        return $value ? 'true' : 'false';
    }
}
