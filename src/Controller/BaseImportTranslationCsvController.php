<?php

namespace App\Controller;

use App\Entity\Site;
use Exception;
use App\Import\Model\CommonTranslationCsv;
use App\Import\Model\LocalTranslationCsv;
use App\Import\Model\ReferenceTranslationCsv;
use App\Import\Strategy\AbstractTranslationCsvImportStrategy;
use App\Import\Strategy\LocalTranslationCsvImportStrategy;
use App\Import\Strategy\ReferenceTranslationCsvImportStrategy;
use App\Service\PhpConfigService;
use RuntimeException;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\SerializerInterface;
use Webmozart\Assert\Assert;
use App\Service\TranslationKeySerializer;

abstract class BaseImportTranslationCsvController extends AbstractController
{
    /**
     * @var array<string, mixed>
     */
    protected array $defaultCsvContext;

    public function __construct(
        private LocalTranslationCsvImportStrategy $localTranslationCsvImportStrategy,
        private ReferenceTranslationCsvImportStrategy $referenceTranslationCsvImportStrategy,
        private serializerInterface $serializer,
        private PhpConfigService $phpConfigService
    ) {
        $this->defaultCsvContext = [
            'csv_delimiter' => "\t",
            'csv_enclosure' => '"',
            'csv_escape_char' => '\\',
            'csv_key_separator' => '.',
        ];
    }

    protected function getCsvContext(array $addedContext = []): array
    {
        return array_merge($addedContext, $this->defaultCsvContext);
    }

    public function importTranslationCsv(
        Request $request,
        TranslationKeySerializer $serializer,
        ?string $brand,
        Site $site
    ): Response {
        set_time_limit(600);
        try {
            /**
             * @var ?UploadedFile uploadedFile
             */
            $uploadedFile = $request->files->get('csv_file');
            
            if ($uploadedFile !== null) {
                // check if file is uploaded
                $this->raiseExceptionIfNoFileUploaded($uploadedFile);

                $data = file_get_contents($uploadedFile->getPathname());
                Assert::notEmpty($data, 'Empty file uploaded');

                $importStrategy = $this->getTranslationCsvStrategy();
                Assert::notNull($importStrategy, 'No import strategy found');
                $data = $serializer->decode($data);
                $result = $importStrategy->validate($data, $this->getCsvContext());
                return $this->render($this->getValidationResultTemplate(), [
                    'header' => $this->getTranslationCsvClass()::$headerColumnKeys,
                    'toImportList' => $result['toImportList'],
                    'toImportListCsv' => $this->serializer->serialize($result['toImportList'], 'csv', $this->getCsvContext()),
                    'toImportWithErrorsList' => $result['toImportWithErrorsList'],
                    'toSkipList' => $result['toSkipList'],
                    'goBackUrl' => $this->getOnSuccessRoute(),
                ]);

            } elseif ($request->request->has('toImportList')) {

                $toImportListCsv = $request->request->get('toImportList');
                Assert::stringNotEmpty($toImportListCsv, 'No data to import');

                $importStrategy = $this->getTranslationCsvStrategy();
                Assert::notNull($importStrategy, 'No import strategy found');

                $globalUpdate = $importStrategy == $this->referenceTranslationCsvImportStrategy;
                $toImportList = $importStrategy->deserialize($toImportListCsv, $this->getCsvContext());
                $result = $importStrategy->import($toImportList, $brand, $site, $globalUpdate);
                $countImported = count($result['updated']) + count($result['inserted']);
                if ($countImported > 0) {
                    $successMsg = [];
                    $successMsg[] = 'Created ' . count($result['inserted']) . ' records';
                    $successMsg[] = 'Updated ' . count($result['updated']) . ' records';
                    $this->addFlash('success', implode(' - ', $successMsg));
                }

                $cntResults = count($result['withErrors']);
                if ($cntResults > 0) {
                    $msg = 'Errors on ' . $cntResults . ($cntResults > 1 ? ' records' : ' record') . ': <br>';
                    foreach($result['withErrors'] as $errorData) {
                        $msg .= ' - ' . $errorData['errors'] . '<br>';
                    }
                    $this->addFlash('danger', $msg);
                }
                if (count($result['skipped']) > 0) {
                    $this->addFlash('warning', 'Skipped ' . count($result['skipped']) . ' records, because nothing to update');
                }

                return $this->redirectToRoute($this->getOnSuccessRoute(), []);
            } else {
                throw new RuntimeException('No file uploaded');
            }
        } catch (Exception $e) {           
            $this->addFlash('danger', $e->getMessage());
            return $this->redirectToRoute($this->getOnSuccessRoute(), []);
        }
    }

    private function raiseExceptionIfNoFileUploaded(?UploadedFile $uploadedFile): void
    {
        if ($uploadedFile === null) {
            throw new RuntimeException('No file uploaded');
        }

        $error = $uploadedFile->getError();
        $message = '';
        if ($error !== UPLOAD_ERR_OK) {
            // Handle different error cases
            switch ($error) {
                case UPLOAD_ERR_INI_SIZE:
                    $message = 'The uploaded file exceeds the max upload filesize';
                    break;

                case UPLOAD_ERR_FORM_SIZE:
                    $message = 'The uploaded file exceeds the max upload filesize';
                    break;

                case UPLOAD_ERR_PARTIAL:
                    $message = 'The uploaded file was only partially uploaded';
                    break;

                case UPLOAD_ERR_NO_FILE:
                    $message = 'No file was uploaded';
                    break;

                case UPLOAD_ERR_NO_TMP_DIR:
                    $message = 'Missing a temporary folder';
                    break;

                case UPLOAD_ERR_CANT_WRITE:
                    $message = 'Failed to write file to disk';
                    break;

                case UPLOAD_ERR_EXTENSION:
                    $message = 'A PHP extension stopped the file upload';
                    break;

                default:
                    $message = 'Unknown upload error';
            }

            throw new RuntimeException($message);
        }
    }

    /**
     * @return class-string<CommonTranslationCsv>
     */
    abstract protected function getTranslationCsvClass(): string;

    abstract protected function getValidationResultTemplate(): string;

    abstract protected function getOnSuccessRoute(): string;

    private function getTranslationCsvStrategy(): ?AbstractTranslationCsvImportStrategy
    {
        $modelClass = $this->getTranslationCsvClass();
        if ($modelClass === LocalTranslationCsv::class) {
            return $this->localTranslationCsvImportStrategy;
        }
        if ($modelClass === ReferenceTranslationCsv::class) {
            return $this->referenceTranslationCsvImportStrategy;
        }

        return null;
    }

    public function getMaxUploadSize(): string
    {
        $postMaxSize = $this->phpConfigService->getPostMaxSizeInBytes();
        return !empty($postMaxSize)? $postMaxSize : $this->phpConfigService->getUploadMaxFileSizeInBytes(); 
    }
}