TRUNCATE TABLE `favorite`;
TRUNCATE TABLE `access_log`;


DELETE
FROM role_menu
WHERE menu_id IN (SELECT id FROM menu WHERE feature_id IS NOT NULL);

DELETE
FROM profile_menu
WHERE menu_id IN (SELECT id FROM menu WHERE feature_id IS NOT NULL);

DELETE FROM menu WHERE feature_id IS NOT NULL;

TRUNCATE TABLE `feature_parameter`;
TRUNCATE TABLE `feature_access`;
TRUNCATE TABLE `widget_feature_attribute`;
TRUNCATE TABLE `widget_data`;

DELETE FROM `widget_feature`;

TRUNCATE TABLE `local_translation`;
TRUNCATE TABLE `reference_translation`;

DELETE FROM`translation_key`;

DELETE FROM `widget`;
DELETE FROM `feature_setting`;
DELETE FROM `feature`;

DELETE FROM `media_service`;
DELETE FROM `media`;
TRUNCATE TABLE `user_preference`;
TRUNCATE TABLE `local_key_json_release`;

OPTIMIZE TABLE `feature_setting`;
OPTIMIZE TABLE `menu`;
OPTIMIZE TABLE `widget_feature`;
OPTIMIZE TABLE `translation_key`;
OPTIMIZE TABLE `widget`;
OPTIMIZE TABLE `feature`;
OPTIMIZE TABLE `media_service`;
OPTIMIZE TABLE `media`;



