<?php


namespace App\Service;

use App\Entity\Language;
use App\Entity\PsaLanguage;
use App\Entity\PsaSite;
use App\Entity\Site;
use App\Entity\VehicleLabel;
use App\Repository\LanguageRepository;
use App\Repository\LocaleVehicleLabelRepository;
use App\Repository\PsaLanguageRepository;
use App\Repository\PsaSiteRepository;
use App\Repository\SiteRepository;
use App\Repository\VehicleLabelRepository;

class VehicleLabelService
{
    /**
     * @var VehicleLabelRepository
     */
    private $_repository;
    /**
     * @var LanguageRepository
     */
    private $_languageRepository;
    /**
     * @var VehicleLabelRepository
     */
    private $_localeVehicleLabelRepository;
    /**
     * @var SiteRepository
     */
    private $_siteRepository;
    /**
     * @var VehicleLabelRepository
     */
    private $_labelRepository;

    public function __construct(VehicleLabelRepository $repository, LanguageRepository $languageRepository,
                                VehicleLabelRepository $localeVehicleLabelRepository, SiteRepository $siteRepository,
                                VehicleLabelRepository $labelRepository)
    {
        $this->_repository = $repository;
        $this->_languageRepository = $languageRepository;
        $this->_localeVehicleLabelRepository = $localeVehicleLabelRepository;
        $this->_siteRepository = $siteRepository;
        $this->_labelRepository = $labelRepository;
    }

    public function getLabelTranslation(VehicleLabel $vehicleLabel, string $brand, string $country, string $language) {
        $result = $this->_localeVehicleLabelRepository->getLocalVehicleLabel($vehicleLabel, $brand, $country, $language);
        if (count($result)) {
            $result = reset($result);
            $vehicleLabel->setLabel($result['localLabel']);
        }
        return $vehicleLabel;
    }
    public function normalizeApiData(string $brand, string $country, string $language) {
        $vehicleLabels = [];
        $site = $this->_siteRepository->findOneBy(['country' => $country, 'brand' => $brand]);
        $language = $this->_languageRepository->findOneBy(['code' => $language]);
        
        if (!$site || !$language) {
            return $this->_labelRepository->findBy(['brand' => $brand]);
        }
        
        $labels = $this->_repository->getVehicleLabelsWithLanguages($site, $language);
        foreach ($labels as $label) {
            /**
             * @var VehicleLabel $vehicleLabel
             */
            $vehicleLabel = $label['label'];
            if ($label['localLabel']) {
                $vehicleLabel->setLabel($label['localLabel']);
            }
            $vehicleLabels[] = $vehicleLabel;
        }
        return $vehicleLabels;
    }

    public function normalizeData(Site $site, Language $language) {
        $vehicleLabels = [];
        $labels = $this->_repository->getVehicleLabelsWithLanguages($site, $language);
        foreach ($labels as $label) {
            /**
             * @var VehicleLabel $vehicleLabel
             */
            $vehicleLabel = $label['label'];
            if ($label['localLabel']) {
                $vehicleLabel->setLabel($label['localLabel']);
                $vehicleLabel->setCreatedAt($label['creationDate']);
            }
            $vehicleLabels[] = $vehicleLabel;
        }
        return $vehicleLabels;
    }
}