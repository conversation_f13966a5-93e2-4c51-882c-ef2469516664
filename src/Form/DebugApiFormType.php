<?php

namespace App\Form;

use App\Entity\Brand;
use App\Entity\Country;
use App\Entity\Language;
use App\Entity\Channel;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\EmailType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Email;
use Symfony\Component\Validator\Constraints\Callback;
use Symfony\Component\Validator\Context\ExecutionContextInterface;

class DebugApiFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            // Optional fields - at least one must be provided
            ->add('email', EmailType::class, [
                'label' => 'Email',
                'required' => false,
                'constraints' => [
                    new Email(['message' => 'Please enter a valid email address.'])
                ]
            ])
            ->add('userID', TextType::class, [
                'label' => 'User ID',
                'required' => false,
            ])
            ->add('vin', TextType::class, [
                'label' => 'VIN',
                'required' => false,
            ])

            // Optional country filter
            ->add('brand', EntityType::class, [
                'class' => Brand::class,
                'choice_label' => 'name',
                'choice_value' => 'code',
                'label' => 'Brand',
                'placeholder' => 'Choose a brand',
                'required' => false,
            ])
            ->add('country', EntityType::class, [
                'class' => Country::class,
                'choice_label' => 'name',
                'choice_value' => 'code',
                'label' => 'Country',
                'placeholder' => 'Choose a country',
                'required' => false,
            ])
            ->add('language', EntityType::class, [
                'class' => Language::class,
                'choice_label' => 'label',
                'choice_value' => 'code',
                'label' => 'Language',
                'placeholder' => 'Choose a country',
                'required' => false,
            ])
            ->add('source', EntityType::class, [
                'class' => Channel::class,
                'choice_label' => 'name',
                'choice_value' => 'name',
                'label' => 'Source (Channel)',
                'placeholder' => 'Choose a source',
                'required' => false,
            ])
            ->add('targetParam', ChoiceType::class, [
                'label' => 'Target Parameter',
                'choices' => [
                    'B2C' => 'B2C',
                    'B2B' => 'B2B',
                ],
                'data' => 'B2C', // Default value
                'required' => false,
            ])
            ->add('os', ChoiceType::class, [
                'label' => 'Operating System',
                'choices' => [
                    'Android' => 'and',
                    'IOS' => 'ios',
                ],
                'data' => 'and',
                'required' => false,
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'constraints' => [
                new Callback([$this, 'validateAtLeastOneIdentifier'])
            ]
        ]);
    }

    /**
     * Custom validation to ensure at least one filter is provided
     */
    public function validateAtLeastOneIdentifier($data, ExecutionContextInterface $context): void
    {
        $email = $data['email'] ?? null;
        $userID = $data['userID'] ?? null;
        $vin = $data['vin'] ?? null;

        if (empty($email) && empty($userID) && empty($vin)) {
            $context->buildViolation('At least one filter (Email, User ID or VIN) must be provided.')
                ->atPath('email')
                ->addViolation();
        }
    }
}
