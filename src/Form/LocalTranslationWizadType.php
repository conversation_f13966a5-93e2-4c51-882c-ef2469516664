<?php

namespace App\Form;

use App\Entity\Brand;
use App\Entity\Country;
use App\Entity\Language;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Contracts\Translation\TranslatorInterface;

class LocalTranslationWizadType extends AbstractType
{
    private TranslatorInterface $translator;

    public function __construct(TranslatorInterface $translator)
    {
        $this->translator = $translator;
    }

    const SOURCES = ['APP' => 'APP', 'WEB' => 'WEB'];

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('languages', EntityType::class, [
                'class' => Language::class,
                'choice_label' => function ($language) {
                    return $this->translator->trans($language->getLabel());
                },
                'choice_value' => 'code',
                'label' => 'Languages',
                'placeholder' => 'Choose Language',
                'required' => false,
                'multiple' => true,
                'expanded' => false,
            ])
            ->add('brands', EntityType::class, [
                'class' => Brand::class,
                'choice_label' => 'name',
                'choice_value' => 'code',
                'label' => 'Brands',
                'placeholder' => 'Choose Brand',
                'required' => false,
                'multiple' => true,
                'expanded' => false,
            ])
            ->add('countries', EntityType::class, [
                'class' => Country::class,
                'choice_label' => 'name',
                'label' => 'Countries',
                'choice_value' => 'code',
                'placeholder' => 'Choose Country',
                'required' => false,
                'multiple' => true,
                'expanded' => false,
    
            ])->add('source', ChoiceType::class, [
                'choices' => self::SOURCES,
                'required' => true,
                'multiple' => true,
                'mapped' => true,
                'constraints' => [
                    new NotBlank(),
                ]
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'user' => null,
            // Additional options can be added here if needed
        ]);
    }
}