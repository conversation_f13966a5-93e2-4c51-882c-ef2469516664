<?php

namespace App\Controller;

use App\Helpers\LoggerTrait;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Authentication\AuthenticationUtils;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Drenso\OidcBundle\OidcClientInterface;
use App\Service\SingleLogoutService;

class LoginController extends AbstractController
{
   use LoggerTrait;

   public function __construct(public SingleLogoutService $singleLogoutService) { }
   /**
    * This controller forwards the user to the OIDC login
    *
    * @throws \Drenso\OidcBundle\Exception\OidcException
    */
   #[Route('/login_oidc', name: 'login_oidc')]
   public function surfconext(Request $request, OidcClientInterface $oidcClient): RedirectResponse
   {
      try{
         $this->logger->debug('Session state', ['state' => $request->getSession()->get('oidc_state')]);
         $this->logger->debug('Request state', ['state' => $request->query->get('state')]);
         // Redirect to authorization @ OIDC provider
         return $oidcClient->generateAuthorizationRedirect(null, ['openid', 'prd:sp4', 'profile']);

      } catch (\Exception $e) {
         $this->logger->error(__METHOD__.': Login OIDC failed', ['exception' => $e]);
         return $this->redirectToRoute('app_login'); // Redirect back to the login page
      }
   }

   #[Route('/login_check', name: 'login_check')]
   public function loginCheck(Request $request): Response
   {
      try {
         $this->logger->debug(__METHOD__.': Login check', ['request' => $request->query->all()]);

         if ($request->query->has('error')) {
            $message = sprintf(
               'Error: %s.  %s',
               $request->query->get('error', ''),
               $request->query->get('error_description', '')
            );

            throw new \Exception('Authentication failed, ' . $message);
         }

         // Redirect to the default target path after successful login
         return $this->redirectToRoute('default_target_path'); // Replace with your desired route
         #return new Response('login failed !');

      } catch (\Exception $e) {
         $this->logger->error(__METHOD__.': Login check failed', ['exception' => $e]);

         return $this->redirectToRoute('app_login'); // Redirect back to the login page
      }

   }

   #[Route(path: '/login', name: 'app_login')]
   public function index(AuthenticationUtils $authenticationUtils): Response
   {
      // get the login error if there is one
      $error = $authenticationUtils->getLastAuthenticationError();

      // last username entered by the user
      $lastUsername = $authenticationUtils->getLastUsername();

      $this->logger->debug(__METHOD__.': Login page', [
         'error' => $error, 
         'lastUsername' => $lastUsername
      ]);
      return $this->render('login/index.html.twig', [
         'last_username' => $lastUsername,
         'error'         => $error,
      ]);
   }

   #[Route(path: '/logout', name: 'app_logout')]
   public function logout(Request $request): void
   {
      $request->getSession()->clear();
   }

   #[Route(path: '/after-logout', name: 'after_logout')]
   public function afterLogout(): Response
    {
        // Redirect to SLO URL
        return $this->singleLogoutService->logout();
    }
}
