/**
 * Dynamic Form Sections Handler
 * Handles choice fields with dynamic sections that enable/disable based on selection
 */

$(document).ready(function() {
    console.log('🔄 Initializing Dynamic Form Sections...');
    
    // Initialize dynamic sections for all forms
    initializeDynamicSections();
    
    // Set up event listeners for choice field changes
    setupChoiceFieldListeners();
});

/**
 * Initialize dynamic sections on page load
 */
function initializeDynamicSections() {
    // Find all choice fields with dynamic attributes
    $('select[data-dynamic], input[type="radio"][data-dynamic]').each(function() {
        const $choiceField = $(this);
        const dynamicConfig = getDynamicConfig($choiceField);
        
        if (dynamicConfig) {
            console.log('🎛️ Found dynamic choice field:', $choiceField.attr('id'), dynamicConfig);
            
            // Initialize sections based on current selection
            initializeFieldSections($choiceField, dynamicConfig);
        }
    });
    
    // Also check for dynamic config in row_attr (from Twig templates)
    $('.multi-fields').each(function() {
        const $container = $(this);
        const $choiceField = $container.find('select, input[type="radio"]').first();
        
        if ($choiceField.length > 0) {
            const dynamicConfig = getDynamicConfigFromRowAttr($choiceField);
            
            if (dynamicConfig) {
                console.log('🎛️ Found dynamic choice field from row_attr:', $choiceField.attr('id'), dynamicConfig);
                
                // Store config as data attribute for easier access
                $choiceField.attr('data-dynamic', JSON.stringify(dynamicConfig));
                
                // Initialize sections
                initializeFieldSections($choiceField, dynamicConfig);
            }
        }
    });
}

/**
 * Get dynamic configuration from data attribute
 */
function getDynamicConfig($field) {
    const dynamicAttr = $field.attr('data-dynamic');
    if (dynamicAttr) {
        try {
            return JSON.parse(dynamicAttr);
        } catch (e) {
            console.error('Error parsing dynamic config:', e);
            return null;
        }
    }
    return null;
}

/**
 * Get dynamic configuration from Twig row_attr (for server-rendered forms)
 */
function getDynamicConfigFromRowAttr($field) {
    // Check if the field's container has dynamic configuration from Twig
    const $container = $field.closest('.multi-fields');
    const dynamicConfigAttr = $container.attr('data-dynamic-config');

    if (dynamicConfigAttr) {
        try {
            return JSON.parse(dynamicConfigAttr);
        } catch (e) {
            console.error('Error parsing dynamic config from container:', e);
        }
    }

    // Check if field itself has dynamic data attribute
    const fieldDynamicAttr = $field.attr('data-dynamic');
    if (fieldDynamicAttr) {
        try {
            return JSON.parse(fieldDynamicAttr);
        } catch (e) {
            console.error('Error parsing dynamic config from field:', e);
        }
    }

    return null;
}

/**
 * Initialize sections based on current field value
 */
function initializeFieldSections($choiceField, dynamicConfig) {
    const currentValue = $choiceField.val();
    const fieldContainer = getFieldContainer($choiceField);

    console.log('🔧 Initializing sections for field:', $choiceField.attr('id'), 'Current value:', currentValue);

    // Find existing sections for each option
    Object.keys(dynamicConfig).forEach(optionValue => {
        const sectionName = dynamicConfig[optionValue];
        const $sections = findExistingSection(fieldContainer, optionValue, sectionName);

        if (currentValue === optionValue) {
            // Enable the selected sections
            $sections.each(function() {
                enableSection($(this), optionValue);
            });
            console.log('✅ Enabled', $sections.length, 'section(s) for:', sectionName);
        } else {
            // Disable other sections
            $sections.each(function() {
                disableSection($(this), optionValue);
            });
            console.log('❌ Disabled', $sections.length, 'section(s) for:', sectionName);
        }
    });
}

/**
 * Set up event listeners for choice field changes
 */
function setupChoiceFieldListeners() {
    // Handle select dropdowns
    $(document).on('change', 'select[data-dynamic]', function() {
        const $choiceField = $(this);
        const dynamicConfig = getDynamicConfig($choiceField);
        
        if (dynamicConfig) {
            handleChoiceChange($choiceField, dynamicConfig);
        }
    });
    
    // Handle radio buttons
    $(document).on('change', 'input[type="radio"][data-dynamic]', function() {
        const $choiceField = $(this);
        const dynamicConfig = getDynamicConfig($choiceField);
        
        if (dynamicConfig) {
            handleChoiceChange($choiceField, dynamicConfig);
        }
    });
}

/**
 * Handle choice field value change
 */
function handleChoiceChange($choiceField, dynamicConfig) {
    const newValue = $choiceField.val();
    const fieldContainer = getFieldContainer($choiceField);

    console.log('🔄 Choice changed:', $choiceField.attr('id'), 'New value:', newValue);

    // Update all sections based on new selection
    Object.keys(dynamicConfig).forEach(optionValue => {
        const sectionName = dynamicConfig[optionValue];
        const $sections = findExistingSection(fieldContainer, optionValue, sectionName);

        if (newValue === optionValue) {
            $sections.each(function() {
                enableSection($(this), optionValue);
            });
            console.log('✅ Enabled', $sections.length, 'section(s) for:', sectionName);

            // Show notification
            if (typeof showNotification === 'function') {
                showNotification(`Enabled ${sectionName} section`, 'info');
            }
        } else {
            $sections.each(function() {
                disableSection($(this), optionValue);
            });
            console.log('❌ Disabled', $sections.length, 'section(s) for:', sectionName);
        }
    });
}

/**
 * Get the container element for the field
 */
function getFieldContainer($field) {
    // Try to find the closest form section or multi-fields container
    let $container = $field.closest('.multi-fields');
    
    if ($container.length === 0) {
        $container = $field.closest('.tab-pane');
    }
    
    if ($container.length === 0) {
        $container = $field.closest('form');
    }
    
    return $container;
}

/**
 * Find existing sections for the given option (no creation of new elements)
 */
function findExistingSection($container, optionValue, sectionName) {
    const $form = $container.closest('form');
    let $sections = $();

    // Check for manual section mapping first
    const $choiceField = $container.find('select, input[type="radio"]').first();
    const manualMapping = $choiceField.data('section-mapping');

    if (manualMapping && manualMapping[optionValue]) {
        // Use manual mapping
        manualMapping[optionValue].forEach(selector => {
            const $mappedSections = $form.find(selector);
            $sections = $sections.add($mappedSections);
        });

        if ($sections.length > 0) {
            console.log('✅ Using manual mapping for:', optionValue, 'Found', $sections.length, 'sections');
            $sections.attr('data-dynamic-section', optionValue);
            return $sections;
        }
    }

    // Method 1: Look for fields with names containing the option value
    $form.find('input, select, textarea').each(function() {
        const $field = $(this);
        const fieldId = $field.attr('id') || '';
        const fieldName = $field.attr('name') || '';
        const fieldLabel = $field.closest('.multi-fields').find('.feature-label').text().toLowerCase();

        // Check if field belongs to this section
        if (fieldId.toLowerCase().includes(optionValue.toLowerCase()) ||
            fieldName.toLowerCase().includes(optionValue.toLowerCase()) ||
            fieldLabel.includes(optionValue.toLowerCase()) ||
            fieldLabel.includes(sectionName.toLowerCase())) {

            const $section = $field.closest('.multi-fields');
            if ($section.length > 0) {
                $sections = $sections.add($section);
            }
        }
    });

    // Method 2: Look for sections with data-section containing the option value
    $form.find('.multi-fields').each(function() {
        const $section = $(this);
        const sectionId = $section.attr('data-section') || '';
        const sectionText = $section.text().toLowerCase();

        if (sectionId.toLowerCase().includes(optionValue.toLowerCase()) ||
            sectionText.includes(optionValue.toLowerCase()) ||
            sectionText.includes(sectionName.toLowerCase())) {
            $sections = $sections.add($section);
        }
    });

    // Method 3: Look for h5 section headers that match
    $form.find('h5').each(function() {
        const $header = $(this);
        const headerText = $header.text().toLowerCase();

        if (headerText.includes(optionValue.toLowerCase()) ||
            headerText.includes(sectionName.toLowerCase())) {
            // Find all multi-fields after this header until next h5
            let $nextElements = $header.nextUntil('h5');
            $nextElements.filter('.multi-fields').each(function() {
                $sections = $sections.add($(this));
            });
        }
    });

    // Remove duplicates and mark sections
    $sections = $sections.filter(function(index, element) {
        return $sections.index(element) === index;
    });

    // Mark the sections with our identifier for easier future reference
    $sections.attr('data-dynamic-section', optionValue);

    if ($sections.length === 0) {
        console.warn('⚠️ No existing sections found for option:', optionValue, sectionName);
    } else {
        console.log('✅ Found', $sections.length, 'section(s) for option:', optionValue, sectionName);
    }

    return $sections;
}

/**
 * Enable a section and its fields
 */
function enableSection($section, optionValue) {
    $section.removeClass('dynamic-section-disabled').addClass('dynamic-section-enabled');
    $section.show();
    
    // Enable all form fields in the section
    $section.find('input, select, textarea').each(function() {
        $(this).prop('disabled', false);
        $(this).removeClass('field-disabled');
    });
    
    // Add visual feedback
    $section.css({
        'opacity': '1',
        'pointer-events': 'auto'
    });
}

/**
 * Disable a section and its fields
 */
function disableSection($section, optionValue) {
    $section.removeClass('dynamic-section-enabled').addClass('dynamic-section-disabled');
    
    // Disable all form fields in the section
    $section.find('input, select, textarea').each(function() {
        $(this).prop('disabled', true);
        $(this).addClass('field-disabled');
    });
    
    // Add visual feedback
    $section.css({
        'opacity': '0.5',
        'pointer-events': 'none'
    });
}

/**
 * Debug function to show current dynamic sections state
 */
function debugDynamicSections() {
    console.group('🔍 Dynamic Sections Debug');
    
    $('[data-dynamic]').each(function() {
        const $field = $(this);
        const config = getDynamicConfig($field);
        const value = $field.val();
        
        console.log('Field:', $field.attr('id'), 'Value:', value, 'Config:', config);
    });
    
    $('[data-dynamic-section]').each(function() {
        const $section = $(this);
        const optionValue = $section.attr('data-dynamic-section');
        const isEnabled = $section.hasClass('dynamic-section-enabled');
        
        console.log('Section:', optionValue, 'Enabled:', isEnabled);
    });
    
    console.groupEnd();
}

/**
 * Manually trigger dynamic section setup for a specific field
 */
function setupDynamicField(fieldSelector, dynamicConfig) {
    const $field = $(fieldSelector);
    if ($field.length === 0) {
        console.error('Field not found:', fieldSelector);
        return;
    }

    // Add dynamic config to field
    $field.attr('data-dynamic', JSON.stringify(dynamicConfig));

    // Initialize sections
    initializeFieldSections($field, dynamicConfig);

    console.log('✅ Dynamic field setup complete for:', fieldSelector);
}

/**
 * Manually map sections to choice values (for complex forms)
 */
function mapSectionsToChoices(fieldSelector, sectionMapping) {
    const $field = $(fieldSelector);
    if ($field.length === 0) {
        console.error('Field not found:', fieldSelector);
        return;
    }

    // Store the manual mapping
    $field.data('section-mapping', sectionMapping);

    console.log('✅ Section mapping stored for:', fieldSelector, sectionMapping);

    // Example usage:
    // mapSectionsToChoices('#deployment_type', {
    //     'partner': ['.partner-section', '#partner-config'],
    //     'internal': ['.internal-section', '#internal-config']
    // });
}

/**
 * Test function to simulate choice changes
 */
function testDynamicChoice(fieldSelector, newValue) {
    const $field = $(fieldSelector);
    if ($field.length === 0) {
        console.error('Field not found:', fieldSelector);
        return;
    }

    $field.val(newValue).trigger('change');
    console.log('🧪 Triggered change for field:', fieldSelector, 'New value:', newValue);
}

// Make functions globally available for testing
window.debugDynamicSections = debugDynamicSections;
window.setupDynamicField = setupDynamicField;
window.mapSectionsToChoices = mapSectionsToChoices;
window.testDynamicChoice = testDynamicChoice;
