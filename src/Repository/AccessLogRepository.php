<?php

namespace App\Repository;

use App\Entity\AccessLog;
use App\Entity\Brand;
use App\Entity\Country;
use App\Entity\Language;
use App\Entity\Profile;
use App\Entity\Site;
use App\Entity\Widget;
use App\Entity\WidgetFeature;
use App\Entity\WidgetFeatureAttribute;
use App\Enum\orderColumns;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<AccessLog>
 *
 * @method AccessLog|null find($id, $lockMode = null, $lockVersion = null)
 * @method AccessLog|null findOneBy(array $criteria, array $orderBy = null)
 * @method AccessLog[]    findAll()
 * @method AccessLog[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AccessLogRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AccessLog::class);
    }

    public function getAccessLogData(int $start, int $length, ?array $orders, ?string $search)
    {
        $query = $this->createQueryBuilder('al')->leftJoin(
            Profile::class,
            'profile',
            Join::WITH,
            'al.profile = profile'
        )->leftJoin(
            Site::class,
            'site',
            Join::WITH,
            'profile.site = site'
        )->leftJoin(
            Brand::class,
            'brand',
            Join::WITH,
            'site.brand = brand'
        )->leftJoin(
            Country::class,
            'country',
            Join::WITH,
            'site.country = country'
        );

        // Create Count Query
        $countQuery = $this->createQueryBuilder('al')->leftJoin(
            Profile::class,
            'profile',
            Join::WITH,
            'al.profile = profile'
        )->leftJoin(
            Site::class,
            'site',
            Join::WITH,
            'profile.site = site'
        )->leftJoin(
            Brand::class,
            'brand',
            Join::WITH,
            'site.brand = brand'
        )->leftJoin(
            Country::class,
            'country',
            Join::WITH,
            'site.country = country'
        );
        $countQuery->select('COUNT(al)');

        if (!empty($search)) {
            $query->andWhere('
                site.label like :search
                OR al.username like :search
                OR al.entityClass like :search
                OR al.action like :search
                OR al.route like :search
                OR brand.code like :search
                OR country.code like :search
                OR al.timestamp like :search
            ')->setParameter(
                'search',
                "%" . trim($search) . "%"
            );
            $countQuery->andWhere('
                site.label like :search
                OR al.username like :search
                OR al.entityClass like :search
                OR al.action like :search
                OR al.route like :search
                OR brand.code like :search
                OR country.code like :search
                OR al.timestamp like :search
            ')->setParameter(
                'search',
                "%" . trim($search) . "%"
            );
        }

        // Limit
        $query->setFirstResult($start)->setMaxResults($length);

        // Order
        $this->orderColumns($orders, $query);
        // Execute
        $results = $query->getQuery()->getResult();
        $countResult = $countQuery->getQuery()->getSingleScalarResult();

        return array(
            "results" => $results,
            "countResult" => $countResult
        );
    }

    private function orderColumns($orders, &$query): void
    {
        foreach ($orders as $order) {
            // $order['name'] is the name of the order column as sent by the JS
            if ($order['name'] != '') {
                $orderColumn = orderColumns::tryFrom($order['name'])?->getOrder();
                if ($orderColumn !== null) {
                    $query->orderBy($orderColumn, $order['dir']);
                }
            }
        }
    }
    public function getWidgetLogs(
        array $widgetFeatureAttrId,
        Brand $brand,
        Country $country
    ): array {
        // Main query
        $query = $this->createQueryBuilder('al')
            ->select('al', 'wfa.name as name', 'l.label', 'wf.label as featureName', 'wf.source as channel')
            ->where('al.entityClass = :entityClass') // Use parameter for entity class
            ->andWhere('al.entityId IN (:ids)') // Use andWhere instead of where
            ->leftJoin(
                WidgetFeatureAttribute::class,
                'wfa',
                Join::WITH,
                'al.entityId = wfa.id'
            )
            ->leftJoin(
                WidgetFeature::class,
                'wf',
                Join::WITH,
                'wfa.widgetFeature = wf.id'
            )
            ->leftJoin(
                Language::class,
                'l',
                Join::WITH,
                'wfa.language = l.id'
            )
            ->andWhere('wf.brand = :brand')
            ->andWhere('wf.country = :country')
            ->setParameter('entityClass', 'App\Entity\WidgetFeatureAttribute') // Set entity class parameter
            ->setParameter('brand', $brand)
            ->setParameter('country', $country)
            ->setParameter('ids', $widgetFeatureAttrId);

        $query->addOrderBy('al.timestamp', 'DESC');
        // Execute queries
        $results = $query->getQuery()->getResult();
        return [
            'results' => $results
        ];
    }
}
