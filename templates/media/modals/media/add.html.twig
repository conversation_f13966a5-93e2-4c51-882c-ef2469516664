<div class="modal fade" id="add-media-modal" tabindex="-1" aria-labelledby="add-media-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="add-media-modal-label">{{ 'add_image'|trans }}</h5>
                <button class="close" type="button" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <form id="add-media-form" onsubmit="return false;">
                {{ form_start(formAdd) }}
                <div class="modal-body">
                    <div class="alert alert-secondary mb-4 mx-3 text-center d-none" id="alert-loading" role="alert">
                        <img src="/images/loading.png" alt="loading" class="loading" width="35"/>
                    </div>
                    <div class="alert alert-success mb-4 mx-3 d-none" id="alert-success" role="alert">
                        <span class="title"></span>
                    </div>
                    <div class="alert alert-danger mb-4 mx-3 d-none" id="alert-error" role="alert">
                        <span class="title"></span>
                    </div>
                    <div id="uppy-container" class="mx-4"></div>
                    <ul id="uppy-messages" class="text-danger" style="margin: 10px 0;"></ul>
                    {% include "media/modals/media/format_type_content.html.twig" %}
                    <div class="max-size mt-4 mb-4">
                        <span class="max-size-label text-uppercase font-weight-bold" style="font-size: 1.2rem;">{{ 'max_size'|trans }}:</span>
                        <span class="max-size-value" style="font-size: 1.1rem;">{{ media_size }} MB</span>
                    </div>
                    <div class="formulaire mx-4">
                        <div class="form-group">
                            {{ form_row(formAdd._token) }}
                            <input type="hidden" id="add_medias_form_id" name="add_medias_form_id">
                            <div class="row media_file_name">
                                <div class="col-md-3">
                                    {{ form_label(formAdd.name) }} <span class="text-danger">*</span>
                                </div>
                                <div class="col-md-7">
                                    {{ form_widget(formAdd.name, {'attr': {'class': 'form-control'}}) }}
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="row">
                                <div class="col-md-3">
                                    {{ form_label(formAdd.textAlt) }}
                                </div>
                                <div class="col-md-7">
                                    {{ form_widget(formAdd.textAlt, {'attr': {'class': 'form-control'}}) }}
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="row">
                                <div class="col-md-3">
                                    {{ form_label(formAdd.copyright) }}
                                </div>
                                <div class="col-md-7">
                                    {{ form_widget(formAdd.copyright, {'attr': {'class': 'form-control'}}) }}
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="row">
                                <div class="col-md-3">
                                    {{ form_label(formAdd.comment) }}
                                </div>
                                <div class="col-md-7">
                                    {{ form_widget(formAdd.comment, {'attr': {'class': 'form-control'}}) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="media-form-add-button" class="btn btn-success save-add-media btn-actions" disabled><i class="fas fa-save ms-1"></i> {{ 'save'|trans }}</button>
                    <button type="button" class="btn btn-secondary btn-actions" data-bs-dismiss="modal"><i class="fas fa-times-circle ms-1"></i> {{ 'btn_cancel'|trans }}</button>
                </div>
                {{ form_end(formAdd, {render_rest: false}) }}
            </form>
        </div>
    </div>
</div>








