<?php

namespace App\Entity;

use App\Entity\LcdvEvRouting;
use App\Entity\RpoEvRouting;
use App\Repository\EvRoutingRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: EvRoutingRepository::class)]
#[ORM\Table(name: 'ev_routing')]
#[UniqueEntity(fields: ['label', 'brand'], message: 'lcdvlabel_label_unique')]
class EvRouting
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'string', length: 255, name: 'label')]
    #[Assert\NotBlank(message: 'lcdvlabel_not_blank')]
    private ?string $label = null;

    #[ORM\Column(type: 'boolean', name: 'is_enabled', options: ['default' => true])]
    private bool $enabled = true;

    #[ORM\Column(type: 'string', length: 2)]
    private ?string $brand = null;

    #[ORM\Column(type: 'datetime', name: 'created_at', options: ['default' => 'CURRENT_TIMESTAMP'])]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\Column(type: 'datetime', name: 'updated_at', nullable: true)]
    private ?\DateTimeInterface $updatedAt = null;

    #[ORM\OneToMany(mappedBy: 'evRouting', targetEntity: LcdvEvRouting::class, orphanRemoval: true)]
    private Collection $lcdvs;

    #[ORM\OneToMany(mappedBy: 'evRouting', targetEntity: RpoEvRouting::class, orphanRemoval: true)]
    private Collection $rpos;

    #[ORM\ManyToOne(targetEntity: Language::class)]
    #[ORM\JoinColumn(name: 'vl_language_id', referencedColumnName: 'id', nullable: false, options: ['default' => 1])]
    private ?Language $language = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true, name: 'engine_type')]
    private ?string $engineType = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true, name: 'constant_speed_consumption_inkWh_per_hundredkm')]
    private ?string $constantSpeedConsumptionInkWhPerHundredkm = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true, name: 'max_charge_inkWh')]
    private ?string $maxChargeInkWh = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true, name: 'vehicle_max_speed')]
    private ?string $vehicleMaxSpeed = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true, name: 'vehicle_weight')]
    private ?string $vehicleWeight = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true, name: 'vehicle_axle_weight')]
    private ?string $vehicleAxleWeight = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true, name: 'vehicle_length')]
    private ?string $vehicleLength = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true, name: 'vehicle_width')]
    private ?string $vehicleWidth = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true, name: 'vehicle_height')]
    private ?string $vehicleHeight = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true, name: 'acceleration_efficiency')]
    private ?string $accelerationEfficiency = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true, name: 'deceleration_efficiency')]
    private ?string $decelerationEfficiency = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true, name: 'uphill_efficiency')]
    private ?string $uphillEfficiency = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true, name: 'downhill_efficiency')]
    private ?string $downhillEfficiency = null;

    #[ORM\Column(type: 'text', nullable: true, name: 'charging_curve_array')]
    private ?string $chargingCurveArray = null;

    #[ORM\Column(type: 'string', length: 255)]
    #[Assert\NotBlank]
    private string $dvq;

    #[ORM\Column(type: 'string', length: 255)]
    #[Assert\NotBlank]
    private string $b0f;

    #[ORM\Column(type: 'string', length: 255)]
    #[Assert\NotBlank]
    private string $dar;

    public function __construct()
    {
        $this->lcdvs = new ArrayCollection();
        $this->rpos = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }
    public function getLabel(): ?string
    {
        return $this->label;
    }
    public function setLabel(string $label): self
    {
        $this->label = $label;
        return $this;
    }
    public function isEnabled(): bool
    {
        return $this->enabled;
    }
    public function setEnabled(bool $enabled): self
    {
        $this->enabled = $enabled;
        return $this;
    }
    public function getBrand(): ?string
    {
        return $this->brand;
    }
    public function setBrand(string $brand): self
    {
        $this->brand = $brand;
        return $this;
    }
    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }
    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }
    public function getUpdatedAt(): ?\DateTimeInterface
    {
        return $this->updatedAt;
    }
    public function setUpdatedAt(?\DateTimeInterface $updatedAt): self
    {
        $this->updatedAt = $updatedAt;
        return $this;
    }
    public function getLcdvs(): Collection
    {
        return $this->lcdvs;
    }
    public function addLcdv(LcdvEvRouting $lcdv): self
    {
        if (!$this->lcdvs->contains($lcdv)) {
            $this->lcdvs[] = $lcdv;
            $lcdv->setEvRouting($this);
        }
        return $this;
    }
    public function removeLcdv(LcdvEvRouting $lcdv): self
    {
        if ($this->lcdvs->removeElement($lcdv) && $lcdv->getEvRouting() === $this) {
            $lcdv->setEvRouting(null);
        }
        return $this;
    }
    public function getRpos(): Collection
    {
        return $this->rpos;
    }
    public function addRpo(RpoEvRouting $rpo): self
    {
        if (!$this->rpos->contains($rpo)) {
            $this->rpos[] = $rpo;
            $rpo->setEvRouting($this);
        }
        return $this;
    }
    public function removeRpo(RpoEvRouting $rpo): self
    {
        if ($this->rpos->removeElement($rpo) && $rpo->getEvRouting() === $this) {
            $rpo->setEvRouting(null);
        }
        return $this;
    }
    public function getLanguage(): ?Language
    {
        return $this->language;
    }
    public function setLanguage(?Language $language): self
    {
        $this->language = $language;
        return $this;
    }
    public function getEngineType(): ?string
    {
        return $this->engineType;
    }
    public function setEngineType(?string $engineType): self
    {
        $this->engineType = $engineType;
        return $this;
    }
    public function getConstantSpeedConsumptionInkWhPerHundredkm(): ?string
    {
        return $this->constantSpeedConsumptionInkWhPerHundredkm;
    }
    public function setConstantSpeedConsumptionInkWhPerHundredkm(?string $val): self
    {
        $this->constantSpeedConsumptionInkWhPerHundredkm = $val;
        return $this;
    }
    public function getMaxChargeInkWh(): ?string
    {
        return $this->maxChargeInkWh;
    }
    public function setMaxChargeInkWh(?string $val): self
    {
        $this->maxChargeInkWh = $val;
        return $this;
    }
    public function getVehicleMaxSpeed(): ?string
    {
        return $this->vehicleMaxSpeed;
    }
    public function setVehicleMaxSpeed(?string $val): self
    {
        $this->vehicleMaxSpeed = $val;
        return $this;
    }
    public function getVehicleWeight(): ?string
    {
        return $this->vehicleWeight;
    }
    public function setVehicleWeight(?string $val): self
    {
        $this->vehicleWeight = $val;
        return $this;
    }
    public function getVehicleAxleWeight(): ?string
    {
        return $this->vehicleAxleWeight;
    }
    public function setVehicleAxleWeight(?string $val): self
    {
        $this->vehicleAxleWeight = $val;
        return $this;
    }
    public function getVehicleLength(): ?string
    {
        return $this->vehicleLength;
    }
    public function setVehicleLength(?string $val): self
    {
        $this->vehicleLength = $val;
        return $this;
    }
    public function getVehicleWidth(): ?string
    {
        return $this->vehicleWidth;
    }
    public function setVehicleWidth(?string $val): self
    {
        $this->vehicleWidth = $val;
        return $this;
    }
    public function getVehicleHeight(): ?string
    {
        return $this->vehicleHeight;
    }
    public function setVehicleHeight(?string $val): self
    {
        $this->vehicleHeight = $val;
        return $this;
    }
    public function getAccelerationEfficiency(): ?string
    {
        return $this->accelerationEfficiency;
    }
    public function setAccelerationEfficiency(?string $val): self
    {
        $this->accelerationEfficiency = $val;
        return $this;
    }
    public function getDecelerationEfficiency(): ?string
    {
        return $this->decelerationEfficiency;
    }
    public function setDecelerationEfficiency(?string $val): self
    {
        $this->decelerationEfficiency = $val;
        return $this;
    }
    public function getUphillEfficiency(): ?string
    {
        return $this->uphillEfficiency;
    }
    public function setUphillEfficiency(?string $val): self
    {
        $this->uphillEfficiency = $val;
        return $this;
    }
    public function getDownhillEfficiency(): ?string
    {
        return $this->downhillEfficiency;
    }
    public function setDownhillEfficiency(?string $val): self
    {
        $this->downhillEfficiency = $val;
        return $this;
    }
    public function getChargingCurveArray(): ?string
    {
        return $this->chargingCurveArray;
    }
    public function setChargingCurveArray(?string $val): self
    {
        $this->chargingCurveArray = $val;
        return $this;
    }
    public function getDvq(): string
    {
        return $this->dvq;
    }
    public function setDvq(string $dvq): self
    {
        $this->dvq = $dvq;
        return $this;
    }
    public function getB0f(): string
    {
        return $this->b0f;
    }
    public function setB0f(string $b0f): self
    {
        $this->b0f = $b0f;
        return $this;
    }
    public function getDar(): string
    {
        return $this->dar;
    }
    public function setDar(string $dar): self
    {
        $this->dar = $dar;
        return $this;
    }
}