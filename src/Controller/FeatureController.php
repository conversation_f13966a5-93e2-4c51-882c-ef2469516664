<?php

namespace App\Controller;

use App\Entity\FeatureSetting;
use App\Repository\MediaRepository;
use App\Repository\SiteRepository;
use App\Security\User;
use App\Service\FeatureManager;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;

#[Route(path: '/feature', name: 'feature')]
class FeatureController extends AbstractController
{
    #[Route(path: '/{id}/edit', name: '')]
    public function index(
        FeatureSetting $featureSetting,
        FeatureManager $featureManager,
        Request $request,
        #[CurrentUser] User $user,
        SiteRepository $siteRepository,
        MediaRepository $mediaRepository
    ): Response {
        $profile = $user->getProfile();
        $site = $profile->getSite();

        $site = $siteRepository->find($site->getId());
        $brand = $site?->getBrand()?->getCode() ?? $profile->getBrand()?->getCode();
        $country = $site?->getCountry()?->getCode();

        $languages = $site?->getLanguages();

        $feature = $featureManager->buildFeature($user, $featureSetting, $brand, $country, $languages, $request->getLocale());
        $forms = $formViews = [];
        foreach ($feature->getForm() as $_source => $form) {
            $forms[$_source] = $form;
            $formViews[$_source] = $form->createView();
        }

        if (count($forms) === 0) {
            $this->addFlash('danger', 'failed to load the form ' . $featureSetting->getName());

            return $this->redirectToRoute('admin');
        }

        $source = $request->query->get('source');
        $form = $source ? $forms[$source] : reset($forms);
        $view = $feature->getTemplate();
        $form->handleRequest();

        if ($form->isSubmitted()) {
            $featureManager->saveFeature($featureSetting, $form, $brand, $country, $languages);
            $this->addFlash('success', $featureSetting->getName() . ' settings has been successfully saved !');

            return $this->redirectToRoute('feature', ['id' => $featureSetting->getId()]);
        }

        $media = $mediaRepository->findBy(['site' => $site]);

        return $this->render("feature/$view.html.twig", [
            'forms' => $formViews,
            'title' => $feature->getTitle(),
            'source' => $source,
            'medias' => $media,
            'profile' => $profile
        ]);
    }
}
