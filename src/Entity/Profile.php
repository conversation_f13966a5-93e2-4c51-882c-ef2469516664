<?php

namespace App\Entity;

use App\Helpers\RoleHelper;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use <PERSON><PERSON>\Serializer\Annotation\ExclusionPolicy;
use <PERSON><PERSON>\Serializer\Annotation\Expose;

#[ORM\Table(name: 'profile')]
#[ORM\Entity(repositoryClass: 'App\Repository\ProfileRepository')]
#[ExclusionPolicy('all')]
class Profile
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer', name: 'id')]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: Site::class, fetch: 'EAGER', inversedBy: 'profiles')]
    #[Expose]
    private Site $site;

    #[ORM\ManyToOne(targetEntity: 'App\Entity\Role')]
    #[ORM\JoinColumn(nullable: false, name: 'role_id', referencedColumnName: 'id')]
    #[Expose]
    private Role $role;

    #[ORM\Column(type: 'boolean', name: 'profile_admin', options: ['default' => false])]
    private bool $isAdmin = false;

    #[ORM\ManyToOne(targetEntity: 'App\Entity\Brand')]
    #[ORM\JoinColumn(nullable: true)]
    private ?Brand $brand = null;

    #[ORM\OneToMany(mappedBy: 'profile', targetEntity: ProfileMenu::class, orphanRemoval: true, cascade: ['persist'])]
    private Collection $profileMenus;

    #[ORM\OneToMany(mappedBy: 'loginProfile', targetEntity: UserPreference::class)]
    private Collection $userPreferences;

    /**
     * @var Collection<int, Media>
     */
    #[ORM\OneToMany(mappedBy: 'profile', targetEntity: Media::class)]
    private Collection $medias;

    public function __construct()
    {
        $this->userPreferences = new ArrayCollection();
        $this->profileMenus = new ArrayCollection();
        $this->medias = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getSite(): Site
    {
        return $this->site;
    }

    public function setSite($site): self
    {
        $this->site = $site;

        return $this;
    }

    public function getRole(): Role
    {
        return $this->role;
    }

    public function setRole(Role $role): self
    {
        $this->role = $role;

        return $this;
    }

    public function getIsAdmin(): ?bool
    {
        return $this->isAdmin;
    }

    public function setIsAdmin(bool $isAdmin): self
    {
        $this->isAdmin = $isAdmin;

        return $this;
    }

    public function __toString()
    {
        return $this->role->getLabel() ?? '';
    }

    public function getBrand(): ?Brand
    {
        return $this->brand;
    }

    public function setBrand(?Brand $brand): self
    {
        $this->brand = $brand;

        return $this;
    }

    /**
     * @return Collection<int, ProfileMenu>
     */
    public function getProfileMenus(): Collection
    {
        return $this->profileMenus;
    }

    public function addProfileMenu(ProfileMenu $profileMenu): self
    {
        if (!$this->profileMenus->contains($profileMenu)) {
            $this->profileMenus->add($profileMenu);
            $profileMenu->setProfile($this);
        }

        return $this;
    }

    public function removeProfileMenu(ProfileMenu $profileMenu): self
    {
        if ($this->profileMenus->removeElement($profileMenu)) {
            // set the owning side to null (unless already changed)
            if ($profileMenu->getProfile() === $this) {
                $profileMenu->setProfile(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, UserPreference>
     */
    public function getUserPreferences(): Collection
    {
        return $this->userPreferences;
    }

    public function addUserPreference(UserPreference $userPreference): self
    {
        if (!$this->userPreferences->contains($userPreference)) {
            $this->userPreferences->add($userPreference);
            $userPreference->setLoginProfile($this);
        }

        return $this;
    }

    public function removeUserPreference(UserPreference $userPreference): self
    {
        if ($this->userPreferences->removeElement($userPreference)) {
            // set the owning side to null (unless already changed)
            if ($userPreference->getLoginProfile() === $this) {
                $userPreference->setLoginProfile(null);
            }
        }

        return $this;
    }

    /**
    * Use isSuperAdmin() instead
    **/
    public function isSuperAdministrator(): bool
    {
        $roles = ['Super Administrator'];
        return in_array($this->getRole()->getLabel(), $roles);
    }

    /**
    * Use isBrandAdministrator() instead
    **/
    public function isLocalAdministrator(): bool
    {
        return $this->isBrandAdministrator();
    }

    public function isBrandAdministrator(): bool
    {
        $roles = ['Functional Administrator', 'Technical Administrator'];
        return in_array($this->getRole()->getLabel(), $roles);
    }

    public function isCountryUser(): bool
    {
        $roles = [
            'Reader',
            'Operations',
            'Webmaster',
            'Local Technical Administrator'
        ];
        return in_array($this->getRole()->getLabel(), $roles);
    }

    /**
     * @return Collection<int, Media>
     */
    public function getMedias(): Collection
    {
        return $this->medias;
    }

    public function addMedia(Media $media): static
    {
        if (!$this->medias->contains($media)) {
            $this->medias->add($media);
            $media->setProfile($this);
        }

        return $this;
    }

    public function removeMedia(Media $media): static
    {
        if ($this->medias->removeElement($media)) {
            // set the owning side to null (unless already changed)
            if ($media->getProfile() === $this) {
                $media->setProfile(null);
            }
        }

        return $this;
    }

    public function isGlobalAndCentralAdministrator(){
        return in_array($this->getRole()->getName(), RoleHelper::GLOBAL_CENTRAL_ROLES);
    }

    public function isLocalProfile(){
       return RoleHelper::isLocalProfile($this->getRole()?->getName());
    }

    public function isSuperAdmin(){
       return RoleHelper::isSuperAdmin($this->getRole()?->getName());
    }

    public function isCentralAdministrator(){
        return in_array($this->getRole()->getName(), RoleHelper::CENTRAL_ROLES);
    }

    public function isLocalCentralProfile(){
        return RoleHelper::isLocalCentralProfile($this->getRole()?->getName());
     }
}
