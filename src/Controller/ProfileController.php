<?php

namespace App\Controller;

use App\Entity\Profile;
use App\Entity\Role;
use App\Entity\Site;
use App\Form\ProfileLoginFormType;
use App\Form\ProfileMenuFormType;
use App\Repository\MenuRepository;
use App\Repository\ProfileRepository;
use App\Repository\SiteRepository;
use App\Security\User;
use App\Service\FavoriteService;
use App\Service\FeatureManager;
use App\Service\MenuManager;
use App\Service\ProfileService;
use App\Service\UserPreferencesManager;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bridge\Doctrine\Attribute\MapEntity;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Core\Exception\AccessDeniedException;
use Symfony\Component\Security\Http\Attribute\CurrentUser;
use Symfony\Contracts\Translation\TranslatorInterface as TranslationTranslatorInterface;

#[Route(path: '/profile')]
class ProfileController extends AbstractController
{
    #[Route(path: '/login', name: 'profile_login')]
    public function loginProfile(
        Request $request,
        #[CurrentUser] User $user,
        UserPreferencesManager $userPreferencesManager,
        ProfileService $profileService,
        ProfileRepository $profileRepository,
        TranslationTranslatorInterface $translator

    ): Response {
        // try to read saved preferences before
        $userPreferences = $userPreferencesManager->getUserPreferences($user->getUserIdentifier());
        $userPreferencesProfile = $profileRepository->findWithRelations($userPreferences?->getLoginProfile()?->getId());

        if (!empty($userPreferencesProfile) && !empty($userPreferences->getLoginLanguage()?->getCode())) {
            $this->setCurrentProfile(
                $userPreferencesProfile,
                $user,
                $userPreferences->getLoginLanguage()->getCode(),
                $request,
                $profileService
            );

            return $this->redirectToRoute('admin');
        }

        // if not saved preferences, show login form
        $form = $this->createForm(ProfileLoginFormType::class);
        $form->handleRequest($request);
        $session = $request->getSession();
        if ($form->isSubmitted() && $form->isValid()) {
            $data = $form->getData();

            $profile = $profileRepository->findWithRelations($data['profile']->getId());
            $this->setCurrentProfile($profile, $user, $data['language'], $request, $profileService);

            $inserted = $userPreferencesManager->savePreferences(
                $user->getUserIdentifier(),
                $profile,
                $data['language']
            );
            if ($inserted) {
                $this->addFlash('success', $translator->trans('user_preferences_save'));
            } else {
                $this->addFlash('danger', 'faild to save User Preferences');
            }

            return $this->redirectToRoute('admin');
        }

        return $this->render('profile/login.html.twig', [
            'form' => $form->createView(),
            'current_lang' => $session->get('language'),
        ]);
    }

    private function setCurrentProfile(
        Profile $profile,
        User $user,
        string $language,
        Request $request,
        ProfileService $profileService
    ): void {
        $user->setProfile($profile);
        $request->getSession()->set('selected_profile' . $user->getUserIdentifier(), $profile);
        $request->getSession()->set('language', $language);
        $user->setRoles(array_merge($user->getRoles(), ['ROLE_ADMIN']));

        $logoText = $profileService->getLogoText($profile);
        $request->getSession()->set('logoText', $logoText);

        $brand = $profileService->getBrand($profile);
        $request->getSession()->set('brand', $brand);

        $brandCssPathFilename = $profileService->getBrandCssPathFilename($profile->getSite()->getBrand() ?? $profile->getBrand());
        $request->getSession()->set('brandCssPathFilename', $brandCssPathFilename);

        return;
    }

    #[Route(path: '/top-bar', name: 'header_profile_form')]
    public function headerForm(Request $request, #[CurrentUser] User $user): Response
    {
        $profile = $user->getProfile();
        if (empty($profile)) {
            throw new AccessDeniedException('Profile not selected');
        }

        $site = $profile->getSite();
        $form = $this->createForm(ProfileLoginFormType::class, [
            'brand' => $site->getBrand(),
            'country' => $site->getCountry(),
            'language' => $request->getSession()->get('language'),
            'profile' => $user->getProfile(),
        ]);

        return $this->render('_layout/header_profile.html.twig', [
            'form' => $form->createView(),
        ]);
    }

    #[Route(path: '/submit-profile', name: 'header_profile_form_submit', methods: ['POST'])]
    public function submitProfile(
        Request $request,
        #[CurrentUser] User $user,
        ProfileService $profileService,
        MenuManager $menuManager,
        ProfileRepository $profileRepository,
        SiteRepository $siteRepository
    ): RedirectResponse {
        $site = $user->getProfile()?->getSite();
        $form = $this->createForm(ProfileLoginFormType::class, [
            'brand' => $site?->getBrand(),
            'country' => $site?->getCountry(),
            'language' => $request->getSession()->get('language'),
            'profile' => $user->getProfile(),
        ]);


        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {

            $data = $form->getData();
            $profile = $data['profile'];

            $countryChange = (isset($data['country']) && $data['country']->getCode() != $profile->getSite()->getCountry()?->getCode() ?? "XX");
            $adminProfile = (($profile->getSite()->getBrand()?->getCode() ?? "XX") == 'XX');
            $role = $profile->getRole();
            $site = $siteRepository->findOneBy(['brand' => $data['brand'], 'country' => $data['country']]);

            $profile = ($countryChange && !$adminProfile) ?
                $profileRepository->findOneBy(['site' => $site, 'role' => $role]) :
                $profileRepository->findWithRelations($data['profile']->getId());

            $referer = $menuManager->getRefererRoute($profile ?? $data['profile'], $request);
            if (empty($profile)) {
                $this->addFlash('danger', 'Please, choose a profile');

                return $this->redirectToRoute($referer['route'], $referer['params']);
            }

            $user->setProfile($profile);

            $request->getSession()->set('selected_profile' . $user->getUserIdentifier(), $profile);
            $request->getSession()->set('language', $data['language']);

            $logoText = $profileService->getLogoText($profile);
            $request->getSession()->set('logoText', $logoText);

            $brand = $profileService->getBrand($profile);
            $request->getSession()->set('brand', $brand);

            $brandCssPathFilename = $profileService->getBrandCssPathFilename($profile->getSite()->getBrand() ?? $profile->getBrand());
            $request->getSession()->set('brandCssPathFilename', $brandCssPathFilename);

            return $this->redirectToRoute($referer['route'], $referer['params']);
        }

        return $this->redirectToRoute('admin');
    }

    #[Route(path: '/global', name: 'profile_global_list')]
    public function globalList(ProfileService $profileService): Response
    {
        $profiles = $profileService->getAllProfilesOrderedBySite();

        return $this->render('profile/global_list.html.twig', [
            'profiles' => $profiles,
        ]);
    }

    #[Route(path: '/local', name: 'profile_local_list')]
    public function localList(ProfileService $profileService): Response
    {
        $profiles = $profileService->getAllProfilesOrderedBySite();

        return $this->render('profile/local_list.html.twig', [
            'profiles' => $profiles,
        ]);
    }

    /**
     * get menu List by role
     * @return Response
     */
    #[Route(path: '/{id}/menu/local/list', name: 'profile_local_menu_list')]
    #[Route(path: '/{id}/menu/global/list', name: 'profile_global_menu_list')]
    public function menuList(Profile $profile, ProfileService $profileService): Response
    {
        $menuTreeNodes = $profileService->getMenuTreeNodes($profile);

        return new JsonResponse($menuTreeNodes);
    }

    /**
     * get menu List by role
     * @return Response
     */
    #[Route(path: '/global/site/{site_id}/role/{role_id}', name: 'profile_global_menu_list_by_site_role')]
    #[Route(path: '/local/site/{site_id}/role/{role_id}', name: 'profile_local_menu_list_by_site_role')]
    public function menuListBySiteAndRole(
        #[MapEntity(expr: 'repository.find(site_id)')] Site $site,
        #[MapEntity(expr: 'repository.find(role_id)')] Role $role,
        ProfileService $profileService
    ): Response {
        $menuTreeNodes = $profileService->getMenuTreeNodesBySiteRole($site, $role);

        return new JsonResponse($menuTreeNodes);
    }

    #[Route(path: '/global/{id}/edit', name: 'profile_global_menu_edit')]
    #[Route(path: '/local/{id}/edit', name: 'profile_local_menu_edit')]
    public function edit(Profile $profile, EntityManagerInterface $em, Request $request): Response
    {
        $routeName = $request->attributes->get('_route');
        $page = ($routeName == 'profile_global_menu_edit') ? 'global' : 'local';
        $form = $this->createForm(ProfileMenuFormType::class, $profile);
        $form->handleRequest($request);
        if ($form->isSubmitted() && $form->isValid()) {
            $em->persist($profile);
            $em->flush();
            $this->addFlash('success', 'profile_flash_success_update');
        }

        return $this->render('profile/edit.html.twig', [
            'form' => $form->createView(),
            'profile' => $profile,
            'page' => $page
        ]);
    }

    #[Route(path: '/main-sidebar', name: 'main_sidebar')]
    public function mainSideBar(
        ProfileRepository $profileRepository,
        MenuRepository $menuRepository,
        #[CurrentUser] User $user,
        FeatureManager $featureManager,
        FavoriteService $favoriteService,
        ProfileService $profileService
    ): Response {
        $currentProfileId = $user->getProfile()->getId();
        $profile = $profileRepository->find($currentProfileId);
        $brand = $profile->getSite()->getBrand();
        $role = $profile->getRole();
        $roleMenus = $role->getRoleMenus();
        $menusIds = $subMenus = [];
        foreach ($roleMenus as $roleMenuItem) {
            $profileService->getMenuParentId($roleMenuItem->getMenu(), $menusIds);
            $subMenus[] = $roleMenuItem->getMenu();
        }
        $parentsMenus = $menuRepository->findBySubMenus($subMenus);
        $menus = array_merge($subMenus, $parentsMenus);

        $menusIds = $profileService->getCleanAuthorizedMenus($profile);

        return $this->render('_layout/main_sidebar.html.twig', [
            'menus' => $menus,
            'menusIds' => $menusIds,
            'role' => $role,
            'brand' => $brand,
            'featuresMenu' => $featureManager->getFeaturesForMenu(),
            'favorites' => $favoriteService->getFavorites($user->getUserIdentifier(), $role),
            'profile' => $profile->getId()
        ]);
    }

    #[Route('/profile_test', name: 'profile_test')]
    public function test(Request $request, #[CurrentUser] User $user): JsonResponse
    {
        return new JsonResponse($user->getProfile());
    }
}
