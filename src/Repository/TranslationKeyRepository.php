<?php

namespace App\Repository;

use App\Entity\Brand;
use App\Entity\Country;
use App\Entity\Language;
use App\Entity\LocalTranslation;
use App\Entity\ReferenceTranslation;
use App\Entity\Site;
use App\Entity\Widget;
use App\Entity\TranslationKey;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\QueryBuilder;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<TranslationKey>
 *
 * @method TranslationKey|null find($id, $lockMode = null, $lockVersion = null)
 * @method TranslationKey|null findOneBy(array $criteria, array $orderBy = null)
 * @method TranslationKey[]    findAll()
 * @method TranslationKey[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class TranslationKeyRepository extends ServiceEntityRepository
{
    public function __construct(
        ManagerRegistry $registry,
        private ReferenceTranslationRepository $referenceTranslationRepository
    ) {
        parent::__construct($registry, TranslationKey::class);
    }

    /**
     * Get all local translations filtered by the given brand
     * 
     * @return array
     */
    public function findByBrand(Brand $brand): array
    {
        return $this
            ->createQueryBuilder('translationkey')
            ->where('translationkey.brand = :brand')
            ->leftJoin('translationkey.localTranslations', 'localTranslation')
            ->leftjoin('localTranslation.site', 'site', 'WITH', 'site.brand =:brand')
            ->setParameter('brand', $brand)
            ->getQuery()
            ->getResult();
    }

    /**
     * Get all label translations for a given brand/country/source filtred by widget if no local translation found for a label we peek the reference translation
     *
     * @return array
     */
    public function getTranslatedKeysByWidget(?Widget $widget, ?Brand $brand, ?Country $country, ?string $source, ?Language $language, ?Language $referenceLanguage): array
    {
        return $this
            ->createQueryBuilder('translationkey')
            ->select('translationkey.id as id, referenceTranslation.translation as referenceTrans, localTranslation.translation as localTrans, translationkey.labelKey as label,(
                CASE 
                    WHEN localTranslation.translation IS NOT NULL
                    THEN localTranslation.translation
                    ELSE referenceTranslation.translation
                END
              ) as translation')
            ->where('translationkey.brand = :brand')
            ->andwhere('translationkey.widget = :widget')
            ->andwhere('translationkey.channel = :channel')
            ->leftJoin('translationkey.localTranslations', 'localTranslation', 'WITH', 'localTranslation.language =:language')
            ->leftjoin('localTranslation.site', 'site', 'WITH', 'site.brand = :brand and site.country =:country')
            ->leftJoin('translationkey.referenceTranslations', 'referenceTranslation', 'WITH', 'referenceTranslation.language =:referenceLanguage')
            ->setParameter('widget', $widget)
            ->setParameter('referenceLanguage', $referenceLanguage)
            ->setParameter('channel', $source)
            ->setParameter('brand', $brand)
            ->setParameter('country', $country)
            ->setParameter('language', $language)
            ->getQuery()
            ->getArrayResult();
    }

    public function findLocalTranslations(Brand $brand, Country $country): array
    {
        return $this
            ->createQueryBuilder('translationkey')
            ->where('translationkey.brand = :brand')
            ->leftJoin('translationkey.localTranslations', 'localTranslation')
            ->leftjoin('localTranslation.site', 'site', 'WITH', 'site.brand =:brand and site.country =:country')
            ->setParameter('brand', $brand)
            ->setParameter('country', $country)
            ->getQuery()
            ->getResult();
    }

    /**
     * @return TranslationKey[]
     */
    public function getAllTranslations(array $params): array
    {

        $query = $this->createQueryBuilder('tk')
            ->addSelect(
                'tk.id',
                'tk.createdAt',
                'tk.updatedAt',
                'tk.releaseDate',
                'tk.feature',
                'tk.channel',
                'tk.labelKey',
                'brand.name as brandName',
                'widget.name as widgetName',
                'rt.translation'
            )
            ->join('tk.brand', 'brand')
            ->leftJoin('tk.widget', 'widget')
            ->leftJoin('tk.referenceTranslations', 'rt')
            ->leftJoin('rt.language', 'language')
            ->orderBy($params['order']['name'], strtoupper($params['order']['dir']));
        $where = '';
        if (!empty($params['search']) && $params['search']) {
            $fields = ['tk.feature', 'tk.labelKey', 'tk.channel', 'brand.name', 'widget.name', 'rt.translation'];
            foreach ($fields as $field) {
                $where .= ($where) ? ' OR ' : '';
                $where .= $field . ' LIKE :search ';
            }
            $query->where($where);
            $query->setParameter('search', "%" . trim($params['search']) . "%");
        }
        if (!empty($params['widget']) && $params['widget']) {
            $query->andWhere('widget = :widget')->setParameter('widget', $params['widget']);
        }
        if (!empty($params['searchParams']['widget'])) {
            $query->andWhere('widget.name LIKE :widget ')->setParameter('widget', "%" . trim($params['searchParams']['widget']) . "%");
        }
        if (!empty($params['searchParams']['feature'])) {
            $query->andWhere('tk.feature LIKE :feature ')->setParameter('feature', "%" . trim($params['searchParams']['feature']) . "%");
        }
        if (!empty($params['searchParams']['channel'])) {
            $query->andWhere('tk.channel LIKE :channel ')->setParameter('channel', "%" . trim($params['searchParams']['channel']) . "%");
        }
        if (!empty($params['searchParams']['brand'])) {
            $query->andWhere('brand.name LIKE :brand ')->setParameter('brand', "%" . trim($params['searchParams']['brand']) . "%");
        }

        $query->andWhere('language.code = :language')->setParameter('language', trim($params['searchParams']['language']));
        $query->getQuery();

        $paginator = new Paginator($query, fetchJoinCollection: true);
        $paginator->getQuery()
            ->setMaxResults($params['length'])
            ->setFirstResult($params['start'])
            ->getArrayResult();

        $results = [
            'data' => array(),
            'recordsFiltered' => count($paginator),
            'recordsTotal' => count($paginator)
        ];

        foreach ($paginator as $translationKeyItem) {
            $translationKeyItem['createdAt'] = $translationKeyItem['createdAt']->format('m/d/Y');
            $translationKeyItem['updatedAt'] = $translationKeyItem['updatedAt']->format('m/d/Y');
            if (!empty($translationKeyItem['releaseDate'])) {
                $translationKeyItem['releaseDate'] = $translationKeyItem['releaseDate']->format('m/d/Y');
            }
            $results['data'][] = $translationKeyItem;
        }

        return $results;
    }

    public function getUniqueLanguageCode($brand, $country, $language)
    {
        // Step 1: Initialize the QueryBuilder and select required fields
        $queryBuilder = $this->createQueryBuilder('tk')
            ->distinct()
            ->select('localTranslationLanguage.code as code')
            ->leftJoin('tk.widget', 'widget')
            ->leftJoin('tk.localTranslations', 'localTranslation')
            ->leftJoin('localTranslation.language', 'localTranslationLanguage')
            ->leftJoin('tk.referenceTranslations', 'referenceTranslation')
            ->leftJoin('localTranslation.site', 'site', 'WITH', 'site.brand = :brand' . ($country ? ' AND site.country = :country' : ''))
            // ->where('localTranslationLanguage.code IS NOT NULL')
            ->setParameter('brand', $brand);

        // Step 2: Add conditional parameters for country and language
        if ($country) {
            $queryBuilder->setParameter('country', $country);
        }

        if ($language) {
            $queryBuilder->andWhere('referenceTranslation.language = :language')
                ->setParameter('language', $language);
        }

        $queryBuilder->orderBy('code', 'ASC');

        return $queryBuilder->getQuery()->getArrayResult();
    }

    private function createQueryBuilderForAllLocalTranslations(array $params, Brand $brand, ?Country $country, ?Language $language): QueryBuilder
    {
        // Step 1: Initialize the QueryBuilder and select required fields
        $queryBuilder = $this->createQueryBuilder('tk')
            ->select([
                    'tk.id AS translationKeyId',
                    'localTranslation.id as localTranslationId',
                    'referenceTranslation.id as referenceTranslationId',
                    'widget.id',
                    'tk.createdAt',
                    'tk.updatedAt',
                    'tk.releaseDate',
                    'tk.feature',
                    'tk.channel',
                    'tk.labelKey',
                    'tk.parameterValue',
                    'widget.name AS widgetName',
                    'localTranslation.translation as translation',
                    'siteLanguage.code AS languageCode',
                    'siteLanguage.label AS languageName',
                    'referenceTranslation.translation as translationReference',
                    'country.name AS countryName',
                    'siteLanguage.id AS languageId',
                    'site.id AS siteId',
                    'tk.createdAt as translationCreatedAt',
                    'localTranslation.updatedAt AS localTranslationUpdatedAt',
                    'tk.updatedAt AS translationUpdatedAt',
                    'tk.releaseDate as releaseDate',
                ])
            ->innerJoin('tk.brand', 'brand')
            ->innerJoin('brand.sites', 'site')
            ->leftJoin('site.languages', 'siteLanguage')
            ->innerJoin('site.country', 'country')
            ->leftJoin('tk.localTranslations', 'localTranslation', 'WITH', 'localTranslation.language = siteLanguage AND localTranslation.site = site')
            ->leftJoin('tk.referenceTranslations', 'referenceTranslation', 'WITH', 'referenceTranslation.language = site.preferedLanguage')
            ->leftJoin('tk.widget', 'widget')
            ->where('tk.brand = :brand')
            ->setParameter('brand', $brand);

        // Step 2: Add conditional parameters for country and language
        if (!empty($country)) {
            $queryBuilder->andWhere('country.name = :country or country.name is null')
                ->setParameter('country', $country->getName());
        }

        if (!empty($language)) {
            $queryBuilder->andWhere('siteLanguage = :language OR siteLanguage IS NULL')
                ->setParameter('language', $language);
        }

        // Step 3: Apply ordering from parameters
        $map = [
            'countryName' => 'country.name',
            'languageName' => 'siteLanguage.label',
            'translation' => 'localTranslation.translation',
            'translationReference' => 'referenceTranslation.translation',
        ];

        $orderName = $params['order']['name'];
        $orderDir = strtoupper($params['order']['dir']);
        $orderName = (array_key_exists($orderName, $map)) ? $map[$orderName] : $orderName;
        $queryBuilder->orderBy($orderName, $orderDir);

        // Step 4: Apply search filter if provided
        if (!empty($params['search'])) {
            $searchFields = ['tk.feature', 'tk.labelKey', 'tk.channel', 'widget.name', 'referenceTranslation.translation'];
            $searchCondition = implode(' OR ', array_map(function ($field) {
                return "$field LIKE :search";
            }, $searchFields));
            $queryBuilder->andWhere($searchCondition)
                ->setParameter('search', "%" . trim($params['search']) . "%");
        }

        // Step 5: Apply specific search filters if provided
        $searchParams = $params['searchParams'];
        if (!empty($searchParams['widget'])) {
            $queryBuilder->andWhere('widget.name LIKE :widget')
                ->setParameter('widget', "%" . trim($searchParams['widget']) . "%");
        }
        if (!empty($searchParams['feature'])) {
            $queryBuilder->andWhere('tk.feature LIKE :feature')
                ->setParameter('feature', "%" . trim($searchParams['feature']) . "%");
        }
        if (!empty($searchParams['channel'])) {
            $queryBuilder->andWhere('tk.channel LIKE :channel')
                ->setParameter('channel', "%" . trim($searchParams['channel']) . "%");
        }
        if (!empty($searchParams['country'])) {
            $queryBuilder->andWhere('country.name LIKE :country')
                ->setParameter('country', "%" . trim($searchParams['country']) . "%");
        }

        if (isset($searchParams['languageCode']) && !empty($searchParams['languageCode'])) {
            $queryBuilder->andWhere('siteLanguage.code LIKE :languageCode')
                ->setParameter('languageCode', "%" . trim($searchParams['languageCode']) . "%");
        }

        return $queryBuilder;
    }

    public function getAllLocalTranslations(array $params, Brand $brand, ?Country $country, ?Language $language): array
    {
        $queryBuilder = $this->createQueryBuilderForAllLocalTranslations($params, $brand, $country, $language);

        // calculate total count
        // Note: is not possible use Doctrine Paginator because present of Many2Many relation generate error
        $countQueryBuilder = clone $queryBuilder;
        $countQueryBuilder->select('COUNT(tk.id)')
            ->setFirstResult(null)
            ->setMaxResults(null);
        $totalCount = $countQueryBuilder->getQuery()->getSingleScalarResult();

        // Step 6: Execute the query with pagination
        $query = $queryBuilder->getQuery();
        $query = $query->setFirstResult($params['start'])
            ->setMaxResults($params['length']);
        $data = $query->getArrayResult();

        // Step 7: Format the results
        $results = [
            'data' => array_map(function ($item) {
                $item['createdAt'] = $item['createdAt']->format('m/d/Y');
                $item['updatedAt'] = $item['updatedAt']->format('m/d/Y');
                if (!empty($item['releaseDate'])) {
                    $item['releaseDate'] = $item['releaseDate']->format('m/d/Y');
                }
                return $item;
            }, $data),
            'recordsFiltered' => $totalCount,
            'recordsTotal' => $totalCount
        ];

        return $results;
    }

    public function exportAllLocalTranslations(array $params, Brand $brand, ?Country $country, ?Language $language): array
    {
        $params = array_merge($params, [
            'order' => [
                'name' => 'tk.labelKey',
                'dir' => 'ASC'
            ],
        ]);

        $queryBuilder = $this->createQueryBuilderForAllLocalTranslations($params, $brand, $country, $language);

        // Step 6: Execute the query with pagination
        $query = $queryBuilder->getQuery();
        $data = $query->getArrayResult();

        return $data;
    }


    public function findUniqueChannels(array $keys): array
    {
        $subquery = $this->createQueryBuilder('t')
            ->select('t.id')
            ->where('t.id IN (:keys)')
            ->setParameter('keys', $keys)
            ->getQuery()
            ->getDQL();

        $query = $this->createQueryBuilder('translationkey')
            ->select('translationkey.channel')
            ->where("translationkey.id IN ($subquery)")
            ->setParameter('keys', $keys)
            ->groupBy('translationkey.channel')
            ->having('COUNT(DISTINCT translationkey.id) = :count')
            ->setParameter('count', count($keys))
            ->getQuery();

        $result = $query->getResult();
        return array_column($result, 'channel');
    }

    public function getReferenceLanguageTranslationKeys(
        Language $referenceLanguage,
        ?string $brand = null,
        ?string $device = null,
        ?string $feature = null,
        ?string $widget = null
    ): array {
        $qb = $this->createQueryBuilder('tk')
            ->leftJoin('tk.referenceTranslations', 'rt', 'WITH', 'rt.language = :referenceLanguage')
            ->leftJoin('rt.language', 'rl')
            ->leftJoin('tk.brand', 'b')
            ->leftJoin('tk.widget', 'w')
            ->addSelect('tk.id as translatedKeyId')
            ->addSelect('rl.code as referenceLanguageCode')
            ->addSelect('tk.labelKey as key')
            ->addSelect('rt.id as referenceTranslationId')
            ->addSelect('rt.translation')
            ->addSelect('rt.updatedAt as translationUpdatedAt')
            ->addSelect('tk.createdAt as translationCreatedAt')
            ->addSelect('tk.releaseDate as releaseDate')
            ->addSelect('tk.sprintNumber')
            ->addSelect('tk.parameterValue')
            ->addSelect('tk.feature')
            ->addSelect('w.name as widgetName')
            ->addSelect('tk.channel')
            ->addSelect('b.code as brandCode')
            ->setParameter('referenceLanguage', $referenceLanguage);

        if ($brand !== null) {
            $qb->andWhere('b.name = :brand')
                ->setParameter('brand', $brand);
        }

        if ($device !== null) {
            $qb->andWhere('tk.channel LIKE :device')
                ->setParameter('device', '%' . $device . '%');
        }

        if ($feature !== null) {
            $qb->andWhere('tk.feature LIKE :feature')
                ->setParameter('feature', '%' . $feature . '%');
        }

        if ($widget !== null) {
            $qb->andWhere('w.name LIKE :widget')
                ->setParameter('widget', '%' . $widget . '%');
        }

        return $qb->getQuery()->getArrayResult();
    }

    public function getAllByUniqueKey(array $uniqueKeyBrandSourceList): array
    {
        $queryBuilder = $this->createQueryBuilder('tk');
        $queryRows = $queryBuilder
            ->select('tk')
            ->addSelect('UPPER(CONCAT(tk.labelKey, \'|\', b.code, \'|\', tk.channel, \'|\', COALESCE(w.name, \'\'))) AS uniqueKey')
            ->innerJoin('tk.brand', 'b')
            ->leftJoin('tk.widget', 'w')
            ->where($queryBuilder->expr()->in('UPPER(CONCAT(tk.labelKey, \'|\', b.code, \'|\',  tk.channel, \'|\', COALESCE(w.name, \'\')))', ':uniqueKeyBrandSourceList'))
            ->setParameter('uniqueKeyBrandSourceList', $uniqueKeyBrandSourceList)
            ->getQuery()
            ->getResult();

        return $queryRows;
    }

    public function getReferenceLanguageTranslationKeysDetails(
        Language $referenceLanguage,
        ?string $brand = null,
        ?string $channel = null
    ): array {
        $data = [];
        $qb = $this->createQueryBuilder('tk')
            ->select([
                    'COUNT(tk.id) as totalTranslationKeys',
                    'SUM(CASE WHEN rt.translation IS NULL THEN 1 ELSE 0 END) as untranslatedKeys',
                    'SUM(CASE WHEN rt.translation IS NOT NULL THEN 1 ELSE 0 END) as translatedKeys'
                ])
            ->leftJoin('tk.referenceTranslations', 'rt', 'WITH', 'rt.language = :referenceLanguage')
            ->leftJoin('rt.language', 'rl')
            ->setParameter('referenceLanguage', $referenceLanguage);

        if ($brand !== null && $brand !== "") {
            $qb->leftJoin('tk.brand', 'b')
                ->andWhere('b.name = :brand')
                ->setParameter('brand', $brand);
        }
        if ($channel !== null && $channel !== "") {
            $qb->andWhere('tk.channel = :channel')
                ->setParameter('channel', $channel);
        }

        // Execute the query and retrieve the result
        $result = $qb->getQuery()->getOneOrNullResult();

        // Check if result is null and return empty array in that case
        if ($result === null) {
            return [];
        }

        // Calculate the percentages
        $totalTranslationKeys = $data['totalTranslationKeys'] = $result['totalTranslationKeys'];
        $translatedKeys = $data['translatedKeys'] = $result['translatedKeys'];
        $untranslatedKeys = $data['untranslatedKeys'] = $result['untranslatedKeys'];


        // Calculate the percentages only if totalTranslationKeys is not zero
        if ($totalTranslationKeys !== 0) {
            $percentageTranslated = round(($translatedKeys / $totalTranslationKeys) * 100, 2);
            $percentageUntranslated = round(($untranslatedKeys / $totalTranslationKeys) * 100, 2);
        } else {
            // If totalTranslationKeys is zero, set percentages to zero
            $percentageTranslated = 0;
            $percentageUntranslated = 0;
        }

        // Add percentages to the result array
        $data['percentageTranslated'] = $percentageTranslated;
        $data['percentageUntranslated'] = $percentageUntranslated;

        return $data;
    }

    public function getLocalLanguageTranslationKeysDetails(
        Language $localLanguage,
        Site $site,
        ?string $brand = null,
        ?string $channel = null
    ): array {
        $data = [];
        $qb = $this->createQueryBuilder('tk')
            ->select([
                    'COUNT(tk.id) as totalTranslationKeys',
                    'SUM(CASE WHEN lt.translation IS NULL THEN 1 ELSE 0 END) as untranslatedKeys',
                    'SUM(CASE WHEN lt.translation IS NOT NULL THEN 1 ELSE 0 END) as translatedKeys'
                ])
            ->leftJoin('tk.localTranslations', 'lt', 'WITH', 'lt.language = :localLanguage AND lt.site = :site')
            ->leftJoin('lt.language', 'rl')
            ->setParameter('localLanguage', $localLanguage)
            ->setParameter('site', $site);

        if ($brand !== null && $brand !== "") {
            $qb->leftJoin('tk.brand', 'b')
                ->andWhere('b.name = :brand')
                ->setParameter('brand', $brand);
        }

        if ($channel !== null && $channel !== "") {
            $qb->andWhere('tk.channel = :channel')
                ->setParameter('channel', $channel);
        }

        // Execute the query and retrieve the result
        $result = $qb->getQuery()->getOneOrNullResult();

        // Check if result is null and return empty array in that case
        if ($result === null) {
            return [];
        }
        // Calculate the percentages
        $totalTranslationKeys = $data['totalTranslationKeys'] = $result['totalTranslationKeys'];
        $translatedKeys = $data['translatedKeys'] = $result['translatedKeys'];
        $untranslatedKeys = $data['untranslatedKeys'] = $result['untranslatedKeys'];

        // Calculate the percentages only if totalTranslationKeys is not zero
        if ($totalTranslationKeys !== 0) {
            $percentageTranslated = round(($translatedKeys / $totalTranslationKeys) * 100, 2);
            $percentageUntranslated = round(($untranslatedKeys / $totalTranslationKeys) * 100, 2);
        } else {
            // If totalTranslationKeys is zero, set percentages to zero
            $percentageTranslated = 0;
            $percentageUntranslated = 0;
        }

        // Add percentages to the result array
        $data['percentageTranslated'] = $percentageTranslated;
        $data['percentageUntranslated'] = $percentageUntranslated;

        return $data;
    } 
 
    public function LocalLanguageTranslationKeyCalculation( 
        array $brands, 
        array $countries, 
        array $channels,
        array $languages,
        array $keys,
    ): array {
        $data = [];
        $qb = $this->createQueryBuilder('tk');
        $qb->select('b.code as brand, c.code AS country, tk.channel, l.code AS language, COUNT(tk.id) AS tot_translation')
            ->addSelect('SUM(CASE WHEN lt.translation IS NOT NULL THEN 1 ELSE 0 END) AS translation_set')
            ->addSelect('SUM(CASE WHEN lt.translation IS NULL THEN 1 ELSE 0 END) AS translation_unset')
            ->innerJoin('tk.brand', 'b')
            ->innerJoin(Site::class, 's', 'WITH', 'b.id = s.brand')
            ->leftJoin('s.languages', 'sl')
            ->leftJoin(Language::class, 'l', 'WITH', 'l.id = sl.id')
            ->innerJoin('s.country', 'c', 'with', 'c.id=s.country')
            ->leftJoin(LocalTranslation::class, 'lt', 'WITH', 'lt.site = s AND lt.language = sl AND lt.translationKey=tk.id')
            ->where('b.code IN (:brands)')
            ->andWhere('c.code IN (:countries)')
            ->andWhere('tk.channel IN (:channels)')
            ->andWhere('l.code IN (:languages)')
            ->andWhere('tk.id IN (:keys)')
            ->groupBy('b.code, c.code, tk.channel, l.code')
            ->orderBy('b.code, c.code, tk.channel, l.code')
            ->setParameter('brands', $brands)
            ->setParameter('countries', $countries)
            ->setParameter('keys', $keys)
            ->setParameter('channels', $channels)
            ->setParameter('languages', $languages);
        $data = $qb->getQuery()->getResult();
        return $data;
    }

    public function getLocalLanguageTranslationKeys(
        array $brands,
        array $countries,
        array $channels,
        array $languages,
        ?array $localTranslations,
        array $referenceLanguages,
        array $params = []
    ): array 
    {
        $start = isset($params['start']) ? (int)$params['start'] : 0;
        $length = isset($params['length']) ? (int)$params['length'] : 10;
        $search = isset($params['search']['value']) ? (string)$params['search']['value'] : '';
         // Column mapping
         $orderColumns = [
            'tk.id',
            'b.code',            // Column 0
            'c.code',            // Column 1
            'tk.channel',        // Column 2
            'l.code',            // Column 3
            'tk.labelKey',       // Column 4
            'lt.translation'    // Column 5
        ];
        // Main query
        $qb = $this->createQueryBuilder('tk')
            ->select(
                'tk',
                'b.code as brand',
                'b.code as brand_img',
                'c.code as country',
                'c.code as country_img',
                'tk.channel as channel',
                'rt.translation as reference_translation',
                'rl.code as reference_language_code',
                's.label as site_label',
                'l.code as language',
                'lt.translation as local_translation',
                'lt.id as local_translation_id'
            )
            ->innerJoin('tk.brand', 'b')
            ->innerJoin(Site::class, 's', 'WITH', 'b.id = s.brand')
            ->innerJoin('s.preferedLanguage', 'rl', 'WITH', 'rl.isReference = 1')
            ->leftJoin(ReferenceTranslation::class, 'rt', 'WITH', 'tk = rt.translationKey AND rt.language = s.preferedLanguage')
            ->leftJoin('s.languages', 'sl')
            ->leftJoin(Language::class, 'l', 'WITH', 'l.id = sl.id')
            ->innerJoin('s.country', 'c', 'WITH', 'c.id = s.country')
            ->leftJoin(LocalTranslation::class, 'lt', 'WITH', 'lt.site = s AND lt.language = sl AND lt.translationKey = tk')
            ->where('b.code IN (:brands)')
            ->andWhere('c.code IN (:countries)')
            ->andWhere('tk.channel IN (:channels)')
            ->andWhere('l.code IN (:languages)')
            ->andWhere('rl.code IN (:referencelanguages)')
            ->setParameter('brands', $brands)
            ->setParameter('countries', $countries)
            ->setParameter('channels', $channels)
            ->setParameter('languages', $languages)
            ->setParameter('referencelanguages', $referenceLanguages);

        /* Order by column Order and searching code start*/
        $orderColumnIndex = $params['order'][0]['column'] ?? 0;
        $orderDir = $params['order'][0]['dir'] ?? 'ASC';
        $orderName = $orderColumns[$orderColumnIndex] ?? 'tk.id';
        $qb->orderBy($orderName, strtoupper($orderDir));
        
        if (!empty($search)) {
            $qb->andWhere(
                $qb->expr()->orX(
                    $qb->expr()->like('b.code', ':search'),
                    $qb->expr()->like('c.code', ':search'),
                    $qb->expr()->like('tk.channel', ':search'),
                    $qb->expr()->like('l.code', ':search'),
                    $qb->expr()->like('tk.labelKey', ':search'),
                    $qb->expr()->like('lt.translation', ':search'),
                    $qb->expr()->like('rt.translation', ':search')
                )
            )
            ->setParameter('search', '%' . $search . '%');
        }    
        /* Order and searching code end*/
        
        $qbTot = clone $qb; 
        $qbTot->select('COUNT(tk.id)');
        $recordsTotal = $qbTot->getQuery()->getSingleScalarResult();

        $qb->setFirstResult($start)
            ->setMaxResults($length);

        if ($localTranslations !== null) {
            $qb->andWhere('lt IN (:localTranslations)')
                ->setParameter('localTranslations', $localTranslations);
        }

        if (!empty($params['status']) && count($params['status']) === 1) {
            if ($params['status'][0] == '1') {
                $qb->andWhere($qb->expr()->isNotNull('lt.translation'));
            } else {
                $qb->andWhere($qb->expr()->isNull('lt.translation'));
            }
        }
        
        $paginator = $qb->getQuery()->getArrayResult();
        $results = [
            'data' => [],
            'recordsFiltered' => $recordsTotal,
            'recordsTotal' => $recordsTotal,
        ];       

        foreach ($paginator as $item) {
            $updatedAt = '';

            if (!empty($item[0]['updatedAt'])) {
                $updatedAt = $item[0]['updatedAt']->format('m/d/Y');
            }

            $results['data'][] = [
                'label_key' => $item[0]['labelKey'],
                'brand' => $item['brand'],
                'country' => $item['country'],
                'channel' => $item['channel'],
                'reference_translation' => $item['reference_translation'],
                'reference_language_code' => $item['reference_language_code'],
                'site_label' => $item['site_label'],
                'localLanguage' => $item['language'],
                'local_translation' => $item['local_translation'],
                'id' => $item[0]['id'],
                'updatedAt' => $updatedAt,
                'translation' => $item['local_translation'],
                'local_translation_id' => $item['local_translation_id'],
            ];
        }

        return $results;
    }

    public function getAllKeyIds(): array
    {
        $qb = $this->createQueryBuilder('l')
            ->select('l.id');
        $result = $qb->getQuery()->getScalarResult();        
        // Extract the codes from the result
        $codes = array_column($result, 'id');
        return $codes;
    }

    public function getLocalTranslationsOrPreferredReferenceTranslations(
        Site $site,
        Language $language,
        string $channel,
        bool $global=false,
    ): array {
        $preferedLanguage = $global||$site->isAdministrationCentral() ? $language : $site->getPreferedLanguage();
        $queryBuilder = $this->createQueryBuilder('tk');
        $rows = $queryBuilder
            ->select('tk.labelKey AS labelKey')
            ->addSelect('lt.translation AS localTranslation')
            ->addSelect('rt.translation AS referenceTranslation')
            ->addSelect('COALESCE(lt.translation, rt.translation) AS translation')
            ->leftJoin('tk.localTranslations', 'lt', 'WITH', 'lt.language = :language AND lt.site = :site')
            ->leftJoin('tk.referenceTranslations', 'rt', 'WITH', 'rt.language = :preferedLanguage')
            ->where('tk.channel = :channel')
            ->andWhere('tk.brand = :brand')
            ->setParameter('brand', $site->getBrand())
            ->setParameter('site', $site->getId())
            ->setParameter('language', $language->getId())
            ->setParameter('channel', $channel)
            ->setParameter('preferedLanguage', $preferedLanguage)
            ->addOrderBy('CASE WHEN lt.translation IS NOT NULL THEN 1 ELSE 0 END', 'ASC')
            ->getQuery()
            ->getResult();

        if (is_array($rows)) {
            return $rows;
        } else {
            return [];
        }
    }
    

    /**
     * @param array<array-key, int> $localTranslationIds
     * @param array<array-key, string> $labelKeys
     * @param array<array-key, int> $brandIds
     * @param array<array-key, int> $countryIds
     * @param array<array-key, string> $channels
     * @return array
     */
    public function getTargegetLabelTranslationsBulkCopyTarget(
        array $labelKeys,
        array $brandIds,
        array $countryIds,
        array $channels,
    ): array {
        $qb = $this->createQueryBuilder('tk');
        $qb->select([
            's.id AS target_site_id',
            's.label AS target_site',
            'b.id AS target_brand_id',
            'b.code AS target_brand',
            'c.id AS target_country_id',
            'c.name AS target_country_name',
            'l.id AS target_translation_language_id',
            'l.code AS target_translation_language_code',
            'tk.channel AS target_channel',
            'tk.id AS target_translation_key_id',
            'tk.labelKey AS target_label_key',
            'lt.id AS target_local_translation_id',
            'lt.translation AS target_local_translation'
        ])
            ->innerJoin('tk.brand', 'b')
            ->innerJoin(Site::class, 's', 'WITH', 'b = s.brand')
            ->innerJoin('s.country', 'c')
            ->innerJoin('s.languages', 'l')
            ->leftJoin('tk.localTranslations', 'lt', 'WITH', 'lt.site = s')
            //->where($qb->expr()->in('b.id', ':brandIds'))
            //->andWhere($qb->expr()->in('c.id', ':countryIds'))
            ->where($qb->expr()->in('tk.labelKey', ':labelKeys'))
            ->andWhere($qb->expr()->in('tk.channel', ':channels'))
            //->setParameter('brandIds', $brandIds)
            //->setParameter('countryIds', $countryIds)
            ->setParameter('channels', $channels)
            ->setParameter('labelKeys', $labelKeys)
            ->addOrderBy('c.name', 'ASC')
            ->addOrderBy('l.code', 'ASC')
            ->addOrderBy('b.code', 'ASC')
            ->addOrderBy('tk.channel', 'ASC')
            ->orderBy('tk.labelKey', 'ASC');

        if (!empty($brandIds)) {
            $qb->andWhere($qb->expr()->in('b.id', ':brandIds'))
                ->setParameter('brandIds', $brandIds);
        }

        if (!empty($countryIds)) {
            $qb->andWhere($qb->expr()->in('c.id', ':countryIds'))
                ->setParameter('countryIds', $countryIds);
        }

        return $qb->getQuery()->getArrayResult();
    }
}
