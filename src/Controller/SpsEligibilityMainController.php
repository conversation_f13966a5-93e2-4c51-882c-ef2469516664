<?php

namespace App\Controller;

use App\Document\SpsEligibilityLcdv;
use App\Document\SpsEligibilityModel;
use App\Form\SpsEligibilityLcdvType;
use App\Form\SpsEligibilityModelType;
use App\Service\SpsEligibilityLcdvService;
use App\Service\SpsEligibilityModelService;
use App\Security\User;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * SPS Eligibility Main Controller
 *
 * Handles the unified tabbed interface for SPS Eligibility management.
 * Provides CRUD operations for both LCDV and Model eligibility rules
 * through a single controller with tab-based navigation.
 *
 * <AUTHOR>
 * @since 2025-06-27
 */
#[Route('/admin/sps-eligibility', name: 'sps_eligibility_')]
class SpsEligibilityMainController extends AbstractController
{
    /**
     * Main SPS Eligibility page with tabs for LCDV and Model
     *
     * Displays a unified interface with two tabs:
     * - LCDV: Eligibility rules based on LCDV codes
     * - Model: Eligibility rules based on vehicle models and model years
     *
     * Access Control:
     * - Product Managers (Super Admin, Technical Admin, Functional Admin): Read/Write
     * - Brands (Reader, Operations, Webmaster, Local Technical Admin): Read-only
     *
     * @param User $user Current authenticated user
     * @param Request $request HTTP request with optional tab and search parameters
     * @param SpsEligibilityLcdvService $lcdvService Service for LCDV operations
     * @param SpsEligibilityModelService $modelService Service for Model operations
     * @return Response Rendered template with tabbed interface
     * @throws AccessDeniedException If user doesn't have required permissions
     */
    #[Route('', name: 'index', methods: ['GET'])]
    public function index(
        #[CurrentUser] User $user,
        Request $request,
        SpsEligibilityLcdvService $lcdvService,
        SpsEligibilityModelService $modelService
    ): Response {
        $profile = $user->getProfile();

        // Check if user has access to SPS Eligibility pages (Product Managers + Brands)
        // Product Managers: Super Admin, Technical Admin, Functional Admin
        // Brands: Reader, Operations, Webmaster, Local Technical Admin
        if (!$profile->isSuperAdmin() && !$profile->isCentralAdministrator() && !$profile->isCountryUser()) {
            throw $this->createAccessDeniedException('Access denied to SPS Eligibility page');
        }

        // Get the active tab from query parameter (default to 'lcdv')
        $activeTab = $request->query->get('tab', 'lcdv');
        
        // Validate tab parameter
        if (!in_array($activeTab, ['lcdv', 'model'])) {
            $activeTab = 'lcdv';
        }

        // Check if user can write (Product Managers: Super Admin, Technical Admin, Functional Admin)
        $canWrite = $profile->isSuperAdmin() || $profile->isCentralAdministrator();

        // Get data based on active tab
        $lcdvData = [];
        $modelData = [];

        if ($activeTab === 'lcdv') {
            // Get LCDV search parameters
            $lcdvCode = $request->query->get('lcdv_code');
            $type = $request->query->get('type');
            $disclaimer = $request->query->get('disclaimer');

            // Search or get all LCDV eligibility rules
            if ($lcdvCode || $type || $disclaimer !== null) {
                $lcdvRules = $lcdvService->search($lcdvCode, $type, $disclaimer);
            } else {
                $lcdvRules = $lcdvService->findAll();
            }

            $lcdvData = [
                'eligibility_rules' => $lcdvRules,
                'types' => $lcdvService->getDistinctTypes(),
                'search' => [
                    'lcdv_code' => $lcdvCode,
                    'type' => $type,
                    'disclaimer' => $disclaimer
                ]
            ];
        } else {
            // Get Model search parameters
            $model = $request->query->get('model');
            $type = $request->query->get('type');
            $modelYearFrom = $request->query->get('model_year_from');
            $disclaimer = $request->query->get('disclaimer');

            // Convert model year to integer if provided
            $modelYearFromInt = $modelYearFrom ? (int) $modelYearFrom : null;

            // Search or get all Model eligibility rules
            if ($model || $type || $modelYearFromInt || $disclaimer !== null) {
                $modelRules = $modelService->search($model, $type, $modelYearFromInt, $disclaimer);
            } else {
                $modelRules = $modelService->findAll();
            }

            $modelData = [
                'eligibility_rules' => $modelRules,
                'types' => $modelService->getDistinctTypes(),
                'models' => $modelService->getDistinctModels(),
                'search' => [
                    'model' => $model,
                    'type' => $type,
                    'model_year_from' => $modelYearFrom,
                    'disclaimer' => $disclaimer
                ]
            ];
        }

        return $this->render('sps_eligibility/index.html.twig', [
            'active_tab' => $activeTab,
            'can_write' => $canWrite,
            'profile' => $profile,
            'lcdv_data' => $lcdvData,
            'model_data' => $modelData
        ]);
    }

    /**
     * Create new SPS Eligibility LCDV rule
     */
    #[Route('/lcdv/new', name: 'lcdv_new', methods: ['GET', 'POST'])]
    public function newLcdv(
        #[CurrentUser] User $user,
        Request $request,
        SpsEligibilityLcdvService $service,
        TranslatorInterface $translator
    ): Response {
        $profile = $user->getProfile();

        // Only product managers can create SPS Eligibility rules
        if (!$profile->isSuperAdmin() && !$profile->isCentralAdministrator()) {
            throw $this->createAccessDeniedException('Only product managers can create SPS Eligibility rules');
        }

        $spsEligibility = new SpsEligibilityLcdv();
        $form = $this->createForm(SpsEligibilityLcdvType::class, $spsEligibility);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $response = $service->save($spsEligibility);

                if ($response->getCode() == 201 || $response->getCode() == 200) {
                    $this->addFlash('success', $translator->trans('SPS Eligibility LCDV rule created successfully'));
                    return $this->redirectToRoute('sps_eligibility_index', ['tab' => 'lcdv']);
                } else {
                    $errorMessage = $response->getData() ?
                        $translator->trans('Error creating SPS Eligibility LCDV rule: {{ error }}', ['{{ error }}' => $response->getData()]) :
                        $translator->trans('Error creating SPS Eligibility LCDV rule (HTTP {{ code }})', ['{{ code }}' => $response->getCode()]);
                    $this->addFlash('error', $errorMessage);
                }
            } catch (\Exception $e) {
                $this->addFlash('error', $translator->trans('Error creating SPS Eligibility LCDV rule: {{ error }}', ['{{ error }}' => $e->getMessage()]));
            }
        }

        return $this->render('sps_eligibility_lcdv/new.html.twig', [
            'form' => $form->createView(),
            'sps_eligibility' => $spsEligibility,
            'profile' => $profile
        ]);
    }

    /**
     * Create new SPS Eligibility Model rule
     */
    #[Route('/model/new', name: 'model_new', methods: ['GET', 'POST'])]
    public function newModel(
        #[CurrentUser] User $user,
        Request $request,
        SpsEligibilityModelService $service,
        TranslatorInterface $translator
    ): Response {
        $profile = $user->getProfile();

        // Only product managers can create SPS Eligibility rules
        if (!$profile->isSuperAdmin() && !$profile->isCentralAdministrator()) {
            throw $this->createAccessDeniedException('Only product managers can create SPS Eligibility rules');
        }

        $spsEligibility = new SpsEligibilityModel();
        $form = $this->createForm(SpsEligibilityModelType::class, $spsEligibility);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $response = $service->save($spsEligibility);

                if ($response->getCode() == 201 || $response->getCode() == 200) {
                    $this->addFlash('success', $translator->trans('SPS Eligibility Model rule created successfully'));
                    return $this->redirectToRoute('sps_eligibility_index', ['tab' => 'model']);
                } else {
                    $errorMessage = $response->getData() ?
                        $translator->trans('Error creating SPS Eligibility Model rule: {{ error }}', ['{{ error }}' => $response->getData()]) :
                        $translator->trans('Error creating SPS Eligibility Model rule (HTTP {{ code }})', ['{{ code }}' => $response->getCode()]);
                    $this->addFlash('error', $errorMessage);
                }
            } catch (\Exception $e) {
                $this->addFlash('error', $translator->trans('Error creating SPS Eligibility Model rule: {{ error }}', ['{{ error }}' => $e->getMessage()]));
            }
        }

        return $this->render('sps_eligibility_model/new.html.twig', [
            'form' => $form->createView(),
            'sps_eligibility' => $spsEligibility,
            'profile' => $profile
        ]);
    }

    /**
     * Show SPS Eligibility LCDV rule details
     */
    #[Route('/lcdv/{id}', name: 'lcdv_show', methods: ['GET'])]
    public function showLcdv(
        #[CurrentUser] User $user,
        string $id,
        SpsEligibilityLcdvService $service
    ): Response {
        $profile = $user->getProfile();

        // Check if user has access to SPS Eligibility pages
        if (!$profile->isSuperAdmin() && !$profile->isCentralAdministrator() && !$profile->isCountryUser()) {
            throw $this->createAccessDeniedException('Access denied to SPS Eligibility page');
        }

        $spsEligibility = $service->findById($id);
        if (!$spsEligibility) {
            throw $this->createNotFoundException('SPS Eligibility rule not found');
        }

        return $this->render('sps_eligibility_lcdv/show.html.twig', [
            'sps_eligibility' => $spsEligibility,
            'profile' => $profile
        ]);
    }

    /**
     * Show SPS Eligibility Model rule details
     */
    #[Route('/model/{id}', name: 'model_show', methods: ['GET'])]
    public function showModel(
        #[CurrentUser] User $user,
        string $id,
        SpsEligibilityModelService $service
    ): Response {
        $profile = $user->getProfile();

        // Check if user has access to SPS Eligibility pages
        if (!$profile->isSuperAdmin() && !$profile->isCentralAdministrator() && !$profile->isCountryUser()) {
            throw $this->createAccessDeniedException('Access denied to SPS Eligibility page');
        }

        $spsEligibility = $service->findById($id);
        if (!$spsEligibility) {
            throw $this->createNotFoundException('SPS Eligibility rule not found');
        }

        return $this->render('sps_eligibility_model/show.html.twig', [
            'sps_eligibility' => $spsEligibility,
            'profile' => $profile
        ]);
    }

    /**
     * Edit SPS Eligibility LCDV rule
     */
    #[Route('/lcdv/{id}/edit', name: 'lcdv_edit', methods: ['GET', 'POST'])]
    public function editLcdv(
        #[CurrentUser] User $user,
        Request $request,
        string $id,
        SpsEligibilityLcdvService $service,
        TranslatorInterface $translator
    ): Response {
        $profile = $user->getProfile();

        // Only product managers can edit SPS Eligibility rules
        if (!$profile->isSuperAdmin() && !$profile->isCentralAdministrator()) {
            throw $this->createAccessDeniedException('Only product managers can edit SPS Eligibility rules');
        }

        $spsEligibility = $service->findById($id);
        if (!$spsEligibility) {
            throw $this->createNotFoundException('SPS Eligibility rule not found');
        }

        $form = $this->createForm(SpsEligibilityLcdvType::class, $spsEligibility);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $response = $service->save($spsEligibility);

                if ($response->getCode() == 201 || $response->getCode() == 200) {
                    $this->addFlash('success', $translator->trans('SPS Eligibility LCDV rule updated successfully'));
                    return $this->redirectToRoute('sps_eligibility_index', ['tab' => 'lcdv']);
                } else {
                    $errorMessage = $response->getData() ?
                        $translator->trans('Error updating SPS Eligibility LCDV rule: {{ error }}', ['{{ error }}' => $response->getData()]) :
                        $translator->trans('Error updating SPS Eligibility LCDV rule (HTTP {{ code }})', ['{{ code }}' => $response->getCode()]);
                    $this->addFlash('error', $errorMessage);
                }
            } catch (\Exception $e) {
                $this->addFlash('error', $translator->trans('Error updating SPS Eligibility LCDV rule: {{ error }}', ['{{ error }}' => $e->getMessage()]));
            }
        }

        return $this->render('sps_eligibility_lcdv/edit.html.twig', [
            'form' => $form->createView(),
            'sps_eligibility' => $spsEligibility,
            'profile' => $profile
        ]);
    }

    /**
     * Edit SPS Eligibility Model rule
     */
    #[Route('/model/{id}/edit', name: 'model_edit', methods: ['GET', 'POST'])]
    public function editModel(
        #[CurrentUser] User $user,
        Request $request,
        string $id,
        SpsEligibilityModelService $service,
        TranslatorInterface $translator
    ): Response {
        $profile = $user->getProfile();

        // Only product managers can edit SPS Eligibility rules
        if (!$profile->isSuperAdmin() && !$profile->isCentralAdministrator()) {
            throw $this->createAccessDeniedException('Only product managers can edit SPS Eligibility rules');
        }

        $spsEligibility = $service->findById($id);
        if (!$spsEligibility) {
            throw $this->createNotFoundException('SPS Eligibility rule not found');
        }

        $form = $this->createForm(SpsEligibilityModelType::class, $spsEligibility);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $response = $service->save($spsEligibility);

                if ($response->getCode() == 201 || $response->getCode() == 200) {
                    $this->addFlash('success', $translator->trans('SPS Eligibility Model rule updated successfully'));
                    return $this->redirectToRoute('sps_eligibility_index', ['tab' => 'model']);
                } else {
                    $errorMessage = $response->getData() ?
                        $translator->trans('Error updating SPS Eligibility Model rule: {{ error }}', ['{{ error }}' => $response->getData()]) :
                        $translator->trans('Error updating SPS Eligibility Model rule (HTTP {{ code }})', ['{{ code }}' => $response->getCode()]);
                    $this->addFlash('error', $errorMessage);
                }
            } catch (\Exception $e) {
                $this->addFlash('error', $translator->trans('Error updating SPS Eligibility Model rule: {{ error }}', ['{{ error }}' => $e->getMessage()]));
            }
        }

        return $this->render('sps_eligibility_model/edit.html.twig', [
            'form' => $form->createView(),
            'sps_eligibility' => $spsEligibility,
            'profile' => $profile
        ]);
    }

    /**
     * Delete SPS Eligibility LCDV rule
     */
    #[Route('/lcdv/{id}/delete', name: 'lcdv_delete', methods: ['GET', 'POST'])]
    public function deleteLcdv(
        #[CurrentUser] User $user,
        string $id,
        SpsEligibilityLcdvService $service,
        TranslatorInterface $translator
    ): Response {
        $profile = $user->getProfile();

        // Only product managers can delete SPS Eligibility rules
        if (!$profile->isSuperAdmin() && !$profile->isCentralAdministrator()) {
            throw $this->createAccessDeniedException('Only product managers can delete SPS Eligibility rules');
        }

        $spsEligibility = $service->findById($id);
        if (!$spsEligibility) {
            throw $this->createNotFoundException('SPS Eligibility rule not found');
        }

        $response = $service->delete($id);

        if ($response->getCode() == 200) {
            $this->addFlash('success', $translator->trans('SPS Eligibility LCDV rule deleted successfully'));
        } else {
            $this->addFlash('error', $translator->trans('Error deleting SPS Eligibility LCDV rule'));
        }

        return $this->redirectToRoute('sps_eligibility_index', ['tab' => 'lcdv']);
    }

    /**
     * Delete SPS Eligibility Model rule
     */
    #[Route('/model/{id}/delete', name: 'model_delete', methods: ['GET', 'POST'])]
    public function deleteModel(
        #[CurrentUser] User $user,
        string $id,
        SpsEligibilityModelService $service,
        TranslatorInterface $translator
    ): Response {
        $profile = $user->getProfile();

        // Only product managers can delete SPS Eligibility rules
        if (!$profile->isSuperAdmin() && !$profile->isCentralAdministrator()) {
            throw $this->createAccessDeniedException('Only product managers can delete SPS Eligibility rules');
        }

        $spsEligibility = $service->findById($id);
        if (!$spsEligibility) {
            throw $this->createNotFoundException('SPS Eligibility rule not found');
        }

        $response = $service->delete($id);

        if ($response->getCode() == 200) {
            $this->addFlash('success', $translator->trans('SPS Eligibility Model rule deleted successfully'));
        } else {
            $this->addFlash('error', $translator->trans('Error deleting SPS Eligibility Model rule'));
        }

        return $this->redirectToRoute('sps_eligibility_index', ['tab' => 'model']);
    }
}
