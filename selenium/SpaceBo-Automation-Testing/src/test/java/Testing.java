
import org.openqa.selenium.WebElement;
import org.testng.Assert;
import org.testng.annotations.Test;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class Testing {
    private static final Logger logger = LoggerFactory.getLogger(Testing.class);
    @Test
    public void AddUpdateDeltecopy() throws InterruptedException {

        logger.info("Starting sample test");
        Methods user = new Methods();
        user.login();
        String[] expectedTexts = {
                "ABARTH", "ALFA ROMEO", "CHRYSLER", "CITROËN", "DODGE",
                "DS", "FIAT", "FIAT PROFESSIONAL", "GLOBAL", "JEEP",
                "LANCIA", "MASERATI", "OPEL", "PEUGEOT","RAM","SPOTIC<PERSON>","VAUXHALL"
        };

        Locators.Media_Library().click();
        Locators.APP().click();
        List<WebElement> App_items = Locators.Leaf_APP();
        Assert.assertEquals(App_items.size(), expectedTexts.length, "Mismatch in number of list items!");
        for (int i = 0; i < App_items.size(); i++) {
            WebElement element = App_items.get(i);
            // Try using getText(), if it doesn't work, try getAttribute("textContent")
            String actualText = element.getText().trim();  // Use trim() to avoid extra whitespace
            // If getText() doesn't return the text, try to fetch the text content
            if (actualText.isEmpty()) {
                actualText = element.getAttribute("textContent").trim();
            }
            String expectedText = expectedTexts[i];
            Assert.assertEquals(actualText, expectedText, "Text does not match for item " + (i + 1));
        }
        Locators.APP().click();
        Locators.WEB().click();
        List<WebElement> WEB_items = Locators.Leaf_Web();
        Assert.assertEquals(WEB_items.size(), expectedTexts.length, "Mismatch in number of list items!");
        for (int i = 0; i < WEB_items.size(); i++) {
            WebElement element = WEB_items.get(i);
            // Try using getText(), if it doesn't work, try getAttribute("textContent")
            String actualText = element.getText().trim();  // Use trim() to avoid extra whitespace
            // If getText() doesn't return the text, try to fetch the text content
            if (actualText.isEmpty()) {
                actualText = element.getAttribute("textContent").trim();
            }
            String expectedText = expectedTexts[i];
            Assert.assertEquals(actualText, expectedText, "Text does not match for item " + (i + 1));
        }
        Locators.WEB().click();
        Locators.APP().click();
        Locators.APP_CITROEN().click();
        Locators.ADD_Media().click();
        Locators.ADD_Browse().sendKeys("C:\\Users\\<USER>\\SpaceBo\\Files\\C3.jpg");
        Locators.Alt_Text().sendKeys("Citroen_C3");
        Locators.Cpyright_Text().sendKeys("yes");
        Locators.Comment_Text().sendKeys("Testing purpose");
        Locators.Media_Save().click();
        Thread.sleep(9000);
        Locators.Media_Img_lastdiv().click();
        Locators.EDIT_Media().click();
        Thread.sleep(5000);
        Locators.Close_File().click();
        Locators.ADD_Browse().sendKeys("C:\\Users\\<USER>\\SpaceBo\\Files\\c5.jpeg");
        Locators.Edit_Alt_Text().clear();
        Locators.Edit_Alt_Text().sendKeys("Citroen_C5_Edited");
        Locators.Edit_Cpyright_Text().clear();
        Locators.Edit_Cpyright_Text().sendKeys("no");
        Locators.Edit_Comment_Text().clear();
        Locators.Edit_Comment_Text().sendKeys("Automation Testing purpose");
        Locators.EDIT_Save().click();
        Thread.sleep(9000);
        Locators.Media_Img_lastdiv().click();
        Locators.Media_Copy_Url().click();
        Locators.Media_Delete().click();
        Thread.sleep(2000);
        Locators.Confirm_Media_Delete().click();
        user.exit();
        logger.info("Browser closed");
    }
}
