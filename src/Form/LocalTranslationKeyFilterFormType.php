<?php

namespace App\Form;

use App\Entity\Country;
use App\Repository\ChannelRepository;
use App\Repository\LanguageRepository;
use App\Repository\TranslationKeyRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Contracts\Translation\TranslatorInterface;

class LocalTranslationKeyFilterFormType extends AbstractType
{
    public function __construct(
        private TranslatorInterface $translator,
        private TranslationKeyRepository $translationKeyRepository,
        private LanguageRepository $languageRepository,
        private ChannelRepository $channelRepository
        )
    {
        $this->translator = $translator;
    }

    /**
     * {@inheritdoc}
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $brand = $options['brand'];
        $country = $options['country'];
        $language = $options['language'];
        $local_languages = $options['localLanguages'];
        $unilang = ["choose language" => ""];
        $channelMap = $this->channelRepository->getChannelMap();
        
        if ($local_languages) {
            $languages = $local_languages;
        }
        else {
            $codes = $this->translationKeyRepository->getUniqueLanguageCode($brand, $country, $language);
            $flattenedCodes = array_column($codes, 'code');
            $languages = $this->languageRepository->findBy(['code' => $flattenedCodes]);
        }
        foreach ($languages as $language) {
            $unilang[$language->getLabel()] = $language->getCode();
        }
        
        $builder
            ->add('channel', ChoiceType::class, [
                'label' => 'channel',
                'choices' => $channelMap,
                'placeholder' => 'choose_channel',
                'required' => false,
            ])
            ->add('feature', TextType::class, ['label' => 'feature'])
            ->add('widget', TextType::class, ['label' => 'widget'])
            ->add('country', EntityType::class, [
                'class' => Country::class,
                'choice_label' => 'name',
                'choice_value' => 'name',
                'label' => 'country',
                'placeholder' => 'choose_country',
                'required' => false
            ])
            ->add('language', ChoiceType::class, ['label' => 'language', 'choices' => $unilang]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'brand' => null,
            'country' => null,
            'language' => null,
            'localLanguages' => null,
        ]);
    }
}
