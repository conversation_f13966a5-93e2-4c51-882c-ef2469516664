<?php

namespace App\Document;

class VehicleModel
{
    private $label;

    private $brand;

    private $lcdv = [];


    private $isO2x = false;

    private $sdp;

    private $defaultImage;

    private $creationAt;

    private $updateAt;

    public function __construct()
    {
        $this->creationAt = new \DateTime();
        $this->updateAt = new \DateTime();
    }

    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function setLabel(string $label): self
    {
        $this->label = $label;

        return $this;
    }

    public function getBrand(): string
    {
        return $this->brand;
    }

    public function setBrand(string $brand): self
    {
        $this->brand = $brand;

        return $this;
    }

    public function getDefaultImage(): string
    {
        return $this->defaultImage;
    }

    public function setDefaultImage(string $defaultImage): self
    {
        $this->defaultImage = $defaultImage;

        return $this;
    }

    public function getLcdv(): array
    {
        return $this->lcdv;
    }

    public function setLcdv(array $lcdv): self
    {
        $this->lcdv = $lcdv;

        return $this;
    }

    public function getIsO2x(): ?bool
    {
        return $this->isO2x;
    }

    public function setIsO2x(bool $isO2x): self
    {
        $this->isO2x = $isO2x;

        return $this;
    }

    public function getSdp(): ?string
    {
        return $this->sdp;
    }

    public function setSdp(string $sdp): self
    {
        $this->sdp = $sdp;

        return $this;
    }

    public function getCreationAt(): ?\DateTime
    {
        return $this->creationAt;
    }

    public function setCreationAt(\DateTime $creationAt): self
    {
        $this->creationAt = $creationAt;

        return $this;
    }

    public function getUpdateAt(): ?\DateTime
    {
        return $this->updateAt;
    }

    public function setUpdateAt(\DateTime $updateAt): self
    {
        $this->updateAt = $updateAt;

        return $this;
    }
}