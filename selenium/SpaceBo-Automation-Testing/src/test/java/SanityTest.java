import org.openqa.selenium.WebElement;
import org.testng.Assert;
import org.testng.annotations.Test;
import org.openqa.selenium.WebDriver;

import java.util.concurrent.TimeUnit;

public class SanityTest {
    @Test(groups = {"Sanity Test"})
    public void Sanity() throws InterruptedException {
        Methods user = new Methods();
       user.login();
        WebDriver driver =  TestUtilities.getDriver();
        Locators.Dashboard().click();
        WebElement dashboard_text=Locators.Title_Text();
        boolean isDashboardTextFound= user.isTextPresent(dashboard_text,"Dashboard");
        Assert.assertTrue( isDashboardTextFound,"Expected text 'Dashboard' not found!");
        Locators.Reference_Translation_Dashboard().click();

        WebElement Refernce_translation_text=Locators.Translation_Dashboard_Text();
        boolean isRefernceTextFound= user.isTextPresent(Refernce_translation_text,"Reference Translation Dashboard");
        Assert.assertTrue( isRefernceTextFound,"Expected text 'Reference Translation Dashboard' not found!");
        driver.navigate().back();

        Locators.Local_Translation_Dashboard().click();
        WebElement local_translation_text=Locators.Translation_Dashboard_Text();
        boolean islocalTextFound= user.isTextPresent(local_translation_text,"Local Translation Dashboard");
        Assert.assertTrue( islocalTextFound,"Expected text 'Local Translation Dashboard' not found!");
        driver.navigate().back();

        Locators.Administration().click();
        Locators.Site_Management().click();
        WebElement Site_Management_text=Locators.Title_Text();
        boolean isSiteTextFound= user.isTextPresent(Site_Management_text,"Site Management");
        Assert.assertTrue( isSiteTextFound,"Expected text 'Site Management' not found!");

        Locators.ReferenceLabelManagement().click();
        WebElement Ref_Label_Management_text=Locators.Title_Text();
        boolean isRefTextFound= user.isTextPresent(Ref_Label_Management_text,"Reference Label Management");
        Assert.assertTrue( isRefTextFound,"Expected text 'Reference Label Management' not found!");

        Locators.Local_translations().click();
        WebElement Local_translations_text=Locators.Title_Text();
        boolean is_Localtranslation_TextFound= user.isTextPresent(Local_translations_text,"Local Translations");
        Assert.assertTrue( is_Localtranslation_TextFound,"Expected text 'Local Translations' not found!");

        Locators.Feature_Setting().click();
        WebElement Feature_Setting_text=Locators.Title_Text();
        boolean isFeature_Setting_TextFound= user.isTextPresent(Feature_Setting_text,"Feature Setting");
        Assert.assertTrue( isFeature_Setting_TextFound,"Expected text 'Feature Setting' not found!");

        Locators.Access_Management().click();
        Locators.Global_Profiles().click();
        WebElement Global_Profiles_text=Locators.Title_Text();
        boolean isGlobal_Profiles_TextFound= user.isTextPresent(Global_Profiles_text,"Global Profiles");
        Assert.assertTrue( isGlobal_Profiles_TextFound,"Expected text 'Global Profiles' not found!");

        Locators.Role().click();
        WebElement Role_text=Locators.Title_Text();
        boolean isRole_TextFound= user.isTextPresent(Role_text,"Role");
        Assert.assertTrue( isRole_TextFound,"Expected text 'Role' not found!");

        Locators.Menu().click();
        WebElement Menu_text=Locators.Title_Text();
        boolean isMenu_TextFound= user.isTextPresent(Menu_text,"Menu");
        Assert.assertTrue( isMenu_TextFound,"Expected text 'Menu' not found!");

        Locators.Local_Profiles().click();
        WebElement Local_Profiles_text=Locators.Title_Text();
        boolean isLocal_ProfilesTextFound= user.isTextPresent(Local_Profiles_text,"Local Profiles");
        Assert.assertTrue( isLocal_ProfilesTextFound,"Expected text 'Local Profiles' not found!");

        Locators.Icon_Management().click();
        WebElement Icon_Management_text=Locators.Title_Text();
        boolean isIcon_Management_TextFound= user.isTextPresent(Icon_Management_text,"Icon Management");
        Assert.assertTrue( isIcon_Management_TextFound,"Expected text 'Icon Management' not found!");

        Locators.Channel_Management().click();
        WebElement Channel_Management_text=Locators.Title_Text();
        boolean isChannel_Management_TextFound= user.isTextPresent(Channel_Management_text,"Channel Management");
        Assert.assertTrue( isChannel_Management_TextFound,"Expected text 'Channel Management' not found!");

        TimeUnit.SECONDS.sleep(1);
        Locators.Settings_Release().click();
        WebElement Settings_Release_text=Locators.Title_Text();
        boolean isSettings_Release_TextFound= user.isTextPresent(Settings_Release_text,"Settings Release");
        Assert.assertTrue( isSettings_Release_TextFound,"Expected text 'Settings Release' not found!");

        TimeUnit.SECONDS.sleep(1);
        Locators.Widget_Administration().click();
        WebElement Widget_Administration_text=Locators.Title_Text();
        boolean isWidget_Administration_TextFound= user.isTextPresent(Widget_Administration_text,"Widget Administration");
        Assert.assertTrue( isWidget_Administration_TextFound,"Expected text 'Widget Administration' not found!");
        TimeUnit.SECONDS.sleep(1);
        Locators.Widget_Management().click();
        WebElement Widget_Management_text=Locators.Title_Text();
        boolean isWidget_Management_TextFound= user.isTextPresent(Widget_Management_text,"Widget Management");
        Assert.assertTrue( isWidget_Management_TextFound,"Expected text 'Widget Management' not found!");
        TimeUnit.SECONDS.sleep(1);
        Locators.Reference_Label_Release().click();
        WebElement Reference_Label_Release_text=Locators.Title_Text();
        boolean isReference_Label_Release_TextFound= user.isTextPresent(Reference_Label_Release_text,"Reference Label Release");
        Assert.assertTrue( isReference_Label_Release_TextFound,"Expected text 'Reference Label Release' not found!");
        TimeUnit.SECONDS.sleep(1);
        Locators.History_Data_Log().click();
        WebElement History_Data_Log_text=Locators.Title_Text();
        boolean isHistory_Data_Log_TextFound= user.isTextPresent(History_Data_Log_text,"History Data Log");
        Assert.assertTrue( isHistory_Data_Log_TextFound,"Expected text 'History Data Log' not found!");
        TimeUnit.SECONDS.sleep(1);
        Locators.Bulk_Operation().click();
        WebElement Bulk_Operation_text=Locators.Title_Text();
        boolean isBulk_Operation_TextFound= user.isTextPresent(Bulk_Operation_text,"Bulk Operation");
        Assert.assertTrue( isBulk_Operation_TextFound,"Expected text 'Bulk Operation' not found!");
        user.exit();
    }
}

