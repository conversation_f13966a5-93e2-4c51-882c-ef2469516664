document.addEventListener("DOMContentLoaded", function () {

    $('.sub_menu').each(function () {
        if ($(this).find('.active-link').length > 0) {
            $(this).prev('.link-menu').find('.chevron-icon').toggleClass('fa-chevron-up fa-chevron-down');
        }
    });

    $('.parent').click(function () {
        $(this).find('.chevron-icon').toggleClass('fa-chevron-up fa-chevron-down');
        $(this).next('.sub_menu').slideToggle();
    });
});

var url = window.location.href, urlRegExp = new RegExp(url.replace(/\/$/, '') + "$");
$('.nav-link').each(function () {
    if (urlRegExp.test(this.href.replace(/\/$/, ''))) {
        $(this).parents('.nav-item').addClass('menu-open');
        if ($(this).parents('.nav-treeview').length) {
            $(this).addClass('active');
        }
    }

});

function filter(brand, country) {
    $('optgroup').hide();
    if (brand && !country) {
        $('.brand-' + brand).parent().show();
        $('.brand-' + brand).parent().children('option').hide();
        $('.brand-' + brand).show();
    } else if (!brand && country) {
        $('.country-' + country).parent().show();
        $('.country-').parent().show();
        $('.country-').parent().children('option').hide();
        $('.country-' + country).show();
        $('.country-').show();
    } else if (brand && country) {
        $('.brand-' + brand + '.country-' + country).parent().show();
        //show elements without country
        $('.brand-' + brand + '.country-').parent().show();
        $('.brand-' + brand + '.country-').parent().children('option').hide();
        $('.brand-' + brand + '.country-').show();
    } else {
        $('optgroup').show();
    }
    $('.brand-XX, .brand-CT, .brand-').parent().show();
}

$('#profile_login_form_brand, #profile_login_form_country').change(function (e) {
    filter(jQuery('#profile_login_form_brand').val(), jQuery('#profile_login_form_country').val())
});

$('#profile_login_form_language').change(function (e) {
    var selectedLang = $(this).val();
    var _location = $(this).data('location');
    $.ajax({
        url: '/general/set-language/' + selectedLang,
        type: 'get',
        dataType: 'json',
        success: function (response) {
            if (_location == 'header') {
                location.reload(true);
            }
        }
    });
});

$('#profile_login_form_profile').change(function (e) {
    if ($('form[data-location="header"]').length) {
        $('form[data-location="header"]').submit();
    }
});
$('#profile_login_form_country').change(function (e) {
    const role = $('#profile_login_form_profile').find(':selected').text().trim().toLowerCase();
    const isAdmin = (role === 'super administrator') || role.startsWith('technical administrator') || role.startsWith('functional administrator');
    if ($('form[data-location="header"]').length && !isAdmin) {
        $('form[data-location="header"]').submit();
    }
});


function showLoader(selectorName) {
    loaderHtml = '<div class="space-loader-container"><div class="space-loader-ellipsis"><div></div><div></div><div></div><div></div></div></div>';
    $(selectorName).prepend(loaderHtml);
}

function hideLoader() {
    loaderHtml = '<div class="space-loader-container"><div class="space-loader-ellipsis"><div></div><div></div><div></div><div></div></div></div>';
    $(".space-loader-container").remove();
}

var select2Obj = $('.select2');
select2Obj.select2();

select2Obj.on('select2:unselecting', function(e) {
    $(this).on('select2:opening', function(e) {
        e.preventDefault();
    });

});
select2Obj.on('select2:unselect', function(e) {
     var sel = $(this);
     setTimeout(function() {
       sel.off('select2:opening');
     }, 1);
});