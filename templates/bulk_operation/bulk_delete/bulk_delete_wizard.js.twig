<script type="text/javascript">
  var bulkDeleteEvent = new CustomEvent('bulkDelete', {
    detail: { selectedIds: '' }
  });

  $(document).ready(function() {
    function loadModalContent(url, data, modalId) {
        $.ajax({
            url: url,
            type: 'GET',
            data: data,
            success: function(response) {
                $('#bulkOperationModalContainer').html(response);
                $(modalId).modal('show');
                initializeStep1DataGrid();
            },
            error: function() {
                alert('Failed to load modal content.');
            }
        });
    }

    function initializeBulkDeleteStep1(ids) {
        loadBulkDeleteStep1Modal(ids.join(','));
    }

    function loadBulkDeleteStep1Modal(selectedLocalTranslationIds) {
        const url = '{{ path('bulk_operation_delete_preview') }}'; 
        const data = { 
            'selected_local_translation_ids': selectedLocalTranslationIds,
        };
        loadModalContent(url, data, '#bulkDeleteStep1Modal');
    }

    $(document).on('click', '#bulkDeleteConfirmation', function() {
        const selectedLocalTranslationIds = $('#selected_local_translation_ids').val();

        const url = '{{ path('bulk_operation_delete') }}';
        const data = { 
            selected_local_translation_ids: selectedLocalTranslationIds,
        };

        $.ajax({
            url: url,
            type: 'GET',
            data: data,
            beforeSend: function() {
                $('#loadingOverlay').show();
            },
            success: function(response) {
                $('#bulkDeleteStep1Modal').modal('hide').on('hidden.bs.modal', function() {
                    location.reload();
                });
            },
            complete: function() {
                $('#loadingOverlay').hide();
            },
            error: function() {
                alert('Failed to complete the operation.');
            }
        });
    });

    document.addEventListener('bulkDelete', function(event) {
        initializeBulkDeleteStep1(event.detail.selectedIds);
    });

    function initializeStep1DataGrid() {
        var table = $('#dtSelectedLocalTranslations').DataTable({
            paging: true,
            searching: true,
            info: false,
            order: [
                [1, 'asc'],
                [2, 'asc'],
                [5, 'asc'],        
                [4, 'asc'],
                [3, 'asc'],
            ]
        });
    }
  });
</script>