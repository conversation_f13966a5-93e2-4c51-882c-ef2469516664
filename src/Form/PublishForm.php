<?php

namespace App\Form;

use App\Entity\Brand;
use App\Entity\Country;
use App\Entity\Language;
use App\Repository\ChannelRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;

class PublishForm extends AbstractType
{
    public function __construct(private EntityManagerInterface $entityManager, private ChannelRepository $channelRepository) {}

    const CUSTOM_SELECT_CSS = "no-background filterInSelect custom-select";

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $site = $options['site'];
        $profile = $options['profile'];
        $channels = $this->channelRepository->findAll();
        $channelNames = array_map(function ($channel) {
            return $channel->getName();
        }, $channels);

        $channelHashmap = array_combine($channelNames, $channelNames);

        $brands = $this->entityManager->getRepository(Brand::class)->findAll();
        $brandChoices = $this->getChoices($brands, fn($brand) => $brand->getName());

        $countries = $this->entityManager->getRepository(Country::class)->findAll();
        $countryChoices = $this->getChoices($countries, fn($country) => $country->getName());

        $brand = $profile->getBrand() ?? $site->getBrand();
        $countriesByBrand = $this->entityManager->getRepository(Country::class)->getCountriesByBrand($brand);
        $countryByBrandChoices = $this->getChoices($countriesByBrand, fn($country) => $country['name']);

        $languages = $this->entityManager->getRepository(Language::class)->findAll();
        $languageChoices = $this->getChoices($languages, fn($language) => $language->getLabel());

        $languagesBySite = $site->getLanguages()->toArray();
        $languageBySiteChoices = $this->getChoices($languagesBySite, fn($language) => $language->getLabel());

        $countryNames = array_column($countriesByBrand, 'name');
        $languagesByCountries = $this->entityManager->getRepository(Language::class)->getLanguagesByCountries($countryNames);
        $languageByCountriesChoices = $this->getChoices($languagesByCountries, fn($language) => $language->getLabel());

        $referencelanguages = $this->entityManager->getRepository(Language::class)->getReferenceLanguages();
        $referenceLanguageChoices = $this->getChoices($referencelanguages, fn($language) => $language->getLabel());

        if ($profile->isSuperAdministrator()) {
            $builder->add('brand', ChoiceType::class, [
                'label' => 'Brand',
                'multiple' => true,
                'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                'required' => false,
                'mapped' => false,
                'choices' => $brandChoices,
            ])
                ->add('brandList', ChoiceType::class, [
                    'label' => 'label',
                    'choice_label' => false,
                    'multiple' => true,
                    'mapped' => false,
                    'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                    'required' => false,
                    'choices' => [],
                ]);
        }
        if (!$profile->isCountryUser()) {
            $builder->add('country', ChoiceType::class, [
                'label' => 'Country',
                'multiple' => true,
                'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                'required' => false,
                'mapped' => false,
                'choices' => $profile->isBrandAdministrator() ? $countryByBrandChoices : $countryChoices,
            ])
                ->add('countryList', ChoiceType::class, [
                    'label' => 'label',
                    'choice_label' => false,
                    'multiple' => true,
                    'mapped' => false,
                    'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                    'required' => false,
                    'choices' => [],
                ])
                ->add('referenceLanguage', ChoiceType::class, [
                    'label' => 'reference_language',
                    'multiple' => true,
                    'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                    'required' => false,
                    'mapped' => false,
                    'choices' => $referenceLanguageChoices,
                ])
                ->add('referenceLanguageList', ChoiceType::class, [
                    'label' => 'label',
                    'choice_label' => false,
                    'multiple' => true,
                    'mapped' => false,
                    'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                    'required' => false,
                    'choices' => [],
                ]);
        }
        $builder->add('language', ChoiceType::class, [
            'label' => 'Language',
            'multiple' => true,
            'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
            'required' => false,
            'mapped' => false,
            'choices' => $profile->isCountryUser() ? $languageBySiteChoices : ($profile->isBrandAdministrator() ? $languageByCountriesChoices : $languageChoices),
        ])
            ->add('languageList', ChoiceType::class, [
                'label' => 'label',
                'choice_label' => false,
                'multiple' => true,
                'mapped' => false,
                'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                'required' => false,
                'choices' => [],
            ])
            ->add('source', ChoiceType::class, [
                'choices' => $channelHashmap,
                'required' => false,
                'multiple' => true,
                'mapped' => true,
            ]);

        $builder->addEventListener(FormEvents::PRE_SUBMIT, function (FormEvent $event) use ($brandChoices, $countryChoices, $languageChoices, $referenceLanguageChoices) {
            $form = $event->getForm();
            $data = $event->getData();
            $brandList = $data['brandList'] ?? [];
            $countryList = $data['countryList'] ?? [];
            $languageList = $data['languageList'] ?? [];
            $referenceLanguageList = $data['referenceLanguageList'] ?? [];

            $form->add('brand', ChoiceType::class, [
                'choices' => $brandChoices,
                'multiple' => true,
                'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                'required' => false,
                'mapped' => false,
            ]);
            if ($brandList) {
                $form->add('brandList', ChoiceType::class, [
                    'label' => false,
                    'mapped' => true,
                    'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                    'required' => false,
                    'choices' => $brandList,
                    'multiple' => true,
                ]);
            }
            $form->add('country', ChoiceType::class, [
                'choices' => $countryChoices,
                'multiple' => true,
                'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                'required' => false,
                'mapped' => false,
            ]);
            if ($countryList) {
                $form->add('countryList', ChoiceType::class, [
                    'label' => false,
                    'mapped' => true,
                    'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                    'required' => false,
                    'choices' => $countryList,
                    'multiple' => true,
                ]);
            }
            $form->add('language', ChoiceType::class, [
                'choices' => $languageChoices,
                'multiple' => true,
                'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                'required' => false,
                'mapped' => false,
            ]);
            if ($languageList) {
                $form->add('languageList', ChoiceType::class, [
                    'label' => false,
                    'mapped' => true,
                    'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                    'required' => false,
                    'choices' => $languageList,
                    'multiple' => true,
                ]);
            }
            $form->add('referenceLanguage', ChoiceType::class, [
                'choices' => $referenceLanguageChoices,
                'multiple' => true,
                'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                'required' => false,
                'mapped' => false,
            ]);
            if ($referenceLanguageList) {
                $form->add('referenceLanguageList', ChoiceType::class, [
                    'label' => false,
                    'mapped' => true,
                    'attr' => ['size' => 6, 'class' => self::CUSTOM_SELECT_CSS],
                    'required' => false,
                    'choices' => $referenceLanguageList,
                    'multiple' => true,
                ]);
            }
            $event->setData($data);
        });
    }

    public function getChoices($entities, $getter)
    {
        $names = array_map($getter, $entities);
        return array_combine($names, $names);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'site' => null,
            'profile' => null
        ]);
    }
}
