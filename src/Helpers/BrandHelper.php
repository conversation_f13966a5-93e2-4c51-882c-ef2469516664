<?php

namespace App\Helpers;

use Symfony\Component\Validator\Constraints as Assert;

final class BrandHelper
{
    /**
     * Get the full brand name by its code.
     */
    public static function getBrandName(string $brand): string
    {
        return match ($brand) {
            'AC' => 'Citroën',
            'AP' => 'Peugeot',
            'DS' => 'DS',
            'OP' => 'Opel',
            'VX' => 'Vauxhall',
            'XX' => 'Global',
            'JE' => 'Jeep',
            default => '',
        };
    }

    /**
     * Get all brand codes.
     *
     * @return string[]
     */
    public static function all(): array
    {
        return ['AP', 'AC', 'DS', 'OP', 'VX', 'XX', 'JE'];
    }

    /**
     * Get validation constraints for brand code.
     *
     * @return array<\Symfony\Component\Validator\Constraint>
     */
    public static function getConstraints(): array
    {
        return [
            new Assert\NotBlank(),
            new Assert\Choice(choices: self::all()),
        ];
    }
}

