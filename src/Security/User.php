<?php

namespace App\Security;

use App\Entity\Profile;
use ArrayIterator;
use <PERSON>enso\OidcBundle\Model\OidcUserData;
use IteratorAggregate;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Traversable;

/**
 * @implements IteratorAggregate<string, mixed>
 */
class User implements UserInterface, PasswordAuthenticatedUserInterface, IteratorAggregate
{
    /**
     * @var string
     */
    private $email;

    /**
     * @var array
     */
    private $roles;

    /**
     * @var string
     */
    private $username;

    /**
     * @var string
     */
    private $firstname;

    /**
     * @var string
     */
    private $lastname;

    /**
     * @var string
     */
    private $country;

    /**
     * @var string
     */
    private $locale;

    /**
     * @var array
     */
    private $groups;

    /**
     * @var string The hashed password
     */
    private $password;

    /**
     * @var Profile []
     */
    private $profiles;

    /**
     * selected profile.
     *
     * @var Profile
     */
    private $profile;

    /**
     * Implement the getIterator method to return an iterator for the properties.
     *
     * @return Traversable<string, mixed>
     */
    public function getIterator(): Traversable
    {
        return new ArrayIterator(get_object_vars($this));
    }

    public function getUserBrands(): array
    {
        $userBrands = [];
        if($this->getProfile()->isSuperAdministrator()){
            foreach ($this->getProfiles() as $profile) {
                $brand = $profile->getBrand();
                
                if(!empty($brand) && !isset($userBrands[$brand->getId()])){
                    $userBrands[$brand->getId()] = $brand;
                }
                $siteBrand = $profile->getSite()->getBrand();
                if(!empty($siteBrand) && !isset($userBrands[$siteBrand->getId()])){
                    $userBrands[$siteBrand->getId()] = $siteBrand;
                }
            }
        } else {
            if(!empty($this->getProfile()->getBrand())){
                $userBrands[] = $this->getProfile()->getBrand();
            }
 
            if(!empty($this->getProfile()->getSite()->getBrand())){
                $userBrands[] = $this->getProfile()->getSite()->getBrand();
            }
        }
        return $userBrands;
    }
 
    /**
     * A visual identifier that represents this user.
     *
     * @see UserInterface
     */
    public function getUserIdentifier(): string
    {
        return (string) $this->username;
    }

    /**
     * @deprecated since Symfony 5.3, use getUserIdentifier instead
     */
    public function getUsername(): string
    {
        return (string) $this->username;
    }

    /**
     * @see UserInterface
     */
    public function getRoles(): array
    {
        $roles = is_string($this->roles) ? [$this->roles] : $this->roles;
        // guarantee every user at least has ROLE_USER
        $roles[] = 'ROLE_USER';
        $roles[] = 'ROLE_ADMIN';

        return array_unique($roles);
    }

    public function setRoles(array $roles): self
    {
        $this->roles = $roles;

        return $this;
    }

    /**
     * @see PasswordAuthenticatedUserInterface
     */
    public function getPassword(): ?string
    {
        return $this->password;
    }

    public function setPassword(string $password): self
    {
        $this->password = $password;

        return $this;
    }

    /**
     * Returning a salt is only needed, if you are not using a modern
     * hashing algorithm (e.g. bcrypt or sodium) in your security.yaml.
     *
     * @see UserInterface
     */
    public function getSalt(): ?string
    {
        return null;
    }

    /**
     * @see UserInterface
     */
    public function eraseCredentials(): void
    {
        // If you store any temporary, sensitive data on the user, clear it here
        // $this->plainPassword = null;
    }

    /**
     * @param string $email
     *
     * @return self
     */
    public function setEmail($email)
    {
        $this->email = $email;

        return $this;
    }

    /**
     * @param string $username
     *
     * @return self
     */
    public function setUsername($username)
    {
        $this->username = $username;

        return $this;
    }

    /**
     * @return string
     */
    public function getFirstname()
    {
        return $this->firstname;
    }

    /**
     * @param string $firstname
     *
     * @return self
     */
    public function setFirstname($firstname)
    {
        $this->firstname = $firstname;

        return $this;
    }

    /**
     * @return string
     */
    public function getLastname()
    {
        return $this->lastname;
    }

    /**
     * @param string $lastname
     *
     * @return self
     */
    public function setLastname($lastname)
    {
        $this->lastname = $lastname;

        return $this;
    }

    /**
     * @return string
     */
    public function getCountry()
    {
        return $this->country;
    }

    /**
     * @param string $country
     *
     * @return self
     */
    public function setCountry($country)
    {
        $this->country = $country;

        return $this;
    }

    /**
     * @return string
     */
    public function getLocale()
    {
        return $this->locale;
    }

    /**
     * @param string $locale
     *
     * @return self
     */
    public function setLocale($locale)
    {
        $this->locale = $locale;

        return $this;
    }

    /**
     * @return array
     */
    public function getGroups()
    {
        return $this->groups;
    }

    /**
     * @return self
     */
    public function setGroups(array $groups)
    {
        $this->groups = $groups;

        return $this;
    }

    /**
     * Profiles.
     */
    public function getProfiles()
    {
        return $this->profiles;
    }

    public function setProfiles($profiles): self
    {
        $this->profiles = $profiles;

        return $this;
    }

    /**
     * Profile.
     */
    public function getProfile()
    {
        return $this->profile;
    }

    public function setProfile($profile): self
    {
        $this->profile = $profile;

        return $this;
    }

    /**
     * Magic function to set properties dynamically.
     *
     * @return void
     */
    public function __set(string $property, $value)
    {
        if (property_exists($this, $property)) {
            $this->$property = $value;
        }
    }

    public function setOidcUserData(?OidcUserData $userData)
    {
        if($userData){
            foreach ($this as $property => $value) {
                try {
                    $this->$property = $userData->getUserData($property);
                } catch (\Exception $e) {
                    throw $e;
                }
            }
        }

        return $this;
    }

    public function hasIdpRoleSuperAdmin(): bool
    {
        foreach($this->getProfile()->getRole()->getIdpRoles() as $idpRole) {
            if ($idpRole->getName() === 'SUPERADMIN') {
                return true;
            }
        }

        return false;
    }

}
