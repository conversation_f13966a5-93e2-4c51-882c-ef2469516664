<script>
    $(document).ready(function () {
        $('#model_table').DataTable({
            initComplete: function () {
                this.api()
                    .columns()
                    .every(function () {
                        var column = this;
                        var index = column.index();
                        var select_id = '';

                        if (index == 0) {
                            select_id = 'search_name';
                        } else if (index == 1) { 
                            select_id = 'search_brand';
                        }

                        if (select_id) {
                            var select = $('<select class="form-control custom-select"><option value=""></option></select>')
                                .appendTo('#' + select_id)
                                .on('change', function () {
                                    var val = $.fn.dataTable.util.escapeRegex($(this).val());
                                    column.search(val ? '^' + val + '$' : '', true, false).draw();
                                });
                            column
                                .data()
                                .unique()
                                .sort()
                                .each(function (d, j) {
                                    select.append('<option value="' + d + '">' + d + '</option>');
                                });
                        }
                    });
            },
            language: {
                "search": "{{ 'datatable.search'|trans }}",
                "lengthMenu": "{{ 'datatable.length_menu'|trans }}",
                "info": "{{ 'datatable.showing'|trans }}",
                "infoEmpty": "{{ 'datatable.no_records'|trans }}",
                "zeroRecords": "{{ 'datatable.no_records'|trans }}",
                paginate: {
                    next: '<i class="fa fa-chevron-right fa-lg">',
                    previous: '<i class="fa fa-chevron-left fa-lg">',
                }
            },
            columnDefs: [
                { targets: 'actions', orderable: false }
            ],
        });
    });
</script>
