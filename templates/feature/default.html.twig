{% extends '_layout/layout.html.twig' %}

{% block title %}
	{{title|trans}}
{% endblock %}

{% block content %}
	{% include '_layout/form_card_header.html.twig' with
    {
        'title': 'features' | trans
    }%}
	<div class="card card-primary card-outline card-outline-tabs">
		<div class="card-header p-0 border-bottom-0">
			<ul class="nav nav-tabs" id="nav-tab" role="tablist">
				{% for source, form in forms %}
					<li class="nav-item">
						<a class="nav-link {% if loop.index == 1 %} active {% endif %}" id="nav-{{source}}-tab" data-toggle="tab" href="#nav-{{source}}" role="tab" aria-controls="nav-{{source}}" aria-selected="true">{{source}}</a>
					</li>
				{% endfor %}
			</ul>
		</div>
		<div class="card-body">
			<div class="tab-content" id="nav-tabContent">
				{% for source, form in forms %}
					<div class="tab-pane fade show {% if loop.index == 1 %} active {% endif %}" id="nav-{{source}}" role="tabpanel" aria-labelledby="nav-{{source}}-tab">
						<div class="card-body">
							{{ form_start(form, { 'action': "?source="~source, 'attr': {'class': 'form-horizontal'}}) }}
							{{ form_errors(form) }}
							{% set form_view = indexbysection(form) %}
							{% set fieldIndex = 0 %}
							{% for section, rows in form_view %}
								{% if section != "default" and section is not empty %}
									<h5 class="">{{section|trans}}</h5>
								{% endif %}

								{% for language, row in rows %}
									{% for field in row %}
										{{ include('feature/modal/media_model.html.twig', {'prefix': field.vars.id}) }}

										<div class="multi-fields" data-section="{{field.vars.id}}">
											{% if field.vars.row_attr.isMultifield is defined and field.vars.row_attr.isMultifield == true %}
												{% include "feature/multi_fields.html.twig" %}
											{% else %}
												{% if ('hidden' not in field.vars.block_prefixes) and ('hidden' not in field.vars.row_attr)%}
													<div class="row">
														<div class="col-md-3">
															{% set checkstatus = '' %}
															{% if field.vars.attr.dbvalue is defined %}
																{% if field.vars.attr.dbvalue != '' %}{% set checkstatus = 'checked="checked"' %}{% endif %}
															{% endif %}
															{% set isNotDefaultCheckbox = form_label(field) is empty and field.vars.name != 'enabled' and field.vars.name != 'isEarlyAdopters' %}
															{% if(not profile.isSuperAdministrator()) %}
																{% if isNotDefaultCheckbox and not field.vars.disabled %}
																<div class="form-check form-switch" style="display: inline-block; vertical-align: middle;">
																	<input name="switch-{{ field.vars.name }}" class="form-check-input form-switch-input" type="checkbox" role="switch" id="switch-{{ field.vars.name }}" targetinput="{{ field.vars.id }}" {{ checkstatus }}  >
																</div>
																{% elseif isNotDefaultCheckbox and field.vars.disabled %}
																<div style="display: inline-block; color: #515151; margin-right: 25px;">
																	<i class="fas fa-lock"></i>
																</div>
																{% endif %}
															{% endif %}

															{% if form_label(field) is not empty %}
																{{ form_label(field,null, {'label_attr': {'class': 'feature-label'}}) }}
															{% endif %}

															<span class="lower-camel-case-label">
																<span>{{ labelToLowerCamelCase(field.vars.name|replace({'_input': ''}))}}</span>
															</span>
															{% if language != "default" %}
																<img src="{{asset('images/flags/'~language~'.svg')}}" style="max-width: 2rem;">
															{% endif %}
														</div>
														<div class="{{ form_label(field) is empty ? 'col-md-4' : 'col-md-6' }}">
															{% if field.vars.row_attr.type is defined and (field.vars.row_attr.type == 'mediaType' or field.vars.row_attr.type == 'fileInput')%}
																<div class="preview-media-block d-flex">
																	{% set url = field.vars.name|replace({ '_input' : ''}) %}
																	{{ form_widget(field) }}
																	{% if field.parent.vars.data[url] is defined and field.parent.vars.data[url] is not empty %}
																		<div class="preview-block-image">
																			<div class="d-flex">
																				<a class="preview-media-link" target="_blank" href="{{field.parent.vars.data[url]}}">
																					<img src="{{field.parent.vars.data[url]}}" class="preview-media-multi-field" alt="">
																				</a>
																				<i class="fas fa-trash preview-media-delete"></i>
																			</div>
																			<span class="preview-media-name">{{field.parent.vars.data[url] |split('/')|last}}</span>
																		</div>
																	{% endif %}
																</div>
															{% else %}
															<div style="display: inline-block; vertical-align: middle;">
																{% set disabledstatus = false %}
																{% if not profile.isSuperAdministrator() and isNotDefaultCheckbox %}
																	{% if field.vars.attr.dbvalue is not defined 
																		or (field.vars.attr.dbvalue is defined and field.vars.attr.dbvalue == '') %}
																		{% set disabledstatus = true %}
																	{% endif %}
																{% endif %}
																{{ form_widget(field, {'attr': field.vars.attr, 'disabled' : disabledstatus }) }}
																{{ form_help(field) }}
															</div>
															{% endif %}
														</div>
														{% if field.vars.attr['tooltip'] is defined and field.vars.attr['tooltip'] != null %}
															<div class="col-md-2">
																<div class="tooltip-container" id="tooltip-container-{{ source }}-{{fieldIndex}}"></div>
																<a tabindex="0" class="values-tooltip" data-toggle="tooltip" data-container="#tooltip-container-{{ source }}-{{fieldIndex}}" title="{{ field.vars.attr['tooltip'] }}">
																	<span style="cursor: pointer">
																		<i class="fa fa-info-circle"></i>
																	</span>
																</a>
															</div>
														{% endif %}
														<span class="alert text-danger">
															{{ form_errors(field) }}
														</span>
													</div>
													<hr/>
												{% endif %}
											{% endif %}
										</div>
										{% set fieldIndex = fieldIndex + 1 %}
									{% endfor %}
								{% endfor %}
							{% endfor %}

							<div class="text-right mt-0">
								<button class="btn btn-success" type="submit">{{ button_label|default('save')|trans }}</button>
							</div>
							{{ form_end(form) }}
						</div>
					</div>
				{% endfor %}
			</div>
		</div>
	</div>
{% endblock %}

{% block script %}
	{{ parent() }}
	<script type="text/javascript" src="{{ asset('js/image-picker/image-picker.js') }}"></script>
	<link href="{{ asset('css/image-picker/image-picker.css') }}" rel="stylesheet">
	<script type="text/javascript" src="{{ asset('js/multi-fields.js') }}"></script>
	<script>
		$(document).ready(function () {
			console.log('forms');
			console.log({{ dump(forms) }});
			console.log('end forms');
		});
	</script>

	{{ include('widget/_multi_value_feature_js.html.twig') }}
{% endblock %}
