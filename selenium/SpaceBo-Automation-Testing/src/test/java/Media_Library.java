import org.openqa.selenium.WebElement;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.io.File;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


public class Media_Library {
    @Test(groups = {"Media Library"})
    public void Add_Update_Delte_copy() throws InterruptedException {

        Methods user = new Methods();
        user.login();
        Locators.Media_Library().click();
        List<WebElement> App_items = Locators.Leaf_APP();
        List<WebElement> WEB_items = Locators.Leaf_Web();

        String c5_Image = "src/Files/c5.jpeg";
        String c5_Image_Path = new File(c5_Image).getAbsolutePath();
        String c3_Image = "src/Files/C3.jpg";
        String c3_Image_Path = new File(c3_Image).getAbsolutePath();
        Locators.APP().click();
        for (WebElement Brands : App_items) {
            Brands.click();
            Locators.ADD_Media().click();
            Locators.ADD_Browse().sendKeys(c3_Image_Path);
            Locators.Alt_Text().sendKeys("Citroen_C3");
            Locators.Cpyright_Text().sendKeys("yes");
            Locators.Comment_Text().sendKeys("Testing purpose");
            Locators.Media_Save().click();
            TimeUnit.SECONDS.sleep(12);
            Locators.Media_Img_lastdiv().click();
            Locators.EDIT_Media().click();
            TimeUnit.SECONDS.sleep(10);
            Locators.Close_File().click();
            Locators.ADD_Browse().sendKeys(c5_Image_Path);
            Locators.Edit_Alt_Text().clear();
            Locators.Edit_Alt_Text().sendKeys("Citroen_C5_Edited");
            Locators.Edit_Cpyright_Text().clear();
            Locators.Edit_Cpyright_Text().sendKeys("no");
            Locators.Edit_Comment_Text().clear();
            Locators.Edit_Comment_Text().sendKeys("Automation Testing purpose");
            Locators.EDIT_Save().click();
            TimeUnit.SECONDS.sleep(10);
            Locators.Media_Img_lastdiv().click();
            Locators.Media_Copy_Url().click();
            Locators.Media_Delete().click();
            TimeUnit.SECONDS.sleep(2);
            Locators.Confirm_Media_Delete().click();
            TimeUnit.SECONDS.sleep(5);
        }
        Locators.APP().click();
        Locators.WEB().click();
        for (WebElement Brands : WEB_items) {
            Brands.click();
            Locators.ADD_Media().click();
            Locators.ADD_Browse().sendKeys(c3_Image_Path);
            TimeUnit.SECONDS.sleep(2);
            Locators.Alt_Text().sendKeys("Citroen_C3");
            Locators.Cpyright_Text().sendKeys("yes");
            Locators.Comment_Text().sendKeys("Testing purpose");
            Locators.Media_Save().click();
            TimeUnit.SECONDS.sleep(12);
            Locators.Media_Img_lastdiv().click();
            Locators.EDIT_Media().click();
            TimeUnit.SECONDS.sleep(10);
            Locators.Close_File().click();
            Locators.ADD_Browse().sendKeys(c5_Image_Path);
            Locators.Edit_Alt_Text().clear();
            Locators.Edit_Alt_Text().sendKeys("Citroen_C5_Edited");
            Locators.Edit_Cpyright_Text().clear();
            Locators.Edit_Cpyright_Text().sendKeys("no");
            Locators.Edit_Comment_Text().clear();
            Locators.Edit_Comment_Text().sendKeys("Automation Testing purpose");
            Locators.EDIT_Save().click();
            TimeUnit.SECONDS.sleep(10);
            Locators.Media_Img_lastdiv().click();
            Locators.Media_Copy_Url().click();
            Locators.Media_Delete().click();
            TimeUnit.SECONDS.sleep(2);
            Locators.Confirm_Media_Delete().click();
            TimeUnit.SECONDS.sleep(5);
            user.Scroll_Down();
            TimeUnit.SECONDS.sleep(1);
        }
        user.exit();
    }
}
