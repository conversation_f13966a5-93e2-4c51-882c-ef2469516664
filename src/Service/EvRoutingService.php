<?php


namespace App\Service;


use App\Repository\EvRoutingRepository;

class EvRoutingService
{

    /**
     * @var EvRoutingRepository
     */
    private $_evRoutinRepository;

    public function __construct( EvRoutingRepository $evRoutingRepository)
    {
        $this->_evRoutinRepository = $evRoutingRepository;
    }

    public function normalizeApiData(string $brand) {

            return $this->_evRoutinRepository->findBy(['brand' => $brand]);

    }

}