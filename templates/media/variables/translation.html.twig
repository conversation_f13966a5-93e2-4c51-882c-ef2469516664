<script>
    var folderAddedTrans = "{{'folder_added'|trans }}";
        folderEditedTrans = "{{'folder_edited'|trans }}",
        folderDeletedTrans = "{{'folder_deleted'|trans }}",
        folderAddedErrorTrans = "{{'error_add'|trans }}",
        folderEditedErrorTrans = "{{'error_edit'|trans }}",
        folderDeletedErrorTrans = "{{'error_delete'|trans }}",
        mediaAddTitleAlert = "{{'media_added'|trans }}",
        mediaEditTitleAlert = "{{'media_edited'|trans }}",
        mediaValidationError = "{{'error_media_validation'|trans}}",
        mediaAddError = "{{'error_media_add'|trans}}",
        mediaEditError = "{{'error_media_edit'|trans}}",
        mediaDeleteError = "{{'error_media_delete'|trans}}",
        mediaDeletedTitle = "{{'media_deleted'|trans }}",
        mediaExistHeaderAlert = "{{'error_media_existing_header'|trans}}",
        mediaExistTitleAlert = "{{'error_media_existing_title'|trans}}",
        mediaExistConfirmAlert = "{{'error_media_existing_confirm'|trans}}",
        mediaExistCancelAlert = "{{'error_media_existing_cancel'|trans}}";
        invalidFolderNameErrorTrans = "{{'invalid_folder_name'|trans}}";
</script>