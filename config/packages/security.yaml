security:
    # https://symfony.com/doc/current/security.html#registering-the-user-hashing-passwords
    password_hashers:
        algorithm: plaintext
        Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface: 'plaintext'
    # https://symfony.com/doc/current/security.html#loading-the-user-the-user-provider
    providers:
        mockfile_provider:
            id: App\Security\UserProvider
        oidc_provider:
            id: App\Security\OidcUserProvider
    firewalls:
        dev:
            pattern: ^/(_(profiler|wdt)|css|images|js)/
            security: false
        mock:
            lazy: true
            form_login:
                # "app_login" is the name of the route created previously
                login_path: app_login
                check_path: app_login
                enable_csrf: true
            provider: mockfile_provider
            request_matcher: App\Security\MockUsersRequestMatcher
            logout:
                path: app_logout
                # where to redirect after logout
                # target: app_any_route
            # activate different ways to authenticate
            # https://symfony.com/doc/current/security/impersonating_user.html
        main:
            lazy: true
            logout:
                path: app_logout
                target: after_logout
            oidc:
                login_path: /login_oidc
                check_path: /login_check
                default_target_path: /profile/login
                always_use_default_target_path: true
                use_logout_target_path: true
                enable_end_session_listener: true
            provider: oidc_provider

    # Easy way to control access for large sections of your site
    # Note: Only the *first* access control that matches will be used
    access_control:
        - { path: ^/page/, roles: PUBLIC_ACCESS }
        - { path: ^/login, roles: PUBLIC_ACCESS }
        - { path: ^/profile/login, roles: ROLE_USER }
        - { path: ^/logout, roles: ROLE_USER }
        - { path: /profile/submit-profile, roles: ROLE_USER }
        - { path: ^/user-settings, roles: ROLE_USER }
        - { path: /favorite/update, roles: ROLE_USER }
        - { path: ^/general, roles: ROLE_USER }
        - { path: ^/admin/profile/*, roles: PUBLIC_ACCESS }
        - { path: ^/v1, roles: PUBLIC_ACCESS }
        - { path: ^/$, roles: ROLE_USER }
        - { path: ^/*, roles: ROLE_ADMIN }
        - { path: ^/v1/ev_routing, roles: IS_AUTHENTICATED_ANONYMOUSLY }
        - { path: ^/after-logout, roles: PUBLIC_ACCESS }
        
    access_decision_manager:
        strategy: unanimous
when@test:
    security:
        password_hashers:
            # By default, password hashers are resource intensive and take time. This is
            # important to generate secure password hashes. In tests however, secure hashes
            # are not important, waste resources and increase test times. The following
            # reduces the work factor to the lowest possible values.
            Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface:
                algorithm: auto
                cost: 4 # Lowest possible value for bcrypt
                time_cost: 3 # Lowest possible value for argon
                memory_cost: 10 # Lowest possible value for argon
