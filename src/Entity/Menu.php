<?php

namespace App\Entity;

use App\Repository\MenuRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Attribute\Groups;

#[ORM\Entity(repositoryClass: MenuRepository::class)]
class Menu
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    #[Groups(['package_export'])]
    private ?int $id = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    #[Groups(['package_export'])]
    private ?string $label;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    #[Groups(['package_export'])]
    private ?string $iconClass;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    #[Groups(['package_export'])]
    private ?string $routeName;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    #[Groups(['package_export'])]
    private ?string $parameters;

    #[ORM\OneToMany(mappedBy: 'menu', targetEntity: ProfileMenu::class, orphanRemoval: true, cascade: ['persist'])]
    private Collection $profileMenus;

    #[ORM\ManyToOne(targetEntity: Menu::class, inversedBy: 'subMenus', cascade: ['persist'])]
    #[Groups(['package_export'])]
    private ?Menu $parent;

    #[ORM\OneToMany(targetEntity: Menu::class, mappedBy: 'parent')]
    private Collection $subMenus;

    #[ORM\ManyToOne(inversedBy: 'menus')]
    private ?FeatureSetting $feature = null;

    #[ORM\OneToMany(mappedBy: 'menu', targetEntity: RoleMenu::class, orphanRemoval: true, cascade: ['persist'])]
    #[Groups(['package_export'])]
    private Collection $roleMenus;

    public function __construct()
    {
        $this->profileMenus = new ArrayCollection();
        $this->subMenus = new ArrayCollection();
        $this->roleMenus = new ArrayCollection();
    }


    public function getId(): ?int
    {
        return $this->id;
    }

    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function setLabel(?string $label): self
    {
        $this->label = $label;

        return $this;
    }

    public function getIconClass(): ?string
    {
        return $this->iconClass;
    }

    public function setIconClass(?string $iconClass): self
    {
        $this->iconClass = $iconClass;

        return $this;
    }

    public function getRouteName(): ?string
    {
        return $this->routeName;
    }

    public function setRouteName(?string $routeName): self
    {
        $this->routeName = $routeName;

        return $this;
    }

    public function getParameters(): ?string
    {
        return $this->parameters;
    }

    public function setParameters(?string $parameters): self
    {
        $this->parameters = $parameters;

        return $this;
    }

    /**
     * @return Collection<int, ProfileMenu>
     */
    public function getProfileMenus(): Collection
    {
        return $this->profileMenus;
    }

    public function addProfileMenu(ProfileMenu $profileMenu): self
    {
        if (!$this->profileMenus->contains($profileMenu)) {
            $this->profileMenus->add($profileMenu);
            $profileMenu->setMenu($this);
        }

        return $this;
    }

    public function removeProfileMenu(ProfileMenu $profileMenu): self
    {
        if ($this->profileMenus->removeElement($profileMenu)) {
            // set the owning side to null (unless already changed)
            if ($profileMenu->getMenu() === $this) {
                $profileMenu->setMenu(null);
            }
        }

        return $this;
    }

    public function getParent(): ?self
    {
        return $this->parent;
    }

    public function setParent(?self $parent): self
    {
        $this->parent = $parent;

        return $this;
    }

    /**
     * @return Collection<int, self>
     */
    public function getSubMenus(): Collection
    {
        return $this->subMenus;
    }

    public function addSubMenu(self $menu): self
    {
        if (!$this->subMenus->contains($menu)) {
            $this->subMenus[] = $menu;
            $menu->setParent($this);
        }

        return $this;
    }

    public function removeSubMenu(self $menu): self
    {
        if ($this->subMenus->removeElement($menu)) {
            // set the owning side to null (unless already changed)
            if ($menu->getParent() === $this) {
                $menu->setParent(null);
            }
        }
        return $this;
    }

    public function getFeature(): ?FeatureSetting
    {
        return $this->feature;
    }

    public function setFeature(?FeatureSetting $feature): self
    {
        $this->feature = $feature;

        return $this;
    }

    /**
     * @return Collection<int, RoleMenu>
     */
    public function getRoleMenus(): Collection
    {
        return $this->roleMenus;
    }

    public function addRoleMenu(RoleMenu $roleMenu): self
    {
        if (!$this->roleMenus->contains($roleMenu)) {
            $this->roleMenus->add($roleMenu);
            $roleMenu->setMenu($this);
        }

        return $this;
    }

    public function removeRoleMenu(RoleMenu $roleMenu): self
    {
        if ($this->roleMenus->removeElement($roleMenu)) {
            // set the owning side to null (unless already changed)
            if ($roleMenu->getMenu() === $this) {
                $roleMenu->setMenu(null);
            }
        }

        return $this;
    }

    public function __toString(): string
    {
        return (string) $this->label;
    }
}
