import org.openqa.selenium.Keys;
import org.testng.annotations.Test;

import java.util.concurrent.TimeUnit;
public class Settings_Release {

    @Test(groups = {"Settings Release"})
    public void Release() throws InterruptedException {
        Methods user = new Methods();
        user.login();
        TimeUnit.SECONDS.sleep(2);
        Locators.Administration().click();
        TimeUnit.SECONDS.sleep(2);
        Locators.Settings_Release().click();
        Locators.Settings_Release_Source().sendKeys("APP", Keys.ENTER);
        Locators.Settings_Release_Source().sendKeys("WEB", Keys.ENTER);
        Locators.Settings_Release_Btn().click();
        TimeUnit.SECONDS.sleep(1);
        user.exit();
    }

}
