<script>
   $(document).ready(function () {
    var $deleteModal = $('#delete-vehicle-modal'),
        $deleteForm  = $deleteModal.find('#delete-vehicle-form'),
        deleteAction = $deleteForm.attr('action');

    $('body').on('click', 'a[href="#delete-vehicle-modal"]', function (event) {
        event.preventDefault();

        var configNbr = $(this).data('config-nbr');
        if (configNbr > 0) {
            return false;
        }

        var modelId = $(this).data('vehicle-model-id');
        $deleteForm.attr('action', deleteAction.replace(':id', modelId));

        // For Bootstrap 5
        var bsModal = new bootstrap.Modal($deleteModal[0]);
        bsModal.show();
    });

    $deleteModal.on('hidden.bs.modal', function () {
        $deleteForm.attr('action', deleteAction);
    });
});
</script>
