<?php

namespace App\Entity;

use App\Repository\LcdvEvRoutingRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: LcdvEvRoutingRepository::class)]
#[ORM\Table(name: 'lcdv_ev_routing')]
#[UniqueEntity(
    fields: ['lcdv', 'evRouting'],
    repositoryMethod: 'findUniqueLcdvEvRouting',
    message: 'evrouting_config_unique',
    ignoreNull: true
)]
class LcdvEvRouting
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'string', length: 32, nullable: true)]
    #[Assert\Type(type: 'alnum', message: 'lcdvlabel_lcdv_notvalid')]
    private ?string $lcdv = null;

    #[ORM\Column(type: 'datetime', name: 'created_at', options: ['default' => 'CURRENT_TIMESTAMP'])]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\ManyToOne(targetEntity: EvRouting::class, inversedBy: 'lcdvs', fetch: 'EAGER')]
    #[ORM\JoinColumn(nullable: false)]
    private ?EvRouting $evRouting = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getLcdv(): ?string
    {
        return $this->lcdv;
    }

    public function setLcdv(string $lcdv): self
    {
        $this->lcdv = $lcdv;
        return $this;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getEvRouting(): ?EvRouting
    {
        return $this->evRouting;
    }

    public function setEvRouting(?EvRouting $evRouting): self
    {
        $this->evRouting = $evRouting;
        return $this;
    }
}
