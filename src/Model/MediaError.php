<?php

namespace App\Model;

use Symfony\Component\HttpFoundation\Response;

class MediaError{
    private $code = Response::HTTP_INTERNAL_SERVER_ERROR;
    private $message;
    private $name;

    public function setCode(int $code): self
    {
        $this->code = $code;
        return $this;
    }

    public function setMessage(string $message): self
    {
        $this->message = $message;
        return $this;
    }

    public function setName(string $name): self
    {
        $this->name = $name;
        return $this;
    }

    public function getArrayFormat(): array
    {
        return [
            'status'    => $this->code ?: Response::HTTP_BAD_REQUEST,
            'message' => $this->message ?? '',
            'name' => $this->name ?? ''
        ];
    }
}