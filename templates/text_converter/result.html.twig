{% extends 'base2.html.twig' %}

{% block title %}HTML Output
{% endblock %}

{% block body %}
	<div
		class="mt-4">
		<!-- Header with Path and Action Buttons -->
		<div class="d-flex justify-content-between align-items-center mb-3 p-4 bg-light rounded">
			<div class="path-display">
				<strong class="text-primary" style="font-size: 1.1rem; margin-right: 0.5rem;">Path:</strong>
				<span class="font-monospace" style="font-weight: 500;">{{ path }}</span>
			</div>

			<div class="action-buttons">

				<form id="previewForm" action="{{ path('static_create') }}" method="post">
					{{ form_start(form) }}
					<div
						style="display: none;">
						{# Hidden form fields #}
						{% for field in form %}
							{{ form_widget(field, { attr: { type: 'hidden' } }) }}
						{% endfor %}
					</div>
					
					<input type="hidden" name="action" id="action_input" value="">

					<div class="action-buttons">
						<a href="javascript:history.back()" class="btn btn-outline-secondary me-2 py-3 px-4">
							<i class="fas fa-edit me-2"></i>
							Back to Edited
						</a>

						<!-- Save Button -->
						<button type="submit" onclick="setAction('save')" name="action" value="save" class="btn btn-success">
							<i class="fas fa-save me-2"></i>
							Save
						</button>

						<!-- Publish Button -->
						<button type="submit" onclick="setAction('publish')" name="action" value="publish" class="btn btn-warning">
							<i class="fas fa-paper-plane me-2"></i>
							Publish
						</button>
					</div>
					{{ form_end(form) }}
				</form>
			</div>
		</div>

		{% if htmlContent is empty %}
			<div class="alert alert-warning text-center">
				No HTML content was received. Please check your form submission.
			</div>
		{% endif %}

		<!-- Centered Tabs Navigation -->
		<ul class="nav nav-tabs justify-content-center mt-4" id="htmlTabs" role="tablist">
			<li class="nav-item" role="presentation">
				<button class="nav-link active" id="rendered-tab" data-bs-toggle="tab" data-bs-target="#rendered" type="button" role="tab">Rendered Output</button>
			</li>
			<li class="nav-item" role="presentation">
				<button class="nav-link" id="code-tab" data-bs-toggle="tab" data-bs-target="#code" type="button" role="tab">HTML Code</button>
			</li>
		</ul>

		<!-- Tabs Content -->
		<div
			class="tab-content mt-3" id="htmlTabsContent">
			<!-- Rendered Output -->
			<div class="tab-pane fade show active" id="rendered" role="tabpanel">
				<div class="card w-100">
					<div class="card-header">
						<h3>Rendered Output</h3>
					</div>
					<div class="card-body">
						{{ htmlContent|raw }}
					</div>
				</div>
			</div>

			<!-- HTML Code -->
			<div class="tab-pane fade" id="code" role="tabpanel">
				<div class="card w-100">
					<div class="card-header">
						<h3>HTML Code</h3>
					</div>
					<div class="card-body bg-dark text-white p-3">
						<pre><code>{{ htmlContent|e }}</code></pre>
					</div>
				</div>
			</div>
		</div>
	</div>

	<style>
		.path-display {
			padding: 0.75rem 1rem;
			background-color: #f8f9fa;
			border-radius: 6px;
			border-left: 4px solid #0d6efd;
			flex-grow: 1;
			margin-right: 2rem;
		}

		.action-buttons {
			display: flex;
			gap: 1rem;
		}

		@media(max-width: 768px) {
			.d-flex {
				flex-direction: column;
				gap: 1rem;
			}

			.path-display {
				margin-right: 0;
				width: 100%;
			}

			.action-buttons {
				width: 100%;
				justify-content: flex-end;
			}
		}
	</style>
	 <script>
	function setAction(actionValue) {
	document.getElementById('action_input').value = actionValue;
}
</script>

{% endblock %}
{% block script %}
	{{ parent() }}
<script>
	function setAction(actionValue) {
	document.getElementById('action_input').value = actionValue;
}
</script>

{% endblock %}
