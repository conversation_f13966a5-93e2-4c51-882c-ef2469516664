<?php

namespace App\Repository;

use App\Entity\Brand;
use App\Entity\Country;
use App\Entity\FeatureSetting;
use App\Entity\Role;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<FeatureSetting>
 *
 * @method FeatureSetting|null find($id, $lockMode = null, $lockVersion = null)
 * @method FeatureSetting|null findOneBy(array $criteria, array $orderBy = null)
 * @method FeatureSetting[]    findAll()
 * @method FeatureSetting[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class FeatureSettingRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, FeatureSetting::class);
    }

    public function save(FeatureSetting $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(FeatureSetting $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function findByLocalUserOrSuperAdmin(Role $role, ?Brand $brand, ?Country $country): array
    {
        $conn = $this->getEntityManager()->getConnection();

        // Start building the SQL query
        if (in_array($role->getLabel(), Role::GLOBAL_ADMIN_ROLES)) {
            // Fetch all feature settings for global roles
            $sql = 'SELECT * FROM feature_setting fs';
            $params = [];
        } elseif (in_array($role->getLabel(), Role::BRAND_ADMIN_ROLES)) {
            // Fetch feature settings only for Brand level
            $sql = 'SELECT * FROM feature_setting fs WHERE JSON_CONTAINS(fs.content, :level, \'$.form.level\') = 1';
            $params = ['level' => '"Brand"'];
        } else {
            // Fetch feature settings only for Local level
            $sql = 'SELECT * FROM feature_setting fs WHERE JSON_CONTAINS(fs.content, :level, \'$.form.level\') = 1';
            $params = ['level' => '"Local"'];
        }

         // Prepare and execute the query
        $stmt = $conn->prepare($sql);
        $result = $stmt->executeQuery($params)->fetchAllAssociative();

        return $result ?: [];
    }


    //    /**
    //     * @return FeatureSetting[] Returns an array of FeatureSetting objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('f')
    //            ->andWhere('f.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('f.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?FeatureSetting
    //    {
    //        return $this->createQueryBuilder('f')
    //            ->andWhere('f.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
}
