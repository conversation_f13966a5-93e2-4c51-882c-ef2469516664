<?php
namespace App\Form;

use App\DataTransformers\ArrayToStringTransformer;
use App\DataTransformers\StringToArrayTransformer;
use App\Entity\{Brand, TranslationKey, Widget};
use App\Repository\BrandRepository;
use App\Repository\ChannelRepository;
use App\Repository\WidgetRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Regex;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\DateTimeType;

class TranslationKeyFormType extends AbstractType
{
    private BrandRepository $brandRepository;

    /**
     * @var ArrayToStringTransformer
     */
    private $transformer;

    public function __construct(
        BrandRepository $brandRepository,
        StringToArrayTransformer $transformer,
        private ChannelRepository $channelRepository,
        private WidgetRepository $widgetRepository
    ) {
        $this->brandRepository = $brandRepository;
        $this->transformer = $transformer;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $channels = $this->channelRepository->findAll();
        $channelNames = array_map(function ($channel) {
            return $channel->getName();
        }, $channels);

        $channelHashmap = array_combine($channelNames, $channelNames);

        
        if ($options['data']->getId()) {
         
            $builder
                // other fields
                ->add('createdAt', DateTimeType::class, [
                    'mapped' => false,
                    'label' => 'create_date',
                    'widget' => 'single_text',
                ])
                ->add('updatedAt', DateTimeType::class, [
                    'mapped' => false,
                    'label' => 'update_date',
                    'widget' => 'single_text',
                ])
                ->add('releaseDate', DateTimeType::class, [
                    'mapped' => false,
                    'label' => 'release_date',
                    'widget' => 'single_text',
                ]);
        }

        $builder->add('channel', ChoiceType::class, [
                'choices' => $channelHashmap,
                'label' => 'channel',
                'label_attr' => ['class' => 'checkbox-custom checkbox-inline'],
                'expanded' => true,
                'multiple' => true,
                'constraints' => [new NotBlank(['message' => 'translation_key_channel_select'])]
                ]
            );

        $builder->add('label_key', TextType::class,
                [
                    'label' => 'label_key',
                    'required' => false,
                    'constraints' => [                       
                        new Regex(['pattern' => '/^[A-Za-z][A-Za-z0-9]*([_.][A-Za-z0-9]*)*$/']),
                        new NotBlank(['message' => 'translation_key_label_key_invalid']),
                    ]
                ]
            ) 
            ->add('feature', TextType::class, ['label' => 'feature', 'required' => false])
            ->add('widget', EntityType::class, [
                'class' => Widget::class,
                'choice_label' => 'name',
                'label' => 'widget',
                'multiple' => false,
                'choices' => $this->widgetRepository->findAll(),
                'required' => false,
            ])
            ->add('channel', ChoiceType::class, [
                'choices' => $channelHashmap,
                'label' => 'channel',
                'label_attr' => ['class' => 'checkbox-custom checkbox-inline '],
                'expanded' => true,
                'multiple' => true,
                'constraints' => [new NotBlank(['message' => 'translation_key_channel_select'])]
                ]
            )
            ->add('referenceTranslations', CollectionType::class, [
                'entry_type' => ReferenceTranslationFormType::class,
                'entry_options' => ['label' => 'reference_translations'],
            ])
            ->add('parameter_value', TextType::class, ['label' => 'parameter_value', 'required' => false]);


        $builder->addEventListener(FormEvents::PRE_SET_DATA, function (FormEvent $event) {
            $entity = $event->getData();
            $form = $event->getForm();

            if (!$entity || null === $entity->getId()) {
                $form->add('brand', EntityType::class, [
                    'class' => Brand::class,
                    'choice_label' => 'name',
                    'label' => 'brand',
                    'multiple' => true,
                    'mapped' => false,
                    'data' => $this->brandRepository->findAll()
                ]);

            } else {
                $form->add('brand', null, [
                    'label' => 'brand',
                    'disabled' => true
                ])
                ->add('channel', null, [
                    'label' => 'channel',
                    'disabled' => true
                ]);
            }

        });
        $builder->get('channel')->addModelTransformer($this->transformer);
    }   
    
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => TranslationKey::class,
        ]);
    }
}