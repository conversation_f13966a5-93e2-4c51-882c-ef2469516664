<div
	id="accordion" class="mb-3 border-dark ">

	<!-- Export's controls -->
	<div class="collapse" data-parent="#accordion" id="exportsBox">
		{% if isReference %}
			{% if isSuperAdmin %}
				<div class="card card-body bg-dark bg-opacity-10 m-1 p-4">
					<h5 class="text-dark">{{'export'|trans}}</h5>
					{% for export in exportsList %}
						<a href="javascript:void(0);" data-language="{{ export.language }}" onclick="generateFilteredExportUrl('{{ export.url }}')">{{ export.filename }}</a>
					{% endfor %}
				</div>
			{% else %}
				<div class="card card-body bg-danger bg-opacity-10 m-1 p-4">
					<h5>{{'export_denied_message' | trans}}</h5>
				</div>
			{% endif %}
			{% else %}
			<div class="card card-body bg-dark bg-opacity-10 m-1 p-4">
				<h5 class="text-dark">{{'export'|trans}}</h5>
				{% for export in exportsList %}
					<a href="javascript:void(0);" data-language="{{ export.language }}" onclick="generateFilteredExportUrl('{{ export.url }}')">{{ export.filename }}</a>
				{% endfor %}
			</div>
			
		{% endif %}
	</div>

	<!-- Import's controls -->
	<div class="collapse " data-parent="#accordion" id="importsBox">
		{% if isReference and not isSuperAdmin %}
			<div class="card card-body bg-danger bg-opacity-10 m-1 p-4">
				<h5>{{'export_denied_message' | trans}}</h5>
			</div>
		{% else %}
			<div class="card card-body bg-dark bg-opacity-10 m-1 p-4">
				<h5 class="text-dark">{{'import'|trans}}</h5>
				<form action="{{ importUrl }}" method="post" enctype="multipart/form-data">
					<label for="csv_file">{{'csv_box_comment'|trans}}:</label>
					<input type="hidden" name="MAX_FILE_SIZE" value="{{maxFileSizeLimit}}" />
					<input type="file" id="csv_file" name="csv_file" accept=".csv">
					<button type="submit">{{ 'upload'|trans}}</button>
				</form>
			</div>
		{% endif %}
	</div>

</div>


{% include '_common/_loader.html.twig' with { description: 'loader_import_in_progress' } %}