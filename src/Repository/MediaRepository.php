<?php

namespace App\Repository;

use App\Entity\Media;
use App\Entity\MediaDirectory;
use App\Entity\Profile;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * @method Media|null find($id, $lockMode = null, $lockVersion = null)
 * @method Media|null findOneBy(array $criteria, array $orderBy = null)
 * @method Media[]    findAll()
 * @method Media[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class MediaRepository extends ServiceEntityRepository
{
    public const LIMIT = 10;

    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Media::class);
    }

    public function getMediasPaginated(MediaDirectory $directory, $offset = 0, $limit = 10, string $search = ''): array
    {
        $query = $this->createQueryBuilder('m')
        ->select('m.id as id,
            m.name as name,
            m.source as source,
            m.extension as extension,
            m.textAlt as textAlt,
            m.copyright as copyright,
            m.size as size,
            m.path as path,
            m.creationDate as creationDate,
            m.lastUpdate as lastUpdate,
            m.comment as comment,
            m.createdBy as createdBy,
            r.name as role_name')
            ->leftJoin('m.profile', 'p')
            ->leftJoin('p.role', 'r')
            ->where('m.directory = :directory');
            $query->andWhere(
                $query->expr()->like(
                    $query->expr()->concat('m.name', $query->expr()->literal('.'), 'm.extension'),
                    ':search'
                )
            );
            $query->setParameter('directory', $directory->getId())
            ->setParameter('search', "%" . $search . "%")
            ->orderBy('m.id', 'asc')
            ->setMaxResults($limit)
            ->setFirstResult($offset);

            return $query->getQuery()
                   ->getScalarResult();
    }

    public function getMediasCount(MediaDirectory $directory): int
    {
        return $this->createQueryBuilder('m')
            ->select('count(m.id) as count')
            ->where('m.directory = :directory')
            ->setParameter('directory', $directory->getId())
            ->orderBy('m.id', 'asc')
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    public function uploadOne(Profile $profile, UploadedFile $file, MediaDirectory $mediaDirectory, string $createdBy = null, Media $media): Media
    {
        $extension = pathinfo($file->getClientOriginalName(), PATHINFO_EXTENSION);
        $name = $media->getName() ? $media->getName() . '.' . $extension : $file->getClientOriginalName();
        $filename = pathinfo($name, PATHINFO_FILENAME);
        $media->setSite($profile->getSite())
            ->setName($filename)
            ->setExtension(pathinfo($file->getClientOriginalName(), PATHINFO_EXTENSION))
            ->setSize($file->getSize())
            ->setDirectory($mediaDirectory)
            ->setCreatedBy($createdBy);
        $path = 'uploads/'. str_replace(' > ', '/', $mediaDirectory->getPath()).'/' . $name;
        $media->setPath($path);
        $em = $this->getEntityManager();
        $em->persist($media);
        $em->flush();

        return $media;
    }
}
