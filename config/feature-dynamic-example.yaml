# Example Feature Configuration with Dynamic Sections
# This shows how to configure choice fields with dynamic sections

feature:
  code: "example_dynamic_feature"
  title: "Dynamic Feature Example"
  enabled: true
  sources:
    - "PARTNER"
    - "INTERNAL"
  
  fields:
    # Choice field that controls dynamic sections
    - name: "deployment_type"
      type: "ChoiceType"
      label: "Deployment Type"
      section: "configuration"
      values:
        partner: "Partner"
        internal: "Internal"
      options:
        placeholder: "Select deployment type"
        # This is the key configuration for dynamic sections
        dynamic:
          partner: "Partner"
          internal: "Internal"
    
    # Fields that belong to the "partner" section
    - name: "partner_api_key"
      type: "TextType"
      label: "Partner API Key"
      section: "partner"
      options:
        required: false
        attr:
          placeholder: "Enter partner API key"
    
    - name: "partner_endpoint"
      type: "TextType"
      label: "Partner Endpoint URL"
      section: "partner"
      options:
        required: false
        attr:
          placeholder: "https://partner.example.com/api"
    
    - name: "partner_timeout"
      type: "NumberType"
      label: "Partner Timeout (seconds)"
      section: "partner"
      options:
        required: false
        attr:
          min: 1
          max: 300
    
    # Fields that belong to the "internal" section
    - name: "internal_database_host"
      type: "TextType"
      label: "Internal Database Host"
      section: "internal"
      options:
        required: false
        attr:
          placeholder: "localhost"
    
    - name: "internal_database_port"
      type: "NumberType"
      label: "Internal Database Port"
      section: "internal"
      options:
        required: false
        attr:
          min: 1
          max: 65535
          placeholder: "3306"
    
    - name: "internal_cache_enabled"
      type: "CheckboxType"
      label: "Enable Internal Cache"
      section: "internal"
      options:
        required: false

# Alternative configuration using nested structure
alternative_feature:
  code: "nested_dynamic_feature"
  title: "Nested Dynamic Feature"
  enabled: true
  
  form:
    fields:
      # Main choice field
      - name: "environment_type"
        type: "ChoiceType"
        label: "Environment Type"
        section: "default"
        values:
          development: "Development"
          production: "Production"
          staging: "Staging"
        options:
          dynamic:
            development: "Development"
            production: "Production"
            staging: "Staging"
      
      # Development-specific fields
      - name: "dev_debug_mode"
        type: "CheckboxType"
        label: "Enable Debug Mode"
        section: "development"
        options:
          required: false
      
      - name: "dev_log_level"
        type: "ChoiceType"
        label: "Log Level"
        section: "development"
        values:
          debug: "Debug"
          info: "Info"
          warning: "Warning"
          error: "Error"
        options:
          placeholder: "Select log level"
      
      # Production-specific fields
      - name: "prod_ssl_enabled"
        type: "CheckboxType"
        label: "Enable SSL"
        section: "production"
        options:
          required: false
      
      - name: "prod_monitoring_url"
        type: "TextType"
        label: "Monitoring URL"
        section: "production"
        options:
          required: false
          attr:
            placeholder: "https://monitoring.example.com"
      
      # Staging-specific fields
      - name: "staging_test_data"
        type: "CheckboxType"
        label: "Load Test Data"
        section: "staging"
        options:
          required: false
      
      - name: "staging_reset_schedule"
        type: "TextType"
        label: "Reset Schedule (cron)"
        section: "staging"
        options:
          required: false
          attr:
            placeholder: "0 2 * * *"

# Usage Instructions:
# 1. Add the 'dynamic' option to any ChoiceType field
# 2. The dynamic object maps choice values to section names
# 3. Create other fields with sections matching the dynamic section names
# 4. The JavaScript will automatically enable/disable sections based on selection
# 5. Use the debug functions in browser console:
#    - debugDynamicSections() - shows current state
#    - setupDynamicField('#field_id', {value1: 'Section1', value2: 'Section2'}) - manual setup
#    - testDynamicChoice('#field_id', 'new_value') - test value changes
