<?php

namespace App\Repository;

use App\Entity\Channel;
use App\Entity\ChannelType;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Channel>
 */
class ChannelRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Channel::class);
    }

    public function getChannelMap(): array
    {
        $channels = $this->findAll();
        $channelNames = array_combine(
            array_map(fn($channel) => $channel->getName(), $channels),
            array_map(fn($channel) => $channel->getName(), $channels)
        );

            if (is_array($channelNames)) {
                return $channelNames;
            }
            return [];
    }

    public function findGlobalChannels(): array
    {
        $entityManager = $this->getEntityManager();
        $qb = $entityManager->createQueryBuilder();

        $qb->select('c.name')
            ->from(Channel::class, 'c')
            ->innerJoin(ChannelType::class, 'ct', 'WITH', 'ct = c.channelType')
            ->where('ct.name = :globalType')
            ->setParameter('globalType', 'GLOBAL');

        $channels = $qb->getQuery()->getResult();
        if (is_array($channels)) {
            return $channels;
        }
        return [];
    }

    public function findBrandedChannels(): array
    {
        $entityManager = $this->getEntityManager();
        $qb = $entityManager->createQueryBuilder();

        $qb->select('c.name')
            ->from(Channel::class, 'c')
            ->innerJoin(ChannelType::class, 'ct', 'WITH', 'ct = c.channelType')
            ->where('ct.name = :brandType')
            ->setParameter('brandType', 'BRAND');

        $channels = $qb->getQuery()->getResult();
        if (is_array($channels)) {
            return $channels;
        }
        return [];
    }
}
