<?php

namespace App\Helpers;

use App\Model\MediaError;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

/**
 * Error response class
 */
class MediaErrorResponse implements IMediaResponseArrayFormat
{
    private $errors = [];

    public function addError(MediaError $mediaError) {

        $this->errors[] = $mediaError->getArrayFormat();
    }

    public function getErrors(): array {

        return $this->errors;
    }

    public function getArrayFormat(): array
    {
        return [
            'errors' => $this->errors
        ];
    }

    public function getJsonFormat(): JsonResponse
    {
        return new JsonResponse($this->getArrayFormat(), Response::HTTP_UNPROCESSABLE_ENTITY);
    }
}
