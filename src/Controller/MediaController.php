<?php

namespace App\Controller;

use App\Entity\Media;
use App\Entity\Profile;
use App\Service\MediaManager;
use App\Entity\MediaDirectory;
use App\Form\Media\EditMediaType;
use App\Helpers\LoggerTrait;
use Symfony\Component\Form\FormInterface;
use Symfony\Bundle\SecurityBundle\Security;
use App\Repository\MediaDirectoryRepository;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use App\Helpers\RoleHelper;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\Validator\Constraints as Assert;

#[Route(path: 'admin/profile/{profile}/medias', name: 'admin_media_')]
class MediaController extends AbstractController
{
    use LoggerTrait;
    public const FOLDER = 'uploads';
    public const LIMIT = 20;

    public function __construct(
        private string $mediaSize,
        private MediaManager $mediaManager,
        private ValidatorInterface $validator
        ) {}
    

    #[Route('/', name: 'index')]
    public function index(
        Profile $profile, 
        Request $request
    ): Response
    {        
        $formAdd = $this->createForm(EditMediaType::class);
        $formEdit = $this->createForm(EditMediaType::class);
        $selectedLanguage = $request->getLocale();

        return $this->render('media/index.html.twig', [
            'profile' => $profile,
            'language' => $selectedLanguage,
            'formAdd' => $formAdd->createView(),
            'formEdit' => $formEdit->createView(),
            'role' => RoleHelper::ROLES[$profile->getRole()->getName()] ?? '',
            'isSuperAdmin' =>RoleHelper::isSuperAdmin($profile->getRole()->getName()),
            'media_size' => number_format((int) $this->mediaSize / 1048576, 2),
        ]);
    }

    #[Route('/directory/add/{id}', name: 'directory_add', methods: ['POST'])]
    public function addDirectory(
        MediaDirectory $mediaDirectory,
        Request $request
    ): Response
    {
        if ($request->isXmlHttpRequest()) {
            $directoryName = trim($request->request->get('directory'), '');
            if ($this->validateFolderName($directoryName)) {
                $this->logger->error('invalide name while creating folder');
                return new JsonResponse(['error' => 'invalid_folder_name'], JsonResponse::HTTP_UNPROCESSABLE_ENTITY);
            }
            $createdDirectory = $this->mediaManager->createDirectory($mediaDirectory, $mediaDirectory->getSite(), $directoryName);
            if ($createdDirectory) {
                return new JsonResponse(['id' => $createdDirectory->getId()]);
            }
        }
        $this->logger->error('error while creating the new media folder');
        return new JsonResponse(['error' => 'NOT CREATED'], Response::HTTP_BAD_REQUEST);
    }

    #[Route('/directory/update/{id}', name: 'directory_update', methods: ['POST'])]
    public function updateDirectory(
        MediaDirectory $mediaDirectory,
        Request $request
    ): Response
    {
        if ($request->isXmlHttpRequest()) {
            $directoryName = trim($request->request->get('directory'), '');
            if ($this->validateFolderName($directoryName)) {
                $this->logger->error('invalide name while updating folder');
                return new JsonResponse(['error' => 'invalid_folder_name'], JsonResponse::HTTP_UNPROCESSABLE_ENTITY);
            }
            $mediaDirectory = $this->mediaManager->updateDirectory($mediaDirectory, $directoryName);
            if ($mediaDirectory) {
                return new JsonResponse(['id' => $mediaDirectory->getId()]);
            }
        }
        $this->logger->error('error while updating the media folder');
        return new JsonResponse(['error' => 'NOT UPDATED'], Response::HTTP_BAD_REQUEST);
    }

    #[Route('/directory/remove/{id}', name: 'directory_remove', methods: ['DELETE'])]
    public function removeDirectory(
        MediaDirectory $mediaDirectory,
        Request $request
    ): Response
    {
        if ($request->isXmlHttpRequest()) {
            if ($this->mediaManager->deleteDirectory($mediaDirectory)) {
                return new JsonResponse(['success' => 'Deleted']);
            }
        }
        $this->logger->error('error while deleting the media folder');
        return new JsonResponse(['error' => 'NOT DELETED'], Response::HTTP_BAD_REQUEST);
    }

    #[Route('/directory/media/{id}', name: 'directory_medias', methods: ['GET'])]
    public function getDirectoryMedias(
        MediaDirectory $mediaDirectory,
        Request $request
    ): Response
    {
        if ($request->isXmlHttpRequest()) {
            $offset = $request->get('page', 1) - 1;
            $search = trim($request->get('search', ''));
            $data = $this->getListMediasByDirectory($mediaDirectory, $offset, true, $search);

            return new JsonResponse(['success' => $data]);
        }
        $this->logger->error('error not supported request');
        return new JsonResponse(['error' => 'BAD REQUEST'], Response::HTTP_BAD_REQUEST);
    }


    #[Route('/{media}/delete', name: 'delete', methods: ['DELETE'])]
    public function delete(
        Profile $profile,
        Media $media,
        Request $request,
        MediaDirectoryRepository $mediaDirectoryRepository
    ): Response
    {
        if (!$this->isCsrfTokenValid('delete-media', $request->request->get('token'))) {
            $this->logger->error('error while deleting media file ');
            throw $this->createAccessDeniedException();
        }
        // Check if is allowed to delete medial
        if($this->mediaManager->hasGrantedToDoActions($media, $profile))
            {
                try {
                    // Delete file BDD
                    $mediaDirectoryId = $media->getParentDirectory()->getId();
                   $this->mediaManager->deleteMedia($media);
                } catch (\Exception /* DBALException */ $e) {
                    $this->logger->error('Error this media is used '. $e->getMessage());
                    return new JsonResponse(['error' => 'Error this media is used']);
                }
                $offset = $request->get('page', 1) - 1;
                $search = trim($request->request->get('search', ''));
                $mediaDirectory = $mediaDirectoryRepository->find($mediaDirectoryId);
                $data = $this->getListMediasByDirectory($mediaDirectory, $offset, true, $search);
                $data['mediaDirectoryId'] = $mediaDirectoryId;
                return new JsonResponse(['success' => true, 'data' => $data]);
        }else{
            $this->logger->error('error unautorized to delete media file');
            return new JsonResponse(['error' => 'Access Denied'], Response::HTTP_UNAUTHORIZED);
        }        
    }

    #[Route('/upload/{media?}', name: 'upload')]
    public function upload(
        Profile $profile,
        Request $request,
        MediaDirectoryRepository $mediaDirectoryRepository,
        Security $security,
        ?Media $media
    ): JsonResponse
    {
        try {
            $files = $request->files->get('images');
            $toErase = filter_var($request->request->get('toErase', false), FILTER_VALIDATE_BOOLEAN);
            $mediaDirectory = $mediaDirectoryRepository->findOneBy(['id' => $request->request->get('parent_id')]) ?? new MediaDirectory();
            $media = $media ?? new Media();
            $success = [];
            $oldPath = $media->getPath();
            // we put the new informations in the media object
            $this->createMediaTypeForm(
                $request->request->get('data'),
                $request->getUri(),
                $request->getMethod(),
                $media
            );
            $update = $media?->getId() ? true : false;
            $selectedLanguage = $request->getLocale();
            $createdBy = $security->getUser()->getUserIdentifier();
            // manage errors
            $errorResponse = $this->mediaManager->manageErrors($files, $selectedLanguage, $media, $mediaDirectory, $toErase);
            if($errorResponse->getErrors()) {
                return $errorResponse->getJsonFormat();
            }
            foreach ($files as $file) {
                $media = $update ? $media : new Media();
                // we put the new informations in the media object
                $this->createMediaTypeForm(
                    $request->request->get('data'),
                    $request->getUri(),
                    $request->getMethod(),
                    $media
                );
                $media->setProfile($profile);
                $name = $this->getFileName($file->getClientOriginalName(), $media->getName());
                $dir = $this->getDir($mediaDirectory?->getPath());
                $newPath = $dir . '/' . $name;
                // the case when adding a new media file
                if(!$update && !$toErase){
                    $this->mediaManager->addFile($profile, $file, $mediaDirectory, $createdBy, $media, $newPath);
                } // the case when updating a media file
                elseif($update && !$toErase){
                    $this->mediaManager->editFile($media, $newPath, $oldPath, $file);
                } // the case when trying to add a new media files with an exsisted one
                elseif($toErase && !$this->mediaManager->isFileExistInS3Bucket($newPath)) {
                    $this->mediaManager->addFile($profile, $file, $mediaDirectory, $createdBy, $media, $newPath);
                } // the case when erasing the media files
                else{
                    $this->mediaManager->toErease($mediaDirectory, $newPath, $oldPath, $file->getPathname(), $media, $file->getSize(), $file?->getClientMimeType(), $update);
                }

                if($media->getFileName()=="api_config"){
                    $this->mediaManager->versioning( $file->getPathname(), $file?->getClientMimeType());
                }
                if($media->getId()) {
                    $success[] = $this->mediaManager->getMediaDetails($media);
                }else{
                    $success[] = ['parentDirectoryId' => $mediaDirectory?->getId()];
                }
            }
            $offset = $request->get('page', 1) - 1;
            $search = trim($request->get('search', ''));
            $data = $this->getListMediasByDirectory($mediaDirectory, $offset, true, $search);
            
            return new JsonResponse(['success' => $success, 'data' => $data]);            
        } catch (\Exception $e) {
            return new JsonResponse([
                'message' => $e->getMessage(),
            ], JsonResponse::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    #[Route('/{media}/get', name: 'get', methods: ['GET', 'POST'])]
    public function getMedia(Media $media): Response
    {
        return new JsonResponse(['success' => $this->mediaManager->getMediaDetails($media)]);
    }

    #[Route('/get/tree', name: 'get_tree', methods: ['GET'])]
    public function getTree(MediaManager $manager, Profile $profile): Response
    {
        return new JsonResponse($manager->getTreeStructure($profile));
    }

    /**
     * Creates and returns a Form instance from the type of the form.
     *
     * @final
     */
    protected function createForm(string $type, $data = null, array $options = []): FormInterface
    {
        return $this->container->get('form.factory')->create($type, $data, $options);
    }

    private function createMediaTypeForm(?string $data, string $uri, string $method, Media $media): FormInterface{
        $result = [];
        parse_str($data, $result);
        $rq = Request::create($uri, $method, $result);
        return $this->createForm(EditMediaType::class, $media)
            ->handleRequest($rq);
    }

    private function getFileName(string $originName, ?string $mediaName): string {
        $extension = pathinfo($originName, PATHINFO_EXTENSION);
        return $mediaName ? $mediaName . '.' . $extension : $originName;
    }

    private function getDir(?string $path): string
    {
        return self::FOLDER . '/' . str_replace(' > ', '/', $path);
    }

    private function getListMediasByDirectory(MediaDirectory $mediaDirectory, int $offset, bool $recalculateTotal = false, string $search = ''){
        $data = $this->mediaManager->getMedias($mediaDirectory, $offset, self::LIMIT, $recalculateTotal, $search);
        $medias = $data['medias'] ?? [];
        $total = $data['totalMedias'] ?? 0;
        $totalPages = (int) ceil($total / self::LIMIT);
        return [
            'medias' => $medias ?? [],
            'total' => $total ?? [],
            'totalPages' => $totalPages ?? [],
        ];
    }

    private function validateFolderName(string $directoryName)
    {
        $errors = $this->validator->validate(
            compact('directoryName'),
            new Assert\Collection([
                'directoryName'    => new Assert\Regex("/^[a-zA-Z0-9_\- ]*$/"),
            ])
        );
        return count($errors);
    }
}