<?php
namespace App\Form;

use App\Entity\Brand;
use App\Entity\Country;
use App\Entity\Language;
use App\Repository\LanguageRepository;
use Doctrine\ORM\EntityRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

class ProfileLoginFormType extends AbstractType
{

    private UserInterface $user;
    /**
     * @var LanguageRepository
     */
    private $languageRepository;

    public function __construct(
        Security $security,
        LanguageRepository $languageRepository,
        private TranslatorInterface $translator
    )
    {
        $this->user = $security->getUser();
        $this->languageRepository = $languageRepository;
        $this->translator = $translator;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
   
        $brand = $this->user->getProfile()?->getBrand() ?? $this->user->getProfile()?->getSite()?->getBrand();
        
        $builder
            ->add('brand', EntityType::class, [
                'class' => Brand::class,
                'choice_label' => 'name',
                'choice_value' => 'code',
                'label' => 'brand',
                'placeholder' => 'choose_brand',
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('b');
                },
                'required' => false,
                'data' => $brand
            ])
            ->add('country', EntityType::class, [
                'class' => Country::class, 
                'choice_label' => 'name', 
                'choice_value' => 'code',
                'label' => 'country', 
                'placeholder' => 'choose_country',
                'required' => false,
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('c')
                        ->join('c.sites', 'site');
                },               
            ])
            ->add('language', EntityType::class, [
                'class' => Language::class, 
                'choice_label' => function ($language) {
                    return $this->translator->trans($language->getLabel());
                },
                'choice_value' => 'code',
                'label' => 'default_language',
                'query_builder' => function (EntityRepository $er) {
                    return $er->createQueryBuilder('l')
                        ->where('l.isReference = :isReference')
                        ->setParameter('isReference', true);
                }
            ])
            ->add('profile', ChoiceType::class, [
                'choices' => $this->user->getProfiles(),
                'choice_value' => 'id',
                'group_by' => function ($profile) {
                    return $profile->getSite()->getLabel();
                },
                'choice_label' => function ($profile) {
                    return $profile->getRole()->getLabel() .' '. $profile->getBrand()?->getCode();
                },
                
                'choice_attr' => function($choice) {
                    $brand = $choice->getBrand() ? $choice->getBrand()->getCode() : $choice->getSite()?->getBrand()?->getCode();
            
                    return ['class' => "mb-1 brand-".$brand." country-".$choice->getSite()?->getCountry()?->getCode()];
                },
                'placeholder' => 'choose_profile',
                'label' => 'profile',
                'constraints' => [
                    new NotBlank()
                ],
            ]);
        $builder
        ->get('language')
        ->addModelTransformer(new CallbackTransformer(
                    function ($languageCode) {
                        return  $this->languageRepository->findOneByCode($languageCode);
                    },
                    function ($language) {
                        return $language->getCode();
                    }
                )
            );
    }
}
