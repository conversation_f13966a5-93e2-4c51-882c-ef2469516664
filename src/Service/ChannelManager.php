<?php

namespace App\Service;

use App\Entity\Brand;
use App\Entity\Channel;
use App\Entity\Country;
use App\Entity\Language;
use App\Entity\LocalKeyJsonRelease;
use App\Repository\BrandRepository;
use App\Repository\ChannelRepository;
use App\Repository\CountryRepository;
use App\Repository\LanguageRepository;
use App\Repository\SiteRepository;
use Doctrine\ORM\EntityManagerInterface;
use App\Entity\MediaDirectory;
use Exception;

class ChannelManager
{
    public const GLOBAL_BRAND = "XX";
    public const GLOBAL_COUNTRY = "XX";


    public function __construct(
        private ChannelRepository $channelRepository,
        private SiteRepository $siteRepository,
        private BrandRepository $brandRepository,
        private CountryRepository $countryRepository,
        private LanguageRepository $languageRepository,
        private EntityManagerInterface $em,
    ) {}

    public function list(): array
    {
        return $this->channelRepository->findAll();
    }

    public function syncLocalization(Channel $channel): array
    {
        try {
            $type = $channel->getChannelType()->getName();
            $channelName = $channel->getName();
            $batchSize = 300;
            $count = 0;

            if ($type === "GLOBAL") {
                $globalBrand = $this->brandRepository->findOneBy(["code" => self::GLOBAL_BRAND]);
                $sites = $this->siteRepository->findBy(['brand' => $globalBrand]);
                $processedCountries = [];

                foreach ($sites as $site) {
                    $country = $site->getCountry();
                    if (
                        empty($country) ||
                        in_array($country->getCode(), $processedCountries)
                    ) {
                        continue;
                    }
                    $processedCountries[] = $country->getCode();

                    foreach ($site->getLanguages() as $language) {
                        $releaseKey = $this->createReleaseKey($channelName, $country, $language, $globalBrand);
                        $this->em->persist($releaseKey);
                        if (++$count % $batchSize === 0) {
                            $this->em->flush();
                            $this->em->clear();
                        }
                    }
                }
            } elseif ($type === 'BRAND') {
                $brands = $this->brandRepository->findAll();

                foreach ($brands as $brand) {
                    if ($brand->getCode() == self::GLOBAL_BRAND) {
                        continue;
                    }
                    $sites = $this->siteRepository->findBy(['brand' => $brand]);

                    foreach ($sites as $site) {
                        $country = $site->getCountry();
                        if (empty($country) || $country->getCode() == self::GLOBAL_COUNTRY) {
                            continue;
                        }
                        foreach ($site->getLanguages() as $language) {
                            $releaseKey = $this->createReleaseKey($channelName, $country, $language, $brand);
                            $this->em->persist($releaseKey);
                            if (++$count % $batchSize === 0) {
                                $this->em->flush();
                                $this->em->clear();
                            }
                        }
                    }
                }
            }

            $this->em->flush();
            $this->em->clear();
            return [true, "Localization Sync Successful"];
        } catch (Exception $e) {
            return [false, $e->getMessage()];
        }
    }

    public function mediaSync(Channel $channel): array
    {
        try {
            $managedChannel = $this->em->getReference(Channel::class, $channel->getId()) ?? $channel;
            $type = $channel->getChannelType()->getName();
            $mediaDirectory = new MediaDirectory();
            $mediaDirectory->setChannel($managedChannel);
            $mediaDirectory->setReadOnly(0);
            $mediaDirectory->setPath($managedChannel->getName());
            $mediaDirectory->setName($managedChannel->getName());
            $mediaDirectory->setLabel($managedChannel->getName());
            $this->em->persist($mediaDirectory);
            $this->em->flush();
            if ($type == 'GLOBAL') {
                $brands = $this->brandRepository->findBy(['code' => "XX"]);
                $brandDirectories = $this->createBrandMediaDirectories($managedChannel, $brands, $mediaDirectory);
                $this->createCountryMediaDirectories($managedChannel, $brandDirectories);
            } else {
                $brand = $this->brandRepository->findAll();
                $brandDirectories = $this->createBrandMediaDirectories($managedChannel, $brand, $mediaDirectory);
                $this->createCountryMediaDirectories($managedChannel, $brandDirectories);
            }
            return [true, "Media Sync Succesfully"];
        } catch (Exception $e) {
            return [false, $e->getMessage()];
        }
    }

    private function createBrandMediaDirectories(Channel $channel, array $brands, MediaDirectory $parentDirectory): array
    {
        $managedDirectory = $this->em->getReference(MediaDirectory::class, $parentDirectory->getId()) ?? $parentDirectory;
        $brandDirectories = [];
        foreach ($brands as $brand) {
            $brandDirectory = new MediaDirectory();
            $brandDirectory->setChannel($channel);
            $brandDirectory->setReadOnly(0);
            $brandDirectory->setPath($channel->getName() . " > " . $brand->getCode());
            $brandDirectory->setName($brand->getName());
            $brandDirectory->setLabel($brand->getCode());
            $brandDirectory->setParentDirectory($managedDirectory);
            $brandDirectory->setBrand($brand->getCode());
            $brandDirectories[] = $brandDirectory;
            $this->em->persist($brandDirectory);
        }
        $this->em->flush();
        return $brandDirectories;
    }

    private function createCountryMediaDirectories(Channel $channel, array $brandDirectories)
    {
        foreach ($brandDirectories as $brandDirectory) {
            $managedDirectory = $this->em->getReference(MediaDirectory::class, $brandDirectory->getId()) ?? $brandDirectory;
            $brand = $this->brandRepository->findOneBy(["code" => $brandDirectory->getBrand()]);
            $sites = $this->siteRepository->findUniqueCountriesByBrand($brand);
            foreach ($sites as $site) {
                $country = $site->getCountry();
                $countryDirectory = new MediaDirectory();
                $countryDirectory->setChannel($channel);
                $countryDirectory->setReadOnly(0);
                $countryDirectory->setPath($channel->getName() . " > " . $brand->getCode() . " > " . $country->getCode());
                $countryDirectory->setName($country->getName());
                $countryDirectory->setLabel($country->getCode());
                $countryDirectory->setParentDirectory($managedDirectory);
                $countryDirectory->setSite($site);
                $countryDirectory->setBrand($brand->getCode());
                $this->em->persist($countryDirectory);
            }
            $this->em->flush();
        }
    }

    private function createReleaseKey(string $channelName, Country $country, Language $language, Brand $brand): LocalKeyJsonRelease
    {
        $releaseKey = new LocalKeyJsonRelease();
        $releaseKey->setChannel($channelName)
            ->setCountry($country)
            ->setLanguage($language)
            ->setBrand($brand)
            ->setStatus('imported')
            ->setUpdatedDate(new \DateTime())
            ->setFilename('default_name')
            ->setImported_date(new \DateTime())
            ->setStatus("imported")
            ->setOld_filename("");

        return $releaseKey;
    }
}
