-- First, delete from tables depending on media
SET @channel_name1 = 'hii';
SET @channel_name2 = 'testChannel';

-- Delete media associated with the media directories of these channels
DELETE FROM `media`
WHERE `directory_id` IN (
    SELECT id FROM `media_directory`
    WHERE channel_id IN (
        SELECT id FROM channel WHERE `name` IN (@channel_name1, @channel_name2)
    )
);

-- Delete local key json releases
DELETE FROM `local_key_json_release` 
WHERE `channel` IN (@channel_name1, @channel_name2);

-- Delete media directories
DELETE FROM `media_directory`
WHERE channel_id IN (SELECT id FROM channel WHERE `name` IN (@channel_name1, @channel_name2));

-- Finally, delete channels
DELETE FROM `channel` 
WHERE `name` IN (@channel_name1, @channel_name2);