<?php

namespace App\Tests\Service;

use App\Entity\Brand;
use App\Entity\Channel;
use App\Entity\ChannelType;
use App\Entity\Country;
use App\Entity\Site;
use App\Entity\Language;
use Doctrine\Common\Collections\ArrayCollection;
use PHPUnit\Framework\TestCase;
use App\Repository\BrandRepository;
use App\Repository\ChannelRepository;
use App\Repository\CountryRepository;
use App\Repository\LanguageRepository;
use App\Repository\SiteRepository;
use App\Service\ChannelManager;
use Doctrine\ORM\EntityManagerInterface;

class ChannelManagerTest extends TestCase
{
    private ChannelManager $channelManager;
    private ChannelRepository $channelRepository;
    private SiteRepository $siteRepository;
    private BrandRepository $brandRepository;
    private CountryRepository $countryRepository;
    private LanguageRepository $languageRepository;
    private EntityManagerInterface $em;

    protected function setUp(): void
    {
        $this->channelRepository = $this->createMock(ChannelRepository::class);
        $this->siteRepository = $this->createMock(SiteRepository::class);
        $this->brandRepository = $this->createMock(BrandRepository::class);
        $this->countryRepository = $this->createMock(CountryRepository::class);
        $this->languageRepository = $this->createMock(LanguageRepository::class);

        $this->em = $this->createMock(EntityManagerInterface::class);
        $site = $this->createMock(Site::class);
        $country = $this->createMock(Country::class);
        $country->method("getCode")->willReturn("XX");
        $country->method("getName")->willReturn("Global");
        $site->method("getCountry")->willReturn($country);
        $this->siteRepository->method("findUniqueCountriesByBrand")->willReturn([$site]);
        $this->brandRepository->method("findOneBy")->willReturn($this->getBrand());

        $this->channelManager = new ChannelManager(
            $this->channelRepository,
            $this->siteRepository,
            $this->brandRepository,
            $this->countryRepository,
            $this->languageRepository,
            $this->em,
        );
    }

    private function getBrand(): Brand
    {
        $brand = new Brand;
        $brand->setCode("AC");
        $brand->setName("Citroen");
        return $brand;
    }


    public function testsyncLocalizationGlobal()
    {
        $channel = $this->createMock(Channel::class);
        $channelType = $this->createMock(ChannelType::class);
        $globalBrand = $this->createMock(Brand::class);
        $site = $this->createMock(Site::class);
        $languagesCollection = new ArrayCollection([$this->createMock(Language::class)]);
        $site->expects($this->any())->method("getCountry")->willReturn($this->createMock(Country::class));
        $site->expects($this->any())->method("getLanguages")->willReturn($languagesCollection);
        $this->siteRepository->expects($this->any())->method("findBy")->willReturn([$site]);
        $this->brandRepository->expects($this->any())->method("findOneBy")->willReturn($this->createMock(Brand::class));

        $channelType->expects($this->any())->method("getName")->willReturn("GLOBAL");
        $channel->expects($this->any())->method("getChannelType")->willReturn($channelType);
        $channel->expects($this->any())->method("getName")->willReturn("FLEET");
        [$bool_result, $message]=$this->channelManager->syncLocalization($channel);
        $this->assertIsBool($bool_result);
        $this->assertEquals(true, $bool_result);
        $this->assertEquals("Localization Sync Successful", $message);
    }

    public function testsyncLocalizationBrand()
    {
        $channel = $this->createMock(Channel::class);
        $channelType = $this->createMock(ChannelType::class);
        $globalBrand = $this->createMock(Brand::class);
        $site = $this->createMock(Site::class);
        $languagesCollection = new ArrayCollection([$this->createMock(Language::class)]);
        $site->expects($this->any())->method("getCountry")->willReturn($this->createMock(Country::class));
        $site->expects($this->any())->method("getLanguages")->willReturn($languagesCollection);
        $this->siteRepository->expects($this->any())->method("findBy")->willReturn([$site]);
        $this->brandRepository->expects($this->any())->method("findAll")->willReturn([$this->createMock(Brand::class)]);
        $channelType->expects($this->any())->method("getName")->willReturn("BRAND");
        $channel->expects($this->any())->method("getChannelType")->willReturn($channelType);
        $channel->expects($this->any())->method("getName")->willReturn("APP");
        [$bool_result, $message] = $this->channelManager->syncLocalization($channel);
        $this->assertIsBool($bool_result);
        $this->assertEquals(true, $bool_result);
        $this->assertEquals("Localization Sync Successful", $message);
    }

    public function testsyncMediaGlobal()
    {
        $channel = $this->createMock(Channel::class);
        $channel->method('getId')->willReturn(1);
        $channelType = $this->createMock(ChannelType::class);
        $channelType->method('getName')->willReturn("GLOBAL");
        $this->brandRepository->expects($this->any())->method("findBy")->willReturn([$this->getBrand()]);
        $channel->expects($this->any())->method("getChannelType")->willReturn($channelType);
        [$bool_result, $message]=$this->channelManager->mediaSync($channel);
        $this->assertIsBool($bool_result);
        $this->assertEquals(true, $bool_result);
        $this->assertEquals("Media Sync Succesfully", $message);
    }

    public function testsyncMediaBrand()
    {
        $channel = $this->createMock(Channel::class);
        $channelType = $this->createMock(ChannelType::class);
        $channelType->expects($this->any())->method("getName")->willReturn("BRAND");
        $this->brandRepository->expects($this->any())->method("findAll")->willReturn([$this->getBrand()]);
        $channel->expects($this->any())->method("getChannelType")->willReturn($channelType);
        [$bool_result, $message] = $this->channelManager->mediaSync($channel);
        $this->assertIsBool($bool_result);
        $this->assertEquals(true, $bool_result);
        $this->assertEquals("Media Sync Succesfully", $message);
    }
}
