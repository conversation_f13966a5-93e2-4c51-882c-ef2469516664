<?php

namespace App\Service;

use App\Entity\Site;
use App\Helpers\WSResponse;
use App\Service\PublishService;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Diff\Differ;
use App\Entity\LocalKeyJsonRelease;
use App\Repository\BrandRepository;
use App\Repository\CountryRepository;
use App\Repository\LanguageRepository;
use Symfony\Component\HttpFoundation\Response;
use App\Repository\LocalKeyJsonReleaseRepository;
use App\Repository\SiteRepository;

class PublishManager
{
    public function __construct(
        private PublishService $publishService,
        private BrandRepository $brandRepository,
        private SiteRepository $siteRepository,
        private LanguageRepository $languageRepository,
        private LocalKeyJsonReleaseRepository $LocalKeyJsonReleaseRepository,
        private CountryRepository $countryRepository
    ) {}

    public function saveMultiple(array $brands, array $countries, array $languages, array $referenceLanguages, array $sources): WSResponse
    {
        try {
            return $this->uploadLocalKeys($brands, $countries, $languages, $referenceLanguages, $sources);
        } catch (\Exception $e) {
            return new WSResponse($e->getCode(), $e->getMessage());
        }
    }

    public function save(?LocalKeyJsonRelease $localKey, Site $site): WSResponse
    {
        return $this->publishService->upload($localKey, $site);
    }

    public function uploadLocalKeys(array $brands, array $countries, array $languages, array $referenceLanguages, array $sources): WSResponse
    {
        $localKeys = $this->LocalKeyJsonReleaseRepository->findByBrandCountryLanguage($brands, $countries, $languages, $referenceLanguages, $sources);

        foreach ($localKeys as $localKey) {
            if ($localKey) {
                $site = $this->siteRepository->findOneBy(['brand' => $localKey->getBrand(), 'country' => $localKey->getCountry()]);
                if ($site) {
                    $this->publishService->upload($localKey, $site);
                }
            }
        }

        return new WSResponse(Response::HTTP_OK, true);
    }

    public function getS3Resource(LocalKeyJsonRelease $key, Site $site): array
    {
        $currentJson = $this->publishService->generateJson($key, $site);
        $publishedJson = $this->publishService->getUploadedJson($key);
        $path = $this->publishService->filename($key, "");
        $differ = new Differ();
        $packet = [
            'key' => $key,
            'diff' => $differ->diff($publishedJson, $currentJson),
            'languageCode' => $key->getLanguage()->getCode(),
            'currentJson' => $currentJson,
            'releasedJson' => $publishedJson,
            'path' => $path
        ];
        return $packet;
    }
}