<?php

namespace App\Service;

use App\Entity\TranslationKey;
use App\Entity\Widget;
use App\Entity\Brand;
use App\Entity\Country;
use App\Entity\Language;
use App\Repository\TranslationKeyRepository;
use Symfony\Contracts\Translation\TranslatorInterface;

class LabelTranslationManager
{
    public function __construct(
        private TranslationKeyRepository $translationKeyRepository,
        private TranslatorInterface $translator
    ) {}

    /**
     * @return array<TranslationKey>
     */
    public function getTranslationKeys(?Widget $widget = null): array
    {
        if ($widget === null) {
            return $this->translationKeyRepository->findAll();
        }

        return $this->translationKeyRepository->findBy(['widget' => $widget]);
    }

    public function getTranslationKeysList(array $requestParams, ?Widget $widget = null): array
    {
        $tkFields = ['channel', 'feature', 'labelKey', 'releaseDate'];
        $orderColumn = $requestParams['order'][0]['column'];
        $orderName = $requestParams['columns'][$orderColumn]['data'] ?? 'tk.id';
        if (in_array($orderName, $tkFields)) {
            $orderName = 'tk.' . $orderName;
        }
        $orderName = ($orderName === 'brandName') ? 'brand.name' : $orderName;
        $orderName = ($orderName === 'widgetName') ? 'widget.name' : $orderName;
        $orderName = ($orderName === 'translation') ? 'rt.translation' : $orderName;
        $orderdir = $requestParams['order'][0]['dir'] ?? 'ASC';
        $brand = $requestParams['brand'] ?? null;
        $channel = $requestParams['channel'] ?? null;
        $feature = $requestParams['feature'] ?? null;
        $widgetSearch = $requestParams['widget'] ?? null;
        $language = !empty($requestParams['language']) ? $requestParams['language'] : 'en';
        $translation = $requestParams['translation'] ?? null;

        $params = [
            'start' => $requestParams['start'] ?? 0,
            'length' => $requestParams['length'] ?? 10,
            'order' => ['name' => $orderName, 'dir' => $orderdir],
            'search' => $requestParams['search']['value'] ?? null,
            'searchParams' => [
                'brand' => $brand,
                'channel' => $channel,
                'feature' => $feature,
                'language' => $language,
                'translation' => $translation,
                'widget' => $widgetSearch
            ]
        ];

        return $this->translationKeyRepository->getAllTranslations($params);
    }

    public function getLocalTranslationsList(?Country $country, ?Language $language, Brand $brand, array $requestParams = []): array
    {
        $tkFields = ['channel', 'feature', 'labelKey', 'releaseDate', 'createdAt', 'updatedAt'];
        $orderColumn = $requestParams['order'][0]['column'];
        $orderName = $requestParams['columns'][$orderColumn]['data'] ?? 'tk.id';
        if (in_array($orderName, $tkFields)) {
            $orderName = 'tk.' . $orderName;
        }
        $orderName = ($orderName === 'brandName') ? 'brand.name' : $orderName;
        $orderName = ($orderName === 'widgetName') ? 'widget.name' : $orderName;
        $orderdir = $requestParams['order'][0]['dir'] ?? 'ASC';

        $channel = $requestParams['channel'] ?? null;
        $feature = $requestParams['feature'] ?? null;
        $widgetSearch = $requestParams['widget'] ?? null;
        $countrySearch = $requestParams['country'] ?? null;
        $translation = $requestParams['translation'] ?? null;
        $languageCode = $requestParams['languageCode'] ?? null;

        $params = [
            'start' => $requestParams['start'] ?? 0,
            'length' => $requestParams['length'] ?? 10,
            'order' => ['name' => $orderName, 'dir' => $orderdir],
            'search' => $requestParams['search']['value'] ?? null,
            'searchParams' => [
                'widget' => $widgetSearch,
                'channel' => $channel,
                'feature' => $feature,
                'country' => $countrySearch,
                'translation' => $translation,
                'languageCode' => $languageCode,
            ]
        ];

        $list = $this->translationKeyRepository->getAllLocalTranslations($params, $brand, $country, $language);

        if (!empty($list['data'])) {
            foreach ($list['data'] as $key => $value) {
                $list['data'][$key]['languageName'] = $this->translator->trans($value['languageName']);
            }
        }

        return $list;
    }


    public function getTranslatedKeysByWidget(Widget $widget, Brand $brand, Country $country, string $source, Language $language, Language $referenceLanguage): array
    {

        $localTranslations = $this->translationKeyRepository->getTranslatedKeysByWidget($widget, $brand, $country, $source, $language, $referenceLanguage);

        return array_column($localTranslations, 'translation', 'label');
    }

    public function getLabelsByWidget(?Widget $widget, ?Brand $brand, ?Country $country, ?string $source, ?Language $language, ?Language $referenceLanguage): array
    {
        return $this->translationKeyRepository->getTranslatedKeysByWidget($widget, $brand, $country, $source, $language, $referenceLanguage);
    }
}
