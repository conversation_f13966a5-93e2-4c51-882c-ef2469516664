<?php

namespace App\Form;

use App\Form\FormDataTransformers\CheckboxToStringTransformer;
use App\Helpers\FormTypeResolver;
use App\Helpers\RegexHelper;
use App\Service\WidgetManager;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;

class FeatureFormType extends AbstractType
{
    const MEDIA_TYPE = [
        'filetype',
        'imagetype',
        'mediatype'
    ];

    const MULTI_FIELDS_TYPE = "MultiFieldType";
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        foreach ($options['fields'] as $field) {
            //get field data
            $child = $field['name'];
            $transformer = $field['transformer'] ?? null;
            $fieldOptions = $field['options'] ?? [];

            // Add tooltip data to the field's attributes        
            $fieldOptions['attr']['tooltip'] = $this->renderTooltipHtmlPerField($child, $field['type'], $options['data']['tooltipValues'] ?? []);
            $fieldOptions['attr']['placeholder'] = $this->getPlaceholderValuePerField($child, $options['data']['placeholderData'] ?? []);
            $fieldOptions['attr']['overwrite'] = $field['overwrite'] ?? false;

            if(isset($options['data'][$child]) && !is_array($options['data'][$child])) {
                $fieldOptions['attr']['dbvalue'] = $options['data'][$child];    
            }

            if (isset($field['overwrite']) && !$field['overwrite']) {
                if (!empty($options['level']) && count($options['level']) >= 2) {
                    if (in_array("Global", $options['level']) && $options['profile'] != 'SuperAdmin') {
                        $fieldOptions['disabled'] = true;
                        $builder->add($field['name'] . '_hidden', HiddenType::class, [
                            'data' => null,
                        ]);
                    } else {
                        if (!in_array("Global", $options['level']) && in_array("Brand", $options['level']) && $options['profile'] != 'LocalAdmin') {
                            $fieldOptions['disabled'] = true;
                            $builder->add($field['name'] . '_hidden', HiddenType::class, [
                                'data' => null,
                            ]);
                        }
                    }
                }
            }

            if (
                isset($field['attributes']['regex']) &&
                isset($options['attr']['typeForm']) &&
                $options['attr']['typeForm'] == WidgetManager::WIDGET
            ) {
                $regexValue = $field['attributes']['regex'];
                $fieldOptions['constraints'] = RegexHelper::getListConstraints($regexValue);
            }
            $multiValue = $fieldOptions['attr']['multiValue'] ?? false;
            if ($field['type'] != self::MULTI_FIELDS_TYPE) {
                if (in_array(strtolower($field['type']), self::MEDIA_TYPE)) {
                    $fieldOptions['attr']['type'] = strtolower($field['type']);
                    $type = FileType::class;
                } else {
                    $type = ($multiValue) ? FeatureMultiValueType::class : FormTypeResolver::getClass($field['type']);
                }
            }
            $fieldMultiValueOptions = $fieldOptions;
            $fieldMultiValueOptions['data'] = ($multiValue && isset($options['data'][$child])) ? $options['data'][$child] : [];

            // Check if field is translatable
            if (isset($field['translatable']) and $field['translatable'] == true) {
                // Add field for each language
                foreach ($options['languages'] as $language) {
                    $languageCode = $language->getCode();
                    $fieldName = $child . '__' . $languageCode;
                    $fieldOptions['attr']['tooltip'] = $this->renderTooltipHtmlPerField($fieldName, $field['type'], $options['data']['tooltipValues'] ?? []);
                    $fieldOptions['attr']['placeholder'] = $this->getPlaceholderValuePerField($fieldName, $options['data']['placeholderData'] ?? []);
                    $fieldOptions['row_attr']['language'] = $languageCode;
                    if ($multiValue) {
                        $fieldMultiValueOptionsTrans = $fieldOptions;
                        $dataUrl = isset($options['data'][$fieldName]) ? $options['data'][$fieldName] : "";
                        $fieldMultiValueOptionsTrans['data'] = [
                            'selectValues' => json_decode($dataUrl),
                            'choicesData' => $dataUrl,
                            'constraints' => $fieldOptions['constraints'] ?? []
                        ];
                        $fieldMultiValueOptionsTrans['constraints'] = [];
                        $builder->add($fieldName, $type, $fieldMultiValueOptionsTrans);
                    } elseif ($field['type'] == self::MULTI_FIELDS_TYPE) {
                        $this->buildMultiFieldCollectionType(
                            $fieldOptions,
                            $options,
                            $builder,
                            $child,
                            $fieldName
                        );
                    } elseif (FileType::class == $type) {
                        $this->buildFileTypeForm($builder, $fieldName, $type, $fieldOptions);
                    } else {
                        $builder->add($fieldName, $type, $fieldOptions);
                    }
                    $builder = $this->manageTransformer($builder, $fieldName, $transformer);
                }
            } else {
                if ($field['type'] == self::MULTI_FIELDS_TYPE) {
                    $fieldName = $child;
                    $this->buildMultiFieldCollectionType(
                        $fieldOptions,
                        $options,
                        $builder,
                        $child,
                        $fieldName
                    );
                } elseif (!$multiValue) {
                    // Add non-translatable field
                    if (FileType::class == $type) {
                        $this->buildFileTypeForm($builder, $child, $type, $fieldOptions);
                    } else {
                        $builder->add($child, $type, $fieldOptions);
                    }
                } else {
                    $fieldMultiValueOptions['data']['constraints'] = $fieldMultiValueOptions['constraints'] ?? [];
                    $fieldMultiValueOptions['constraints'] = [];
                    $builder->add($child, $type, $fieldMultiValueOptions);
                }
                $builder = $this->manageTransformer($builder, $child, $transformer);
            }

            if($field['type'] == 'CheckboxType') {
                $builder->add($field['name'] . '_hidden', HiddenType::class, [
                    'mapped' => false,
                    'data' => empty($options['data'][$field['name']]) ? 'disabled' : ''
                ]);
            }
        }

        $builder->add('source', HiddenType::class, ['row_attr' => ['section' => ''], 'data' => $options['data']['source'] ?? '', 'label' => false]);

        if($options['profile'] != 'SuperAdmin') {
            $builder->addEventListener(FormEvents::SUBMIT, function (FormEvent $event) use ($options) {
                $data = $event->getData();
                $form = $event->getForm();
                
                foreach ($options['fields'] as $field) {
                    if($field['type'] == 'CheckboxType' && $field['name'] != 'enabled' && $field['name'] != 'isEarlyAdopters') {
                        $fieldName = $field['name'];
                        $hiddenFieldName = "{$fieldName}_hidden";
                        $isDisabled = $form->get($hiddenFieldName)->getData() === 'disabled';
                        
                        if ($isDisabled) {
                            $data[$field['name']] = '';
                        }
                    }
                }
                $event->setData($data);
            });
            
        }   
    }

    private function renderTooltipHtmlPerField(string $fieldName, string $fieldType, array $data): string
    {
        $html = '';
        $fieldTypeLower = strtolower($fieldType);
        $isCheckboxType = $fieldTypeLower === 'checkboxtype';

        if (!empty($data)) {
            if (!isset($data['brand']) && !isset($data['global']) && isset($data[$fieldName])) {
                $value = $this->cleanUpDataRender($isCheckboxType, $data[$fieldName], $fieldName);
                $html .= "Global: " . ucfirst($value != '' ? $value : 'empty') . "<br>";
            } else {
                $dataSources = [
                    'global' => $data['global'][$fieldName] ?? null,
                    'brand' => $data['brand'][$fieldName] ?? null
                ];

                foreach ($dataSources as $source => $value) {
                    $value = $this->cleanUpDataRender($isCheckboxType, $value, $fieldName);
                    $html .= ucfirst($source) . ": " . ucfirst($value != '' ? $value : 'empty') . "<br>";
                }
            }
        }

        return $html;
    }

    private function buildFileTypeForm(FormBuilderInterface &$builder, string $key, $type, ?array $fieldOptions = [])
    {
        $constraints = $fieldOptions['constraints'] ?? [];
        $typefield = $fieldOptions['attr']['type'];
        switch ($typefield) {
            case 'mediatype':
                $placeholder = 'choose_media';
                break;
            case 'imagetype':
                $placeholder = 'choose_image';
                break;
            default:
                $placeholder = 'choose_file';
        }
        $label = isset($fieldOptions['row_attr']['language']) ? $fieldOptions['label']. "-" . $fieldOptions['row_attr']['language']  : $fieldOptions['label'];
        $builder
            ->add($key . '_input', $type, [
                'label' => $label ?? $key,
                'data_class' => null,
                'row_attr' => [
                    'type' => 'fileInput',
                    'section' => 'default',
                ],
                'attr' => [
                    'placeholder' => $placeholder,
                ],
                'required'=>false
            ])
            ->add($key, TextType::class, [
                'label' => false,
                'row_attr' => [
                    'section' => 'default',
                    'hidden' => true
                ],
                'attr' => [
                    'class' => 'd-none image-type-error'
                ],
                'required' => $fieldOptions['required'],
                'constraints' => $constraints
            ]);
    }

    private function cleanUpDataRender($isCheckboxType, $value, $fieldName)
    {
        if ($isCheckboxType) {
            if ($value === '') {
                return 'disabled';
            }
            return $value === 'false' ? 'unchecked' : 'checked';
        }
        if ($fieldName === 'earlyAdoptersList' && is_array($value)) {
            return implode(',', $value);
        }
        return str_replace('_', ' ', $value);
    }

    private function getPlaceholderValuePerField(string $fieldName, array $data): string
    {
        $child = $fieldName;
        $placeholder = null;

        if (isset($data['global'])) {
            // Local 
            if (isset($data['brand']) && !empty($data['brand'][$child])) {
                $placeholder = $data['brand'][$child];
            } else if (!empty($data['global'][$child])) {
                $placeholder = $data['global'][$child];
            }
        } else if (isset($data[$child])) {
            // Brand 
            $placeholder = $data[$child] ?? null;
        }

        if ($fieldName == 'earlyAdoptersList' && !is_null($placeholder)) {
            $placeholder = implode(',', $placeholder);
        }
        return $placeholder != null ? ucfirst($placeholder) : '';
    }

    /**
     * Adds a model transformer to a field.     
     */
    public function manageTransformer(
        FormBuilderInterface $builder, 
        string $fieldName, 
        ?string $transformer
    ): FormBuilderInterface
    {
        if ($transformer) {
            $builder->get($fieldName)->addModelTransformer(new $transformer());
        }
        return $builder;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'level' => [],
            'profile' => [],
            'fields' => [],
            'languages' => []
        ]);
    }

    private function buildMultiFieldCollectionType(
        array $fieldOptions,
        array $options,
        FormBuilderInterface &$builder,
        string $child,
        ?string $fieldName = ''
    ) {
        $data = [];
        $fieldOptions['row_attr']['isMultifield'] = true;
        $fieldOptions['row_attr']['label'] = $fieldOptions['label'];
        $fieldOptions['row_attr']['languages'] = $options['languages'];
        $fieldOptions['row_attr']['profile'] = $options['profile'];

        $data['row_attr'] = $fieldOptions['row_attr'];
        $entryOptions = array_merge(
            $data,
            [
                'label_attr' => $fieldOptions['model'],
                'attr' => [
                    'typeForm' => $options['attr']['typeForm'] ?? null
                ]
            ]
        );

        $collectionData = json_decode($options['data'][$fieldName] ?? '', true);
        if (!is_null($collectionData)) {
            $fields = $fieldOptions['model'];
            $collectionData = array_map(function ($item) use ($fields) {
                return array_intersect_key($item, $fields);
            }, $collectionData);
        }

        $builder->add($fieldName, CollectionType::class, [
            'entry_type' => MultiFieldType::class,
            'allow_add' => true,
            'allow_delete' => true,
            'label' => $child,
            'entry_options' => $entryOptions,
            'block_name' => $fieldName,
            'row_attr' => $fieldOptions['row_attr'],
            'required' => false,
            'by_reference' => false,
            'data' => $collectionData
        ]);
    }
}
