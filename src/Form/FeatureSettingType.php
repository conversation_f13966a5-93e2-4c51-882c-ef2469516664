<?php

namespace App\Form;

use App\Entity\FeatureSetting;
use App\Entity\Role;
use App\Service\FeatureSettingManager;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Contracts\Translation\TranslatorInterface;

class FeatureSettingType extends AbstractType
{
    public function __construct(private TranslatorInterface $translator) {}

    private const ALLOWED_FIELD_TYPES = [
        'TEXTTYPE',
        'URLTYPE',
        'CHOICETYPE',
        'CHECKBOXTYPE',
        'TEXTAREATYPE',
        'SELECTTYPE',
        'MULTIFIELDTYPE',
        'DATETYPE',
        'MEDIATYPE'
    ];

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $isFileRequired = $options['isFileRequired'];
        /** @var Role $role */
        $role = $options['role'];
        $builder
            ->add('name', TextType::class, [
                'constraints' => [
                    new NotBlank(),
                ],
            ])
            ->add('file', FileType::class, ['data_class' => null, 'required' => $isFileRequired, 'mapped' => false]);

        // Add an event listener if File is Required
        $builder->addEventListener(FormEvents::POST_SUBMIT, function (FormEvent $event) use ($role) {
            $form = $event->getForm();
            $uploadedFile = $form->get('file')->getData();

            if ($role->getLabel() !== 'Super Administrator') {
                $form->get('file')->addError(new FormError($this->translator->trans('feature_setting_invalid_role_error')));
            } else {
                if ($uploadedFile) {
                    $jsonData = file_get_contents($uploadedFile->getRealPath());
                    $data = json_decode($jsonData, true);

                    if (json_last_error() !== JSON_ERROR_NONE) {
                        $form->get('file')->addError(new FormError($this->translator->trans('feature_setting_invalid_json_error')));
                    } elseif (empty($data['form']['level'])) {
                        // Add a form error if 'level' is empty
                        $form->get('file')->addError(new FormError($this->translator->trans('feature_setting_level_error')));
                    } else {
                        $levels = $data['form']['level'];
                        foreach ($levels as $level) {
                            if (!in_array($level, FeatureSettingManager::FEATURE_SETTING_LEVELS)) {
                                $form->get('file')->addError(new FormError($this->translator->trans('feature_setting_invalid_level_error', ['%level%' => $level])));
                            }
                        }
                        // Validate field types
                        foreach ($data['form']['fields'] as $field) {
                            $type = $field['type'] ?? null;
                            if ($type && !in_array(strtoupper($type), self::ALLOWED_FIELD_TYPES, true)) {
                                $form->get('file')->addError(new FormError($this->translator->trans('feature_setting_invalid_type_error', ['%type%' => $type])));
                            }
                        }
                    }
                }
            }
        });
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => FeatureSetting::class,
            'isFileRequired' => false,
            'role' => null,
            'validation_groups' => ['edit']
        ]);
    }
}
