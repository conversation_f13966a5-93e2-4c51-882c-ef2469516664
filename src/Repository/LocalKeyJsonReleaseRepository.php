<?php

namespace App\Repository;

use App\Entity\Brand;
use App\Entity\Country;
use App\Entity\Language;
use App\Entity\LocalKeyJsonRelease;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<LocalKeyJsonRelease>
 *
 * @method LocalKeyJsonRelease|null find($id, $lockMode = null, $lockVersion = null)
 * @method LocalKeyJsonRelease|null findOneBy(array $criteria, array $orderBy = null)
 * @method LocalKeyJsonRelease[]    findAll()
 * @method LocalKeyJsonRelease[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LocalKeyJsonReleaseRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, LocalKeyJsonRelease::class);
    }


    public function getfilename(Country $country, Language $language, Brand $brand, string $source)
    {
        $data = $this->createQueryBuilder('l')
            ->andWhere('l.country = :country')
            ->andWhere('l.language = :language')
            ->andWhere('l.brand = :brand')
            ->andWhere('l.channel = :source')
            ->setParameter('country', $country)
            ->setParameter('language', $language)
            ->setParameter('brand', $brand)
            ->setParameter('source', $source)
            ->getQuery()
            ->getResult();

        return $data;
    }

    public function keysByBrandCountryAndLanguages(Country $country, Brand $brand, array $languages): array
    {
        $data = $this->createQueryBuilder('l')
            ->andWhere('l.country = :country')
            ->andWhere('l.brand = :brand')
            ->andWhere('l.language IN (:languages)')
            ->setParameter('country', $country)
            ->setParameter('brand', $brand)
            ->setParameter('languages', $languages)
            ->getQuery()
            ->getResult();

        if (is_array($data)) {
            return $data;
        } else {
            return [];
        }
    }

    public function updateStatusBy(array $brands, array $channels, array $localLanguages, array $countries = []): void
    {
        $entityManager = $this->getEntityManager();
        $qb = $entityManager->createQueryBuilder();

        $qb->update(LocalKeyJsonRelease::class, 'r')
            ->set('r.status', ':status')
            ->where($qb->expr()->in('r.brand', ':brands'))
            ->andWhere($qb->expr()->in('r.channel', ':channels'))
            ->andWhere($qb->expr()->in('r.language', ':localLanguages'))
            ->setParameter('status', 'Updated')
            ->setParameter('brands', $brands)
            ->setParameter('channels', $channels)
            ->setParameter('localLanguages', $localLanguages);

        if (!empty($countries)) {
            $qb->andWhere($qb->expr()->in('r.country', ':countries'))
                ->setParameter('countries', $countries);
        }

        $qb->getQuery()->execute();
    }

    public function findByBrandCountryLanguage($brands, $countries, $languages, $referenceLanguages, $sources): array
    {
        $qb = $this->createQueryBuilder('l');

        if (!empty($brands)) {
            $qb->innerJoin('l.brand', 'b')
                ->andWhere('b.name IN (:brands)')
                ->setParameter('brands', $brands);
        }

        if (!empty($countries) || !empty($referenceLanguages)) {
            $qb->innerJoin('l.country', 'c');

            if (!empty($countries)) {
                $qb->andWhere('c.name IN (:countries)')
                    ->setParameter('countries', $countries);
            }

            if (!empty($referenceLanguages)) {
                $qb->innerJoin('c.sites', 's')
                    ->andWhere('s.country = c')
                    ->innerJoin('s.preferedLanguage', 'preferedLanguage')
                    ->andWhere('preferedLanguage.label IN (:referenceLanguages)')
                    ->setParameter('referenceLanguages', $referenceLanguages);
            }
        }

        if (!empty($languages)) {
            $qb->innerJoin('l.language', 'lang')
                ->andWhere('lang.label IN (:languages)')
                ->setParameter('languages', $languages);
        }

        if (!empty($sources)) {
            $qb->andWhere('l.channel IN (:sources)')
                ->setParameter('sources', $sources);
        }

        return $qb->getQuery()->getResult();
    }
}
