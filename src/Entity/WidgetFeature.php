<?php

namespace App\Entity;

use App\Repository\WidgetFeatureRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Attribute\Groups;

#[ORM\Entity(repositoryClass: WidgetFeatureRepository::class)]
class WidgetFeature
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[Groups(['package_export'])]
    #[ORM\Column(length: 255)]
    private ?string $code = null;

    #[Groups(['package_export'])]
    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $description = null;

    #[Groups(['package_export'])]
    #[ORM\Column]
    private ?bool $enabled = null;

    #[Groups(['package_export'])]
    #[ORM\Column(length: 255)]
    private ?string $label = null;

    #[Groups(['package_export'])]
    #[ORM\OneToMany(mappedBy: 'widgetFeature', targetEntity: WidgetFeatureAttribute::class, orphanRemoval: true, cascade: ["persist"])]
    private Collection $attributes;

    #[Groups(['package_export'])]
    #[ORM\ManyToOne(inversedBy: 'widgetFeatures',cascade: ["persist"])]
    #[ORM\JoinColumn(nullable: false)]
    private ?Widget $widget = null;

    #[Groups(['package_export'])]
    #[ORM\ManyToOne(cascade: ["persist"])]
    #[ORM\JoinColumn(nullable: false)]
    private ?Brand $brand = null;

    #[Groups(['package_export'])]
    #[ORM\ManyToOne(cascade: ["persist"])]
    private ?Country $country = null;

    #[Groups(['package_export'])]
    #[ORM\Column(length: 255)]
    private ?string $source = null;

    public function __construct()
    {
        $this->attributes = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): self
    {
        $this->description = $description;

        return $this;
    }

    public function isEnabled(): ?bool
    {
        return $this->enabled;
    }

    public function setEnabled(bool $enabled): self
    {
        $this->enabled = $enabled;

        return $this;
    }

    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function setLabel(string $label): self
    {
        $this->label = $label;

        return $this;
    }

    /**
     * @return Collection<int, WidgetFeatureAttribute>
     */
    public function getAttributes(): Collection
    {
        return $this->attributes;
    }

    public function addAttribute(WidgetFeatureAttribute $attribute): self
    {
        if (!$this->attributes->contains($attribute)) {
            $this->attributes->add($attribute);
            $attribute->setWidgetFeature($this);
        }

        return $this;
    }

    public function removeAttribute(WidgetFeatureAttribute $attribute): self
    {
        if ($this->attributes->removeElement($attribute)) {
            // set the owning side to null (unless already changed)
            if ($attribute->getWidgetFeature() === $this) {
                $attribute->setWidgetFeature(null);
            }
        }

        return $this;
    }

    public function getWidget(): ?Widget
    {
        return $this->widget;
    }

    public function setWidget(?Widget $widget): self
    {
        $this->widget = $widget;

        return $this;
    }

    public function getBrand(): ?Brand
    {
        return $this->brand;
    }

    public function setBrand(?Brand $brand): self
    {
        $this->brand = $brand;

        return $this;
    }

    public function getCountry(): ?Country
    {
        return $this->country;
    }

    public function setCountry(?Country $country): self
    {
        $this->country = $country;

        return $this;
    }

    public function getSource(): ?string
    {
        return $this->source;
    }

    public function setSource(string $source): self
    {
        $this->source = $source;

        return $this;
    }
}
