<?php

namespace App\Entity;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Attribute\Groups;
use GMP;
#[ORM\Entity(repositoryClass: 'App\Repository\RoleRepository')]
class Role
{
    const ACCESS_MODES = [
        'none' => 'none',
        'read' => 'R',
        'write' => 'W',
    ];

    const LOCAL_ADMIN_ROLES = ['Reader', 'Operations', 'Webmaster', 'Local Technical Administrator'];
    const BRAND_ADMIN_ROLES = ['Technical Administrator', 'Functional Administrator'];
    const GLOBAL_ADMIN_ROLES = ['Super Administrator'];

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer', name: 'id')]
    #[Groups(['package_export'])]
    private ?int $id = null;

    #[ORM\Column(type: 'string', length: 255, name: 'label', options: ['default' => ''])]
    #[Groups(['package_export'])]
    private ?string $label;

    #[ORM\ManyToMany(targetEntity: IdpRole::class, mappedBy: 'roles')]
    private Collection $idpRoles;

    #[ORM\OneToMany(mappedBy: 'role', targetEntity: RoleMenu::class, orphanRemoval: true, cascade: ['persist'])]
    private Collection $roleMenus;

    #[ORM\OneToMany(mappedBy: 'role', targetEntity: AppService::class)]
    private Collection $appServices;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $name = null;

    public function __construct()
    {
        $this->idpRoles = new ArrayCollection();
        $this->roleMenus = new ArrayCollection();
        $this->appServices = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function setLabel(string $label): self
    {
        $this->label = $label;

        return $this;
    }

    /**
     * @return Collection<int, IdpRole>
     */
    public function getIdpRoles(): Collection
    {
        return $this->idpRoles;
    }

    public function addIdpRole(IdpRole $idpRole): self
    {
        if (!$this->idpRoles->contains($idpRole)) {
            $this->idpRoles->add($idpRole);
            $idpRole->addRole($this);
        }

        return $this;
    }

    public function removeIdpRole(IdpRole $idpRole): self
    {
        if ($this->idpRoles->removeElement($idpRole)) {
            $idpRole->removeRole($this);
        }

        return $this;
    }

    public function __toString()
    {
        return $this->label;
    }

    /**
     * @return Collection<int, RoleMenu>
     */
    public function getRoleMenus(): Collection
    {
        return $this->roleMenus;
    }

    public function addRoleMenu(RoleMenu $roleMenu): self
    {
        if (!$this->roleMenus->contains($roleMenu)) {
            $this->roleMenus->add($roleMenu);
            $roleMenu->setRole($this);
        }

        return $this;
    }

    public function removeRoleMenu(RoleMenu $roleMenu): self
    {
        if ($this->roleMenus->removeElement($roleMenu)) {
            // set the owning side to null (unless already changed)
            if ($roleMenu->getRole() === $this) {
                $roleMenu->setRole(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, AppService>
     */
    public function getAppServices(): Collection
    {
        return $this->appServices;
    }

    public function addAppService(AppService $appService): self
    {
        if (!$this->appServices->contains($appService)) {
            $this->appServices->add($appService);
            $appService->setRole($this);
        }

        return $this;
    }

    public function removeAppService(AppService $appService): self
    {
        if ($this->appServices->removeElement($appService)) {
            // set the owning side to null (unless already changed)
            if ($appService->getRole() === $this) {
                $appService->setRole(null);
            }
        }

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }
}
