import org.testng.annotations.Test;
import java.io.File;
import java.util.concurrent.TimeUnit;
public class Feature_Setting{
    @Test(groups = {"Feature Setting"})
    public void Create_Feature() throws InterruptedException {
        Methods user = new Methods();
       user.login();
        Locators.Administration().click();
        TimeUnit.SECONDS.sleep(1);
        Locators.Feature_Setting().click();
        Locators.Create_Features_Btn().click();
        String Feature_Name="Automation Test 01";
        Locators.Feature_Setting_Name().sendKeys(Feature_Name);
        String relativePath = "src/Files/Automation_Feature_Setting_Test_01.json";
        String absolutePath = new File(relativePath).getAbsolutePath();
        Locators.Feature_Setting_File().sendKeys(absolutePath);
        Locators.Feature_Setting_View_Btn().click();
        TimeUnit.SECONDS.sleep(1);
        Locators.Features_View_Close_Btn().click();
        Locators.Features_Admin_Mode().click();
        Locators.Features_Setting_Save().click();
//        Locators.Features_Back_Btn().click();
        user.isTextPresent(Locators.Features_Last_Row(),Feature_Name);
        Locators.Features().click();
        user.Scroll_Down();
        TimeUnit.SECONDS.sleep(1);
        Locators.Global_Features().click();
        user.Scroll_Down();
        TimeUnit.SECONDS.sleep(1);
        user.Scroll_Down();
        Locators.Global_Features_Last().click();
        user.isTextPresent(Locators.Features_Title(),Feature_Name);
        user.Scroll_Up();
        Locators.Global_Features().click();
        user.Scroll_Up();
        user.Refresh_Page();
        Locators.Administration().click();
        TimeUnit.SECONDS.sleep(1);
        Locators.Feature_Setting().click();
        Locators.Features_View().click();
        TimeUnit.SECONDS.sleep(1);
        Locators.Features_View_Close_Btn().click();
        Locators.Features_Edit().click();
        String Edit_Feature_Name="Edit Check for Automation Test 01";
        Locators.Feature_Setting_Name().clear();
        Locators.Feature_Setting_Name().sendKeys(Edit_Feature_Name);
        Locators.Features_Setting_Save().click();
        Locators.Features_Delete().click();
        TimeUnit.SECONDS.sleep(1);
        Locators.Features_Confirm_Delete().click();
       user.exit();
    }
}
