<?php

namespace App\Entity;

use App\Repository\MediaDirectoryRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use App\Validator\MediaDirectory as MediaDirectoryConstraint;

#[ORM\Entity(repositoryClass: MediaDirectoryRepository::class)]
#[MediaDirectoryConstraint]
class MediaDirectory
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\ManyToOne(targetEntity: Site::class)]
    #[ORM\JoinColumn(nullable: true)]
    private ?Site $site;

    #[Assert\NotBlank]
    #[ORM\Column(type: 'string', length: 100)]
    private string $label;

    #[ORM\Column(type: 'string', length: 100)]
    private string $path;

    #[ORM\Column(type: 'boolean')]
    private bool $readOnly = false;

    #[ORM\ManyToOne(targetEntity: MediaDirectory::class, inversedBy: 'childDirectories')]
    private ?MediaDirectory $parentDirectory;

    #[ORM\OneToMany(targetEntity: MediaDirectory::class, mappedBy: 'parentDirectory')]
    private Collection $childDirectories;

    #[ORM\Column(type: 'string', length: 2, nullable: true)]
    private ?string $brand;

    #[ORM\OneToMany(targetEntity: Media::class, mappedBy: 'directory')]
    private Collection $medias;

    #[ORM\ManyToOne(inversedBy: 'mediaDirectories', cascade: ['persist'])]
    private ?Channel $channel = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $name = null;

    public function __construct()
    {
        $this->childDirectories = new ArrayCollection();
        $this->medias = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getSite(): ?Site
    {
        return $this->site;
    }

    public function setSite(?Site $site): self
    {
        $this->site = $site;

        return $this;
    }

    public function getLabel(): ?string
    {
        //return $this->label;
        return $this->name;
    }

    public function setLabel(string $label): self
    {
        $this->label = $label;

        return $this;
    }

    public function getPath(): ?string
    {
        return $this->path;
    }

    public function setPath(string $path): self
    {
        $this->path = $path;

        return $this;
    }

    public function getReadOnly(): ?bool
    {
        return $this->readOnly;
    }

    public function setReadOnly(bool $readOnly): self
    {
        $this->readOnly = $readOnly;

        return $this;
    }

    public function getParentDirectory(): ?self
    {
        return $this->parentDirectory;
    }

    public function setParentDirectory(?self $parentDirectory): self
    {
        $this->parentDirectory = $parentDirectory;

        return $this;
    }

    /**
     * @return Collection<int, MediaDirectory>
     */
    public function getChildDirectories(): Collection
    {
        return $this->childDirectories;
    }
 
    public function setChildDirectories(Collection $childDirectories): self
    {
        $this->childDirectories = $childDirectories;
        return $this;
    }

    public function addChildDirectory(self $childDirectory): self
    {
        if (!$this->childDirectories->contains($childDirectory)) {
            $this->childDirectories[] = $childDirectory;
            $childDirectory->setParentDirectory($this);
        }
        return $this;
    }

    public function removeChildDirectory(self $childDirectory): self
    {
        if ($this->childDirectories->removeElement($childDirectory)) {
            // set the owning side to null (unless already changed)
            if ($childDirectory->getParentDirectory() === $this) {
                $childDirectory->setParentDirectory(null);
            }
        }
        return $this;
    }

    public function getBrand(): ?string
    {
        return $this->brand;
    }

    public function setBrand(?string $brand): self
    {
        $this->brand = $brand;

        return $this;
    }

    /**
     * @return Collection<int, Media>
     */
    public function getMedias(): Collection
    {
        return $this->medias;
    }

    public function addMedia(Media $media): self
    {
        if (!$this->medias->contains($media)) {
            $this->medias[] = $media;
            $media->setDirectory($this);
        }
        return $this;
    }

    public function removeMedia(Media $media): self
    {
        if ($this->medias->removeElement($media)) {
            // set the owning side to null (unless already changed)
            if ($media->getDirectory() === $this) {
                $media->setDirectory(null);
            }
        }
        return $this;
    }

    public function getChannel(): ?Channel
    {
        return $this->channel;
    }

    public function setChannel(?Channel $channel): self
    {
        $this->channel = $channel;

        return $this;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(?string $name): self
    {
        $this->name = $name;

        return $this;
    }
}
