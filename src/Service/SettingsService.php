<?php

namespace App\Service;

use App\Helpers\WSResponse;
use App\Security\User;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use App\Model\SettingsModel;

/**
 * SettingsService
 */
class SettingsService
{
	const SETTINGS_COLLECTION = 'settings';
	const SETTINGS_FILENAME_S3 = 'settings.json';

	function __construct(
		private MongoAtlasQueryService $mongoQueryService,
		private NormalizerInterface $normalizer,
		private string $settingsBucket,
		private AWSS3Service $s3Service,
		private string $baseUrl
	) {
	}

	/**
	 * @param array $settingsModels
	 * @return WSResponse
	 */
	public function insertSettings($user, array $settingsModels): WSResponse
	{
		foreach ($settingsModels as $settingModel) {
			$settingsData = $settingModel->getSettingsData();
			$settingsData = $this->getFormatedFeatureParameters($settingsData);
			if ($settingModel->getSource() == 'APP') {
				//$settingsData = $this->transformSettingsToAppFormat($settingsData);
			}
			$settingModel->setSettingsData($settingsData);
			$this->saveJsonSettings($user, $settingModel);
		}
		$settings = $this->normalizer->normalize($settingsModels);
		return $this->mongoQueryService->updateAndInsertMany(
			self::SETTINGS_COLLECTION,
			['brand', 'culture', 'source'],
			$settings,
			true
		);
	}

	/**
	 * send json file to S3
	 * @param SettingsModel $settingsModel
	 * @return bool
	 */
	public function saveJsonSettings(User $user, SettingsModel $settingsModel): bool
	{
		$fileName=$this->getFileName($user, $settingsModel);
		$settingsData = $settingsModel->getSettingsData();
		if (empty($settingsData)){
			$settingsData = new \stdClass();
		}
		$jsonContent = json_encode($settingsData, JSON_UNESCAPED_SLASHES);
		return $this->s3Service->putObject($fileName, $jsonContent, $this->settingsBucket);
	}

	public function getFileName(User $user, SettingsModel $settingsModel): string
	{
		if ($user->getProfile()->isSuperAdministrator()) {
			return strtolower($settingsModel->getSource()) . '/configuration/' . self::SETTINGS_FILENAME_S3;
		} else {
			$sourcePath = $settingsModel->getSource() . '/' . $settingsModel->getBrand();

			if ($settingsModel->getCulture()) {
				return strtolower($sourcePath) . '/' . $settingsModel->getCulture() . '/configuration/' . self::SETTINGS_FILENAME_S3;
			} else {
				return strtolower($sourcePath) . '/configuration/' . self::SETTINGS_FILENAME_S3;
			}
		}
	}

	public function getFormatedFeatureParameters($settingsData)
	{
		$formattedData = [];
		foreach ($settingsData as $key => $leaf) {
			$formatedKey = (new LowerCamelCaseService())->textToLowercamelcase($key);

			if (is_array($leaf)) {
				if (isset($leaf['enabled'])) {
					$leaf['enabled'] = (bool) $leaf['enabled'];
				}
				if (isset($leaf['isEarlyAdopters'])) {
					$leaf['isEarlyAdopters'] = (bool) $leaf['isEarlyAdopters'];
				}
				foreach ($leaf as $key2 => $value2) {
					if ($value2 == "false" || $value2 == "true") {
						$leaf[$key2] = $leaf[$key2];//filter_var($leaf[$key2], FILTER_VALIDATE_BOOLEAN);
					}
				}
				$formattedData[$formatedKey] = $this->getFormatedFeatureParameters($leaf);
			} else {
				$decodedValue = json_decode($leaf, true);
				if (is_array($decodedValue) && isset($decodedValue[0]['type']) && $decodedValue[0]['type'] == 'isMultifield') {
					$decodedValue = $this->removeKeys($decodedValue);
					$formattedData[$formatedKey] = $this->getFormatedFeatureParameters($decodedValue);
				}else{
					$formattedData[$formatedKey] = $leaf; 
				}
			}
		}

		return $formattedData;
	}

	private function removeKeys(?array $lists = [])
	{
		for ($i = 0; $i <= count($lists); $i++) {
			unset($lists[$i]['image_input']);
			unset($lists[$i]['type']);
		}
		return array_values($this->removeAllNullValues($lists));
	}

	private function removeAllNullValues(?array $array = []): ?array
	{
		return array_filter($array, function ($item) {
			if (is_array($item)) {
				foreach ($item as $value) {
					if (!is_null($value)) {
						return true;
					}
				}
				return false;
			}
		}) ?? [];
	}

	public function getUploadedJson(string $fileUrl): string
    {
        $fileUrl = $this->baseUrl . '/' . $fileUrl;
        try {
            $jsonContents = file_get_contents($fileUrl);
        } catch (\Exception $e) {
            $jsonContents = '{"Error":"Unable to download/find json file"}';
        }
        $prettyJson = json_encode(json_decode($jsonContents), JSON_PRETTY_PRINT);
        return $prettyJson;
    }

	private function transformSettingsToAppFormat(array $settingsData) {
		$settings = [];
		$i = 0;
		foreach ($settingsData as $key => $setting) {
			$settings[$i]['type'] = $key;
			$settings[$i]['enabled'] = $setting['enabled'] ?? false;
			unset($setting['enabled'], $setting['isEarlyAdopters']);
			if(!(count($setting) == 1 && isset($setting['code']))) {
				$settings[$i]['config'] = $setting;
			}
			$i++;
		}
		return $settings;
	}
}