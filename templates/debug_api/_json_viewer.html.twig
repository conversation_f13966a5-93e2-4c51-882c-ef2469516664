{# Primary JSON Viewer Component #}
<div class="card mt-4 response-card" id="primary-json-viewer" {% if not jsonData %}style="display: none;"{% endif %}>
    <div class="card-header response-header bg-json-primary text-black">
        <div class="d-flex justify-content-between align-items-center">
            <div class="response-title">
                <i class="fas fa-code mr-2"></i>
                <span class="response-label">JSON Response</span>
            </div>
            <div class="response-controls">
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-light" id="expand-all" title="Expand All">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </button>
                    <button type="button" class="btn btn-outline-light" id="collapse-all" title="Collapse All">
                        <i class="fas fa-compress-arrows-alt"></i>
                    </button>
                    <button type="button" class="btn btn-outline-light" id="copy-json" title="Copy JSON">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="card-body response-body p-0">
        <!-- Response Metadata -->
        <div class="response-metadata p-3 border-bottom bg-light">
            <div class="row">
                <div class="col-md-3">
                    <div class="metadata-item d-flex flex-column align-items-center">
                        <div class="metadata-label">
                            <i class="fas fa-info-circle m-1"></i>Status
                        </div>
                        <div class="metadata-value status-success" id="metadata-status">
                            <i class="fas fa-check-circle m-1"></i>200 OK
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metadata-item d-flex flex-column align-items-center">
                        <div class="metadata-label">
                            <i class="fas fa-file-code m-1"></i>Content-Type
                        </div>
                        <div class="metadata-value" id="metadata-content-type">application/json</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metadata-item d-flex flex-column align-items-center">
                        <div class="metadata-label">
                            <i class="fas fa-weight m-1"></i>Size
                        </div>
                        <div class="metadata-value" id="response-size">-</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metadata-item d-flex flex-column align-items-center">
                        <div class="metadata-label">
                            <i class="fas fa-database m-1"></i>Documents
                        </div>
                        <div class="metadata-value" id="doc-count">-</div>
                    </div>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-3">
                    <div class="metadata-item d-flex flex-column align-items-center">
                        <div class="metadata-label">
                            <i class="fas fa-car m-1"></i>Vehicles
                        </div>
                        <div class="metadata-value" id="vehicle-count">-</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metadata-item d-flex flex-column align-items-center">
                        <div class="metadata-label">
                            <i class="fas fa-cubes m-1"></i>Objects
                        </div>
                        <div class="metadata-value" id="total-objects">-</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metadata-item d-flex flex-column align-items-center">
                        <div class="metadata-label">
                            <i class="fas fa-clock m-1"></i>Timestamp
                        </div>
                        <div class="metadata-value" id="response-timestamp">-</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="metadata-item d-flex flex-column align-items-center">
                        <div class="metadata-label">
                            <i class="fas fa-stopwatch m-1"></i>Response Time
                        </div>
                        <div class="metadata-value" id="response-time">-</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- JSON Viewer -->
        <div class="json-output-section p-3">
            <div class="json-output-container json-viewer-content">
                <json-viewer id="json-renderer"></json-viewer>
            </div>
        </div>
    </div>
</div>

<style>
.bg-json-primary {
			background: linear-gradient(135deg, #47faf5 0%, #003d3b 100%);
		}
</style>