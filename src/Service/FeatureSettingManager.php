<?php

namespace App\Service;

use App\Entity\Brand;
use App\Entity\Country;
use Doctrine\ORM\EntityManagerInterface;
use App\Entity\FeatureSetting;
use App\Entity\Menu;
use App\Entity\Role;
use App\Repository\BrandRepository;
use App\Repository\MenuRepository;
use App\Repository\RoleRepository;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;

/**
 * Feature Setting
 */
class FeatureSettingManager
{
	const GLOBAL_FEATURES_MENU = 'global_features';
	const LOCAL_FEATURES_MENU = 'local_features';
	const BRAND_FEATURES_MENU = 'brand_features';

	const BRAND_LEVEL = 'Brand';
	const GLOBAL_LEVEL = 'Global';
	const LOCAL_LEVEL = 'Local';

	const FEATURE_SETTING_LEVELS = [
		self::GLOBAL_LEVEL,
		self::BRAND_LEVEL,
		self::LOCAL_LEVEL
	];

	private Request $request;

	public function __construct(
		private RequestStack $requestStack,
		private EntityManagerInterface $em,
		private ProfileService $profileService,
		private MenuRepository $menuRepository,
		private BrandRepository $brandRepository,
		private RoleRepository $roleRepository
	) {
		$this->request = $requestStack->getCurrentRequest();
	}

	public function saveFeatureSetting(UploadedFile $file, FeatureSetting $featureSetting, Brand $brand, ?Country $country): bool
	{
		$filename = $this->handleFileUpload($file, $featureSetting, $brand, $country);
		$this->em->persist($featureSetting);

		$content = file_get_contents($file);
		$content = json_decode($content, true);
		$levels = $content['form']['level'] ?? [];
		if ($this->isFileNamesValide($content)) {
			$this->processLevels($levels, $featureSetting, $brand, $country, $filename);
			$this->em->flush();
			return true;
		}

		return false;
	}

	public function editFeatureSetting(?UploadedFile $file, FeatureSetting $featureSetting, Brand $brand, ?Country $country): bool
	{
		if ($file) {
			$this->handleFileUpload($file, $featureSetting, $brand, $country);
		}

		// Persist the FeatureSetting entity
		$this->em->persist($featureSetting);
		$menus = $featureSetting->getMenus();
		$this->updateMenusForFeatureSetting($menus, $featureSetting);

		$this->em->flush();

		return true;
	}

	private function handleFileUpload(UploadedFile $file, FeatureSetting $featureSetting, Brand $brand, ?Country $country): string
	{
		$extension = pathinfo($file->getClientOriginalName(), PATHINFO_EXTENSION);
		$filename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME) . '_' . uniqid() . '.' . $extension;
		$content = file_get_contents($file);
		$content = json_decode($content, true);
		$featureSetting->setBrand($brand)->setCountry($country)->setFile($filename)->setContent($content);
		return $filename;
	}

	private function processLevels(array $levels, FeatureSetting $featureSetting, Brand $brand, ?Country $country, ?string $filename): void
	{
		$this->configureFeatureSetting($featureSetting, $filename);
		foreach ($levels as $level) {
			$menu = $this->createMenuForFeatureSetting($featureSetting, $level);
			$this->setFeatureAccessesByLevel($menu, $level);
		}
		$this->em->flush();
	}

	private function configureFeatureSetting(FeatureSetting $featureSetting, ?string $filename): void
	{
		$featureSetting
			->setBrand(null)
			->setCountry(null)
			->setFile($filename);

		$this->em->persist($featureSetting);
	}

	private function createMenuForFeatureSetting(FeatureSetting $featureSetting, string $level): Menu
	{
		$menu = new Menu();
		$menu->setParent($this->getFeatureMenuByLevel($level))
			->setLabel($featureSetting->getName())
			->setRouteName('feature')
			->setParameters("{\"id\":" . $featureSetting->getId() . "}")
			->setFeature($featureSetting);

		$featureSetting->addMenu($menu);

		$this->em->persist($menu);


		return $menu;
	}
	//to edit 
	private function setFeatureAccessesByLevel(Menu $menu, string $level): void
	{
		if ($level === self::GLOBAL_LEVEL) {
			$role = $this->roleRepository->findOneBy(['label' => 'Super Administrator']);
			$this->profileService->grantRoleAccessToMenu($role, $menu, 'W');
		} else {
			if ($level === self::BRAND_LEVEL || $level === self::LOCAL_LEVEL) {
				$roleType = ($level === self::BRAND_LEVEL) ? Role::BRAND_ADMIN_ROLES[0] : Role::LOCAL_ADMIN_ROLES[0];

				$roles = $this->roleRepository->findAllByRole($roleType);
				foreach ($roles as $role) {
					$this->profileService->grantRoleAccessToMenu($role, $menu, 'none');
				}
			}
		}
	}

	public function removeFeatureSetting(FeatureSetting $featureSetting): bool
	{
		try {
			$this->em->remove($featureSetting);
			$this->em->flush();

			return true;
		} catch (\Exception $e) {

			return false;
		}
	}

	public function isFileNamesValide(array $content): bool
	{
		if (isset($content['form']['fields'])) {
			foreach ($content['form']['fields'] as $field) {
				if (isset($field['name'])) {
					$name = $field['name'];
					if (!preg_match('/^[a-zA-Z ]+$/', $name)) {
						$this->request->getSession()->getFlashBag()->add("warning", "The Name $name field contains invalid characters. Only a-z, A-Z, and space are allowed.");
						return false;
					}
				}
			}
		}

		return true;
	}

	public function getFeatureMenuByLevel(string $level): Menu
	{
		if (self::GLOBAL_LEVEL === $level) {
			return $this->menuRepository->findOneBy(['label' => self::GLOBAL_FEATURES_MENU]);
		}

		if (self::LOCAL_LEVEL === $level) {
			return $this->menuRepository->findOneBy(['label' => self::LOCAL_FEATURES_MENU]);
		}

		return $this->menuRepository->findOneBy(['label' => self::BRAND_FEATURES_MENU]);
	}

	public function findFeatureSettingByName(string $name): ?FeatureSetting
	{
		return $this->em->getRepository(FeatureSetting::class)->findOneBy(['name' => $name]);
	}

	private function updateMenusForFeatureSetting(iterable $menus, FeatureSetting $featureSetting): void
	{
		foreach ($menus as $menu) {
			$menu
				->setLabel($featureSetting->getName())
				->setRouteName('feature')
				->setParameters(json_encode(['id' => $featureSetting->getId()]))
				->setFeature($featureSetting);

			$this->em->persist($menu);
		}
	}
}
