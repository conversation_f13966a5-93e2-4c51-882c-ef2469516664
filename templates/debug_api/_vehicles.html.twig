{# Enhanced Vehicles Display Component with Tabs #}
{% if mongoData and mongoData.documents is not empty %}
	<div class="card mt-4 vehicles-container shadow-sm">
		<div class="card-header vehicles-header bg-primary text-white">
			<div class="d-flex justify-content-between align-items-center">
				<div>
					<h5 class="mb-1">
						<i class="fas fa-database me-2"></i>MongoDB Documents ({{ mongoData.documents|length }})
					</h5>
					<small class="header-subtitle opacity-75">Vehicle information with copy functionality</small>
				</div>
			</div>
		</div>

		<div
			class="card-body p-0">
			<!-- Navigation Tabs -->
			<ul class="nav nav-tabs d-flex" id="vehicleDocumentTabs" role="tablist">
				{% for document in mongoData.documents %}
					{% if document.vehicle is defined and document.vehicle is not empty %}
						<li class="nav-item flex-fill" role="presentation">
							<button class="nav-link {% if loop.first %}active{% endif %} d-flex align-items-center justify-content-center h-100 w-100 border text-dark" id="tab-{{ loop.index0 }}-tab" data-bs-toggle="tab" data-bs-target="#tab-{{ loop.index0 }}" type="button" role="tab" aria-controls="tab-{{ loop.index0 }}" aria-selected="{% if loop.first %}true{% else %}false{% endif %}">
								<i class="fas fa-user-circle me-2 text-dark"></i>
								<div class="d-flex flex-column align-items-center text-center">
									<span class="fw-bold text-dark">{{ document.profile.firstName|default('Unknown') }}
										{{ document.profile.lastName|default('User') }}</span>
								</div>
							</button>
						</li>
					{% endif %}
				{% endfor %}
			</ul>

			<!-- Tab Content -->
			<div class="tab-content" id="vehicleDocumentTabsContent">
				{% for document in mongoData.documents %}
					{% if document.vehicle is defined and document.vehicle is not empty %}
						<div
							class="tab-pane fade {% if loop.first %}show active{% endif %}" id="tab-{{ loop.index0 }}" role="tabpanel" aria-labelledby="tab-{{ loop.index0 }}-tab">

							<!-- User Info Header -->
							<div class="bg-light border-bottom p-2">
								<div class="mt-3">
									<div class="d-flex justify-content-center align-items-center fs-2">
										<i class="fas fa-user-circle me-2"></i>
										<div class="mb-1 text-primary">
											<span class="font-weight-bold">Selected User Information</span>
										</div>
									</div>
									<div class="d-flex justify-content-between align-items-center glass-card p-3">
										<div>
											<h6 class="mb-1">
                                                <i class="fas fa-user"></i>
												<span class="font-weight-bold">FirstName:</span>
												{{ document.profile.firstName|default('Unknown') }}
											</h6>
											<h6 class="mb-1">
                                                <i class="fas fa-user"></i>
												<span class="font-weight-bold">LastName:</span>
												{{ document.profile.lastName|default('Unknown') }}
											</h6>
											<h6 class="">
												<i class="fas fa-envelope me-1"></i>
												<span class="font-weight-bold">Email:</span>
												{{ document.profile.email|default('No email') }}
											</h6>
											<h6 class="">
												<i class="fas fa-id-card me-1"></i>
												<span class="font-weight-bold">UserID:</span>
												{{ document.userId }}
											</h6>
                                            <h6 class="">
												<i class="fas fa-database"></i>
												<span class="font-weight-bold">User DB ID:</span>
												{{ document.userDbId|default('No UserDB ID')  }}
											</h6>
										</div>
										<div class="d-flex gap-2 fs-4">
											<span class="badge bg-primary">{{ document.vehicle|length }}
												vehicle(s)</span>
											<span class="badge bg-secondary"><i class="fas fa-globe me-2"></i>{{ document.profile.country|default('Unknown') }}</span>
										</div>
									</div>
								</div>
							</div>

							<!-- Vehicles Grid -->
							<div class="p-3">
								<div class="row g-3">
									{% for vehicle in document.vehicle %}
										<div class="col-12 col-md-6 col-lg-4">
											<div
												class="card h-100 glass-card vehicle-card">
												<!-- Vehicle Header with Image -->
												<div class="card-header glass-header p-3">
													<div class="d-flex justify-content-between align-items-start mb-2">
														<div class="flex-grow-1">
															<h6 class="card-title mb-1 text-dark fw-bold">
																{{ vehicle.shortLabel|default(vehicle.modelDescription|default('Vehicle')) }}
															</h6>
															<small class="text-muted">{{ vehicle.type|default('Unknown Type') }}</small>
														</div>
														<span class="badge glass-badge px-2 py-1">
															{{ vehicle.brand|default('N/A') }}
														</span>
													</div>
													{% if vehicle.picture is defined and vehicle.picture %}
														<div class="text-center">
															<img src="{{ vehicle.picture }}" alt="Vehicle" class="img-fluid rounded glass-image" style="max-height: 120px; object-fit: cover;">
														</div>
													{% endif %}
												</div>

												<!-- Vehicle Details -->
												<div
													class="card-body p-3">
													<!-- User ID -->
													<div class="detail-row mb-3">
														<div class="d-flex justify-content-between align-items-center">
															<div class="d-flex align-items-center">
																<i class="fas fa-user text-primary me-2"></i>
																<span class="fw-medium">User ID</span>
															</div>
															<div class="d-flex align-items-center">
																<div class="bg-light px-2 py-1 rounded me-2 user-select-all" id="userId-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}">
																	{{ document.userId|default('N/A') }}
																</div>
																<button class="btn btn-outline-primary btn-sm border-0" onclick="copyToClipboard('userId-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}', this)" title="Copy User ID">
																	<i class="fas fa-copy"></i>
																</button>
															</div>
														</div>
													</div>

													<!-- Brand -->
													<div class="detail-row mb-3">
														<div class="d-flex justify-content-between align-items-center">
															<div class="d-flex align-items-center">
																<i class="fas fa-tag text-success me-2"></i>
																<span class="fw-medium">Brand</span>
															</div>
															<div class="d-flex align-items-center">
																<div class="bg-light px-2 py-1 rounded me-2 user-select-all" id="brand-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}">
																	{{ vehicle.brand|default('N/A') }}
																</div>
																<button class="btn btn-outline-success btn-sm border-0" onclick="copyToClipboard('brand-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}', this)" title="Copy Brand">
																	<i class="fas fa-copy"></i>
																</button>
															</div>
														</div>
													</div>

													<!-- Country -->
													<div class="detail-row mb-3">
														<div class="d-flex justify-content-between align-items-center">
															<div class="d-flex align-items-center">
																<i class="fas fa-globe text-info me-2"></i>
																<span class="fw-medium">Country</span>
															</div>
															<div class="d-flex align-items-center">
																<div class="bg-light px-2 py-1 rounded me-2 user-select-all" id="country-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}">
																	{{ vehicle.country|default(document.profile.country|default('N/A')) }}
																</div>
																<button class="btn btn-outline-info btn-sm border-0" onclick="copyToClipboard('country-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}', this)" title="Copy Country">
																	<i class="fas fa-copy"></i>
																</button>
															</div>
														</div>
													</div>

													<!-- VIN -->
													<div class="detail-row mb-3">
														<div class="d-flex justify-content-between align-items-center">
															<div class="d-flex align-items-center">
																<i class="fas fa-barcode text-warning me-2"></i>
																<span class="fw-medium">VIN</span>
															</div>
															<div class="d-flex align-items-center">
																<div class="bg-light px-2 py-1 rounded me-2 user-select-all text-truncate" id="vin-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}">
																	{{ vehicle.vin|default('N/A') }}
																</div>
																<button class="btn btn-outline-warning btn-sm border-0" onclick="copyToClipboard('vin-{{ loop.parent.loop.index0 }}-{{ loop.index0 }}', this)" title="Copy VIN">
																	<i class="fas fa-copy"></i>
																</button>
															</div>
														</div>
													</div>
												</div>

												<!-- Vehicle Footer -->
												<div class="card-footer glass-footer">
													<div class="d-flex justify-content-between align-items-center">
														<small class="text-muted d-flex align-items-center">
															{% if vehicle.mileage is defined and vehicle.mileage and vehicle.mileage.value is defined %}
																<i class="fas fa-tachometer-alt me-1 text-primary"></i>
																{{ vehicle.mileage.value }}
																km
															{% else %}
																<i class="fas fa-info-circle me-1 text-secondary"></i>
																No mileage data
															{% endif %}
														</small>
														<small class="text-muted d-flex align-items-center">
															{% if vehicle.featureCode is defined and vehicle.featureCode %}
																<i class="fas fa-cogs me-1 text-success"></i>
																{{ vehicle.featureCode|length }}
																features
															{% else %}
																<i class="fas fa-minus-circle me-1 text-secondary"></i>
																No features
															{% endif %}
														</small>
													</div>
												</div>
											</div>
										</div>
									{% endfor %}
								</div>
							</div>
						</div>
					{% endif %}
				{% endfor %}
			</div>
		</div>
	</div>

	<style>
		/* Custom CSS for enhanced styling */
		.bg-gradient-primary {
			background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
		}

		/* Glass Card Effect */
		.glass-card {
			background: rgba(255, 255, 255, 0.25);
			backdrop-filter: blur(10px);
			-webkit-backdrop-filter: blur(10px);
			border: 1px solid rgba(255, 255, 255, 0.3);
			border-radius: 16px;
			box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
			transition: all 0.3s ease;
		}

		.glass-header {
			background: rgba(255, 255, 255, 0.4);
			backdrop-filter: blur(15px);
			-webkit-backdrop-filter: blur(15px);
			border: none;
			border-bottom: 1px solid rgba(255, 255, 255, 0.2);
			border-radius: 16px 16px 0 0;
		}

		.glass-footer {
			background: rgba(255, 255, 255, 0.3);
			backdrop-filter: blur(15px);
			-webkit-backdrop-filter: blur(15px);
			border: none;
			border-top: 1px solid rgba(255, 255, 255, 0.2);
			border-radius: 0 0 16px 16px;
		}

		.glass-badge {
			background: rgba(255, 255, 255, 0.7);
			backdrop-filter: blur(10px);
			-webkit-backdrop-filter: blur(10px);
			border: 1px solid rgba(255, 255, 255, 0.5);
			color: #495057;
			font-weight: 600;
			border-radius: 12px;
		}

		.glass-image {
			border: 2px solid rgba(255, 255, 255, 0.3);
			backdrop-filter: blur(5px);
			-webkit-backdrop-filter: blur(5px);
		}

		.vehicle-card {
			transition: transform 0.3s ease, box-shadow 0.3s ease;
		}

		.vehicle-card:hover {
			transform: translateY(-8px);
			box-shadow: 0 16px 40px rgba(0, 0, 0, 0.15);
		}

		.vehicle-card:hover .glass-card {
			background: rgba(255, 255, 255, 0.35);
			border-color: rgba(255, 255, 255, 0.4);
		}

		.detail-row {
			border-bottom: 1px solid rgba(255, 255, 255, 0.2);
			padding-bottom: 8px;
		}

		.detail-row:last-child {
			border-bottom: none;
		}

		/* Enhanced glass effect for detail values */
		.detail-row .bg-light {
			background: rgba(255, 255, 255, 0.6) !important;
			backdrop-filter: blur(8px);
			-webkit-backdrop-filter: blur(8px);
			border: 1px solid rgba(255, 255, 255, 0.3);
		}

		.nav-tabs {
			border-bottom: 1px solid #dee2e6;
		}

		.tab-item {
			min-width: 0;
			flex-grow: 1;
		}

		.nav-tabs .nav-link {
			border: 1px solid #dee2e6;
			border-bottom: 3px solid transparent;
			transition: all 0.3s ease;
			color: #000 !important;
			min-height: 80px;
			margin: 0;
			border-radius: 0;
		}

		.nav-tabs .nav-link:hover {
			border-bottom-color: #007bff;
			background-color: #f8f9fa;
			color: #000 !important;
		}

		.nav-tabs .nav-link.active {
			border-bottom-color: #007bff;
			background-color: #fff;
			font-weight: 600;
			color: #000 !important;
			border-color: #007bff;
		}

		.btn-sm {
			padding: 0.25rem 0.5rem;
			font-size: 0.75rem;
		}

		code {
			font-size: 0.85em;
			max-width: 150px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			display: inline-block;
		}

		/* Responsive adjustments */
		@media(max-width: 768px) {
			.nav-tabs .nav-link {
				padding: 0.75rem 0.5rem;
				font-size: 0.9rem;
			}

			.detail-row {
				flex-direction: column;
				align-items: flex-start !important;
			}

			.detail-row .d-flex:last-child {
				margin-top: 0.5rem;
				width: 100%;
				justify-content: flex-end;
			}
		}

		/* Copy button hover effects */
		.btn-outline-primary:hover,
		.btn-outline-success:hover,
		.btn-outline-info:hover,
		.btn-outline-warning:hover {
			transform: scale(1.05);
		}

		.vehicles-container {
			border: none;
			box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
		}

		.card-header.vehicles-header {
			border: none;
			background: linear-gradient(135deg, #00641e 0%, #1cd553 100%);
		}
	</style>
{% endif %}
