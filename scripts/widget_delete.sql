/**
* Create a sql script to completely remove a widget with a give name from a db instance
*
**/


-- Define the widget name as a session variable
SET @widget_name = 'space-app-subscription-widget' COLLATE utf8mb4_unicode_ci;

/*
SELECT wfa.*
FROM widget w
INNER JOIN widget_feature wf ON w.id = wf.widget_id
INNER JOIN widget_feature_attribute wfa ON wf.id = wfa.widget_feature_id
WHERE w.name = @widget_name ;


SELECT wf.*
FROM widget w
INNER JOIN widget_feature wf ON w.id = wf.widget_id
WHERE w.name = @widget_name ;


SELECT wd.* 
FROM widget w
INNER JOIN widget_data wd ON w.id = wd.widget_id
WHERE w.name = @widget_name ;


SELECT rt.*
FROM reference_translation rt
INNER JOIN translation_key tk ON tk.id = rt.translation_key_id
INNER JOIN widget w ON w.id = tk.widget_id
WHERE w.name = @widget_name ;


SELECT lt.*
FROM local_translation lt
INNER JOIN translation_key tk ON tk.id = lt.translation_key_id
INNER JOIN widget w ON w.id = tk.widget_id
WHERE w.name = @widget_name ;


SELECT tk.*
FROM translation_key tk
INNER JOIN widget w ON w.id = tk.widget_id
WHERE w.name = @widget_name ;


SELECT w.* 
FROM widget w
WHERE w.name = @widget_name ;

*/



DELETE wfa
FROM widget w
INNER JOIN widget_feature wf ON w.id = wf.widget_id
INNER JOIN widget_feature_attribute wfa ON wf.id = wfa.widget_feature_id
WHERE w.name = @widget_name ;


DELETE wf
FROM widget w
INNER JOIN widget_feature wf ON w.id = wf.widget_id
WHERE w.name = @widget_name ;

DELETE wd 
FROM widget w
INNER JOIN widget_data wd ON w.id = wd.widget_id
WHERE w.name = @widget_name ;

DELETE rt
FROM reference_translation rt
INNER JOIN translation_key tk ON tk.id = rt.translation_key_id
INNER JOIN widget w ON w.id = tk.widget_id
WHERE w.name = @widget_name ;

DELETE lt
FROM local_translation lt
INNER JOIN translation_key tk ON tk.id = lt.translation_key_id
INNER JOIN widget w ON w.id = tk.widget_id
WHERE w.name = @widget_name ;

DELETE tk
FROM translation_key tk
INNER JOIN widget w ON w.id = tk.widget_id
WHERE w.name = @widget_name ;

DELETE w
FROM widget w
WHERE w.name = @widget_name ;







