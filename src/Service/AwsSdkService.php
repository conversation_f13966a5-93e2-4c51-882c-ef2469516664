<?php

namespace App\Service;

use Aws\CloudFront\CloudFrontClient;
use Aws\Credentials\Credentials;
use Aws\S3\S3Client;
use Aws\Sdk;
use Aws\Sts\StsClient;

class AwsSdkService
{
    private S3Client $s3Client;

    private Credentials $credentials;

    private array $account;

    private CloudFrontClient $cloudFrontClient;

    /**
     * AwsSdkService constructor.
     */
    public function __construct(array $account, Credentials $credentials)
    {
        $this->account = $account;
        $this->credentials = $credentials;
    }

    public function getS3Client(): S3Client
    {
        if ($this->account['used_iam_instance_profile']) {
            
            
            $this->s3Client = new S3Client([
                'version' => $this->account['version'],
                'region' => $this->account['region'],
                // 'use_path_style_endpoint' => true,
            ]);
            
            return $this->s3Client;
        }

        $stsClient = new StsClient([
            'credentials' => $this->credentials,
            'region' => $this->account['region'],
            'version' => $this->account['version'],
        ]);
        $assumeRole = $stsClient->assumeRole([
            'RoleArn' => $this->account['role_arn'],
            'RoleSessionName' => 's3-access',
        ]);
        $sdk = new Sdk([
            'region' => $this->account['region'],
            'version' => $this->account['version'],
        ]);

        $this->s3Client = $sdk->createS3([
            'region' => $this->account['region'],
            'version' => $this->account['version'],
            'credentials' => [
                'key' => $assumeRole['Credentials']['AccessKeyId'],
                'secret' => $assumeRole['Credentials']['SecretAccessKey'],
                'token' => $assumeRole['Credentials']['SessionToken'],
            ],
            // 'use_path_style_endpoint' => true,
        ]);

        return $this->s3Client;
    }

    public function getCloudFrontClient(){
        if ($this->account['used_iam_instance_profile']) {
            $this->cloudFrontClient = new CloudFrontClient([
                'version' => $this->account['version'],
                'region' => $this->account['region'],
            ]);
            
            return $this->cloudFrontClient;
        }
        $this->cloudFrontClient = new CloudFrontClient([
            'version' => $this->account['version'],
            'region' => $this->account['region'],
            'credentials' => $this->credentials
        ]);

        return  $this->cloudFrontClient;
    }
}
