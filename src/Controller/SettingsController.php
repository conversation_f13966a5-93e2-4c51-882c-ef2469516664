<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Attribute\Route;
use App\Form\SettingsFormType;
use App\Helpers\LoggerTrait;
use App\Repository\ChannelRepository;
use App\Service\SettingsManager;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Security\Http\Attribute\CurrentUser;
use Symfony\Contracts\Translation\TranslatorInterface as TranslationTranslatorInterface;

#[Route(path: '/settings', name:'settings_')]
class SettingsController extends AbstractController
{
    use LoggerTrait;

    #[Route(path: '/sync', name: 'save')]
    public function saveSettings(
        #[CurrentUser] $user, 
        Request $request, 
        SettingsManager $settingsManager,
        TranslationTranslatorInterface $translator,
        ChannelRepository $channelRepository,
        LoggerInterface $logger
    ): Response|RedirectResponse
    {
        $form = $this->createForm(SettingsFormType::class);
        $channelNames = $channelRepository->getChannelMap();

        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid() && !$request->isXmlHttpRequest()) {
            $data = $form->getData();

            $inserted = $settingsManager->saveSettings($user, $data['source']);
            if (isset($inserted) && $inserted->getCode() == Response::HTTP_OK) {
                $this->addFlash('success', $translator->trans('settings_msg'));
            } else {
                $this->addFlash('danger', $translator->trans('settings_failed_msg'));
                $logger->error(__METHOD__ . ' - Wrong reply on saving settings', ['response' => $inserted]);
            }
            return $this->redirectToRoute('settings_save');
        }

        return $this->render('settings/save.html.twig', [
            'form' => $form->createView(),
            'channels' => $channelNames,
            'userRole' => $user->getProfile()->getRole()->getLabel()
        ]);
    }

    #[Route(path: '/{channel}/view', name: 'view')]
    public function view(Request $request, SettingsManager $settingsManager, #[CurrentUser] $user): Response|RedirectResponse
    {
        $channel = $request->get('channel');
        $jsonPacket = $settingsManager->createPacket($user, [$channel]);
        
        if ($jsonPacket['code']==Response::HTTP_NO_CONTENT) {
            $this->addFlash('danger', 'Settings needs to be configured');
            return $this->redirectToRoute('settings_save');
        }

        return $this->render('settings/jsonViewer.html.twig', [
            'currentJson' => $jsonPacket['currentJson'],
            'releasedJson' => $jsonPacket['releasedJson'],
            'iconUrl' => $jsonPacket['iconUrl'],
            'filename' => 'settings.json',
            'diffJson' => $jsonPacket['diff'],
            'path' => $jsonPacket['path'],
        ]);
    }

    #[Route(path: '/back', name: 'back')]
    public function backToSettings(): RedirectResponse
    {
        return $this->redirectToRoute('settings_save');
    }
}