<?php

namespace App\Entity;

use App\Repository\LanguageRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Attribute\Groups;

#[ORM\Entity(repositoryClass: LanguageRepository::class)]
class Language
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'string', length: 2, nullable: true)]
    #[Groups(['package_export'])]
    private ?string $code = null;

    #[ORM\Column(type: 'string', length: 50, nullable: true)]
    #[Groups(['package_export'])]
    private ?string $label = null;

    #[ORM\Column(type: 'boolean', options: ['default' => false])]
    private bool $isReference = false;

    #[ORM\OneToMany(mappedBy: 'loginLanguage', targetEntity: UserPreference::class)]
    private Collection $userPreferences;

    #[ORM\ManyToMany(targetEntity: Site::class, mappedBy: "languages")]
    private Collection $sites;

    public function __construct()
    {
        $this->userPreferences = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(?string $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function setLabel(?string $label): self
    {
        $this->label = $label;

        return $this;
    }

    public function getIsReference(): bool
    {
        return $this->isReference;
    }

    public function setIsReference(bool $isReference): self
    {
        $this->isReference = $isReference;

        return $this;
    }

    /**
     * @return Collection<int, UserPreference>
     */
    public function getUserPreferences(): Collection
    {
        return $this->userPreferences;
    }

    public function addUserPreference(UserPreference $userPreference): self
    {
        if (!$this->userPreferences->contains($userPreference)) {
            $this->userPreferences->add($userPreference);
            $userPreference->setLoginLanguage($this);
        }

        return $this;
    }

    public function removeUserPreference(UserPreference $userPreference): self
    {
        if ($this->userPreferences->removeElement($userPreference)) {
            // set the owning side to null (unless already changed)
            if ($userPreference->getLoginLanguage() === $this) {
                $userPreference->setLoginLanguage(null);
            }
        }

        return $this;
    }

    public function __toString()
    {
        return $this->getCode() ?? '';
    }

    public function getSites(): Collection
    {
        return $this->sites;
    }

    public function addSite(Site $site): self
    {
        if (!$this->sites->contains($site)) {
            $this->sites[] = $site;
            $site->addLanguage($this);
        }

        return $this;
    }

    public function removeSite(Site $site): self
    {
        if ($this->sites->removeElement($site)) {
            $site->removeLanguage($this);
        }

        return $this;
    }
}
