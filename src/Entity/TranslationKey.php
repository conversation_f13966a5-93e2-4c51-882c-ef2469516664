<?php

namespace App\Entity;

use App\Repository\TranslationKeyRepository;
use DateTime;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Event\PrePersistEventArgs;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use App\Validator\Constraints as AppAssert;
use Symfony\Component\Serializer\Attribute\Groups;

#[ORM\Entity(repositoryClass: TranslationKeyRepository::class)]
#[ORM\HasLifecycleCallbacks()]
#[AppAssert\UniqueTranslationKey]
class TranslationKey
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    #[Assert\NotBlank(message: 'translation_key_label_key_notblank')]
    #[Assert\Regex(pattern: '/^[A-Za-z][A-Za-z0-9]*([_.][A-Za-z0-9]*)*$/u', message: 'translation_key_label_key_invalid')]
    #[Groups(['package_export'])]
    private ?string $labelKey = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    #[Groups(['package_export'])]
    private ?string $feature = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $channel = null;

    #[ORM\OneToMany(targetEntity: ReferenceTranslation::class, mappedBy: 'translationKey', cascade: ['persist'])]
    #[Groups(['package_export'])]
    private Collection $referenceTranslations;

    #[ORM\OneToMany(targetEntity: LocalTranslation::class, mappedBy: 'translationKey', cascade: ['persist'])]
    #[ORM\OrderBy(['language' => 'ASC'])]
    #[Groups(['package_export'])]
    private Collection $localTranslations;

    #[ORM\Column(type: 'datetime')]
    private DateTime $createdAt;

    #[ORM\Column(type: 'datetime')]
    private DateTime $updatedAt;

    #[ORM\Column(type: 'datetime', nullable: true)]
    private ?DateTime $releaseDate = null;

    #[ORM\ManyToOne(targetEntity: Brand::class)]
    #[ORM\JoinColumn(nullable: false)]
    #[Groups(['package_export'])]
    private Brand $brand;

    #[ORM\Column(type: 'integer', nullable: true)]
    private ?int $sprintNumber = null;

    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $parameterValue;

    #[ORM\ManyToOne(targetEntity: Widget::class, inversedBy: 'translationKeys')]
    #[ORM\JoinColumn(nullable: true)]
    #[Groups(['package_export'])]
    private ?Widget $widget = null;

    public function __construct()
    {
        $this->referenceTranslations = new ArrayCollection();
        $this->localTranslations = new ArrayCollection();
        $this->createdAt = new DateTime();
        $this->updatedAt = new DateTime();
        
    }

    /**
     * Clone TranslationKey entity.
     *
     * @return $this
     */
    public function clone(): self
    {
        return clone $this;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getLabelKey(): ?string
    {
        return $this->labelKey;
    }

    public function setLabelKey(?string $labelKey): self
    {
        $this->labelKey = $labelKey;

        return $this;
    }

    public function getFeature(): ?string
    {
        return $this->feature;
    }

    public function setFeature(?string $feature): self
    {
        $this->feature = $feature;

        return $this;
    }

    public function getChannel(): ?string
    {
        return $this->channel;
    }

    public function setChannel(?string $channel): self
    {
        $this->channel = $channel;

        return $this;
    }

    /**
     * @return Collection<int, ReferenceTranslation>
     */
    public function getReferenceTranslations(): Collection
    {
        return $this->referenceTranslations;
    }

    public function addReferenceTranslation(ReferenceTranslation $referenceTranslation): self
    {
        if (!$this->referenceTranslations->contains($referenceTranslation)) {
            $this->referenceTranslations[] = $referenceTranslation;
            $referenceTranslation->setTranslationKey($this);
        }

        return $this;
    }

    /**
     * @param Collection<int, ReferenceTranslation> $referenceTranslations
     */
    public function setReferenceTranslations(Collection $referenceTranslations): self
    {
        $this->referenceTranslations = $referenceTranslations;
        return $this;
    }

    public function removeReferenceTranslation(ReferenceTranslation $referenceTranslation): self
    {
        if ($this->referenceTranslations->removeElement($referenceTranslation)) {
            if ($referenceTranslation->getTranslationKey() === $this) {
                $referenceTranslation->setTranslationKey(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, LocalTranslation>
     */
    public function getLocalTranslations(): Collection
    {
        return $this->localTranslations;
    }

    /**
     * @param Collection<int, LocalTranslation> $localTranslations
     */
    public function setLocalTranslations(Collection $localTranslations): self
    {
        $this->localTranslations = $localTranslations;
        return $this;
    }

    public function addLocalTranslation(LocalTranslation $localTranslation): self
    {
        if (!$this->localTranslations->contains($localTranslation)) {
            $this->localTranslations[] = $localTranslation;
            $localTranslation->setTranslationKey($this);
        }

        return $this;
    }

    public function removeLocalTranslation(LocalTranslation $localTranslation): self
    {
        if ($this->localTranslations->removeElement($localTranslation)) {
            if ($localTranslation->getTranslationKey() === $this) {
                $localTranslation->setTranslationKey(null);
            }
        }
        return $this;
    }

    public function getCreatedAt(): DateTime
    {
        return $this->createdAt;
    }

    #[ORM\PrePersist]
    public function setCreatedAt(PrePersistEventArgs $eventArgs): void
    {
        $this->createdAt = new DateTime();
    }

    #[ORM\PreUpdate]
    public function getUpdatedAt(): DateTime
    {
        return $this->updatedAt;
    }

    #[ORM\PreUpdate]
    public function setUpdatedAt(): void 
    {
        $this->updatedAt = new DateTime();
    }

    public function getReleaseDate(): ?DateTime
    {
        return $this->releaseDate;
    }

    public function setReleaseDate(): void 
    {
        $this->releaseDate = new DateTime();
    }

    public function getBrand(): Brand
    {
        return $this->brand;
    }

    public function setBrand(Brand $brand): self
    {
        $this->brand = $brand;

        return $this;
    }

    public function getSprintNumber(): ?int
    {
        return $this->sprintNumber;
    }

    public function setSprintNumber(?int $sprintNumber): self
    {
        $this->sprintNumber = $sprintNumber;

        return $this;
    }

    public function getParameterValue(): ?string
    {
        return $this->parameterValue;
    }

    public function setParameterValue(?string $parameterValue): self
    {
        $this->parameterValue = $parameterValue;

        return $this;
    }

    public function getWidget(): ?Widget
    {
        return $this->widget;
    }

    public function setWidget(?Widget $widget): self
    {
        $this->widget = $widget;

        return $this;
    }
   
}
