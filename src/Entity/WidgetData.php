<?php

namespace App\Entity;

use App\Repository\WidgetDataRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Attribute\Groups;


#[ORM\Entity(repositoryClass: WidgetDataRepository::class)]
class WidgetData
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[Groups(['package_export'])]
    #[ORM\Column]
    private ?bool $enabled = null;

    #[Groups(['package_export'])]
    #[ORM\ManyToOne(inversedBy: 'widgetData', cascade: ["persist"])]
    #[ORM\JoinColumn(nullable: false)]
    private Widget $widget;

    #[Groups(['package_export'])]
    #[ORM\ManyToOne(cascade: ["persist"])]
    #[ORM\JoinColumn(nullable: false)]
    private Brand $brand;

    #[Groups(['package_export'])]
    #[ORM\ManyToOne(cascade: ["persist"])]
    private Country $country;

    #[Groups(['package_export'])]
    #[ORM\Column(length: 255)]
    private string $source;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function isEnabled(): ?bool
    {
        return $this->enabled;
    }

    public function setEnabled(bool $enabled): self
    {
        $this->enabled = $enabled;

        return $this;
    }

    public function getWidget(): ?Widget
    {
        return $this->widget;
    }

    public function setWidget(?Widget $widget): self
    {
        $this->widget = $widget;

        return $this;
    }

    public function getBrand(): Brand
    {
        return $this->brand;
    }

    public function setBrand(Brand $brand): self
    {
        $this->brand = $brand;

        return $this;
    }

    public function getCountry(): Country
    {
        return $this->country;
    }

    public function setCountry(Country $country): self
    {
        $this->country = $country;

        return $this;
    }

    public function getSource(): string
    {
        return $this->source;
    }

    public function setSource(string $source): self
    {
        $this->source = $source;

        return $this;
    }
}
