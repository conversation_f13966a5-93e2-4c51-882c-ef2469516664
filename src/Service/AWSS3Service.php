<?php
namespace App\Service;

use App\Helpers\LoggerTrait;
use Aws\S3\S3Client;
use Aws\Result;
use Exception;

/**
 * AWSS3Service
 */
class AWSS3Service
{
	use LoggerTrait;
	function __construct(private AwsSdkService $awsSdkService)
	{}
	

	public function putObject(string $key, mixed $file, string $bucket, ?string $mimeType = null): bool
	{
		try {
			$contentType = $mimeType ? ['ContentType' => $mimeType] : [];
			$options = [
				'Bucket' => $bucket,
				'Key' => $key,
				'Body' => $file,
				'ContentDisposition' => 'inline',
				'timeout' => 30,
			];
			$s3Client = $this->getS3Client();
			$response = $s3Client->putObject(
                array_merge($options, $contentType)
            ); 
            $this->logger->debug("s3Client->putObject for key $key returns  ".json_encode($response));
            return true;
		} catch (\Exception $e) {
			$this->logger->error('[AWSS3Service] error while saving to S3 '.$e->getMessage());
			return false;
		}  
	}

	public function deleteObject(string $bucket, string $key): bool
	{
		$s3Client = $this->getS3Client();
		if ($s3Client->doesObjectExist($bucket, $key)) {
            $s3Client->deleteObject([
                'Bucket' => $bucket,
                'Key'    => $key
            ]);
            return true;
        }
        return  false;
	}

	public function getObject(string $bucket, string $key)
	{
		try {
			$s3Client = $this->getS3Client();
		    return $s3Client->getObject([
		        'Bucket' => $bucket,
		        'Key' => $key,
		    ]);
		    
		} catch (\Exception $exception) {
		    throw  $exception;
		}
	}

	private function getS3Client(): S3Client
	{
		return $this->awsSdkService->getS3Client();
	}

	/**
 	* @return array<string, string>|bool
 	*/
	public function getObjectMetaData(string $bucket, string $key): array|bool
	{
		try {
			$s3Client = $this->getS3Client();
			$objectMetaData = [];

			// Check if the object exists
			if ($s3Client->doesObjectExist($bucket, $key)) {
				// Get the object's metadata
				$result = $s3Client->headObject([
					'Bucket' => $bucket,
					'Key' => $key,
				]);

				// Get the creation and last update dates
				$objectMetaData['creationDate'] = $result['@metadata']['headers']['date'];
				$objectMetaData['lastUpdateDate'] = $result['LastModified'];

				return $objectMetaData;
			} else {
				return false;
			}
		} catch (Exception $e) {
			return false;
		}
	}

	public function getObjectContent(string $bucket, string $key): string|bool
	{
		try {
			$s3Client = $this->getS3Client();

			/** @var Result $result */
			$result = $s3Client->getObject([
				'Bucket' => $bucket,
				'Key' => $key,
			]);

			$body = $result->hasKey('Body') ? $result->get('Body') : '';
			return (string) $body;
		} catch (Exception $e) {
			return false;
		}
	}	

	public function objectExist(string $bucket, string $key): bool
	{
		$s3Client = $this->getS3Client();
		if ($s3Client->doesObjectExist($bucket, $key)) {
            return true;
        }
        return  false;
	}
	
	
	public function copyObject(string $bucket, string $key,string $copySource): bool
	{
		$s3Client = $this->getS3Client();
		$s3Client->copyObject([
		    'Bucket' => $bucket,
		    'Key' => $key,
		    'CopySource' => $copySource
		]);
		return true;
	}

	public function listObjectsV2(string $bucket, string $key)
	{
		$s3Client = $this->getS3Client();
		$objects = $s3Client->listObjectsV2([
			'Bucket' => $bucket,
			'Prefix' => $key,
		]);
		return $objects;
	}

	

}
