<div class="modal fade" id="bulkDeleteStep1Modal" tabindex="-1" role="dialog" aria-labelledby="bulkDeletePreviewModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl" role="document">
    <div class="modal-content">
    
      <div class="modal-header">
        <h5 class="modal-title" id="bulkDeletePreviewModalLabel">{{'massive.wizard.delete.step1.title' | trans}}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close">
        </button>
      </div>

      <div class="modal-body">
        <input type="hidden" name="selectedLocalTranslationIds" id="selected_local_translation_ids" value="{{ selectedLocalTranslationIds }}">
        <table id="dtSelectedLocalTranslations" class="table table-sm "> 
          <thead>
              <tr>
                  <th>{{ 'brand' | trans }}</th>
                  <th>{{ 'country' | trans }}</th>
                  <th>{{ 'channel' | trans }}</th>
                  <th>{{ 'localLanguage' | trans }}</th>
                  <th>{{ 'keyName' | trans }}</th>
                  <th>{{ 'translation' | trans }}</th>
                  <th>{{ 'updatedAt' | trans }}</th>
              </tr> 
          </thead> 
          <tbody>
              {% for row in dtData.data %}
                  <tr>
                      <td>{{ row.brand }}</td>
                      <td>{{ row.country }}</td>
                      <td>{{ row.channel }}</td>
                      <td>{{ row.localLanguage }}</td>
                      <td>{{ row.label_key }}</td>
                      <td>{{ row.translation }}</td>
                      <td>{{ row.updatedAt }}</td>
                  </tr>
              {% endfor %}
          </tbody> 
        </table>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" id="cancelDelete" data-bs-dismiss="modal">{{ 'cancel' | trans }}</button>
        <button type="button" class="btn btn-primary" id="bulkDeleteConfirmation">{{ 'confirm' | trans }}</button>
      </div>
    </div>
  </div>
</div>