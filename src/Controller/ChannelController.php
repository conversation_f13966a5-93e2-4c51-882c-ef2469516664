<?php

namespace App\Controller;

use App\Security\User;
use App\Entity\Channel;
use App\Form\ChannelFormType;
use App\Service\ChannelManager;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\CurrentUser;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;

#[Route('/channel', name: 'channel_')]
class ChannelController extends AbstractController
{
    #[Route('/', name: 'list')]
    public function index(#[CurrentUser] User $user, ChannelManager $channelManager): Response
    {
        $isSuperAdmin = $user->getProfile()->isSuperAdministrator();
        $channels = $channelManager->list();
        return $this->render('channel/index.html.twig', [
            'channels' => $channels,
            'isSuperAdmin' => $isSuperAdmin
        ]);
    }

    #[Route(path: '/create', name: 'create')]
    public function create(Request $request, EntityManagerInterface $em, ChannelManager $channelManager): Response
    {
        $channel = new Channel();
        $form = $this->createForm(ChannelFormType::class, $channel);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $em->persist($channel);
            $em->flush();

            $operations = ['mediaSync', 'syncLocalization'];
            foreach ($operations as $method) {
                [$check, $message] = $channelManager->$method($channel);
                if (!$check) {
                    $this->addFlash('danger', $message);
                    return $this->redirectToRoute('channel_list');
                }
            }
            $this->addFlash('success', 'success_channel_create_message');
            return $this->redirectToRoute('channel_list');
        }
        return $this->render('channel/add.html.twig', [
            'form' => $form->createView()
        ]);
    }

    #[Route('/profile_test', name: 'profile_test')]
    public function test(Request $request, #[CurrentUser] User $user): JsonResponse
    {
        dd($user->getProfile());
        return new JsonResponse($user->getProfile());
    }    
}
