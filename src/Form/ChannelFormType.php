<?php

namespace App\Form;

use App\Entity\Channel;
use App\Entity\ChannelType;
use App\Repository\ChannelTypeRepository;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;

class ChannelFormType extends AbstractType
{
    public function __construct(private ChannelTypeRepository $channelTypeRepo) {}

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class, [
                'constraints' => [
                    new NotBlank(),
                    new Length(['min' => 2, 'max' => 255])
                ]
            ])
            ->add('description', TextType::class, [
                'required' => false,
                'constraints' => [
                    new Length(['max' => 255])
                ]
            ])
            ->add('channelType', EntityType::class, [
                'class' => ChannelType::class,
                'choice_label' => 'name',
                'label' => 'type',
                'placeholder' => 'Choose a channel type',
                'required' => true,
                'mapped' => true,
                'by_reference' => false,
                'multiple' => false,
            ]);

        $builder->addEventListener(FormEvents::PRE_SUBMIT, function (FormEvent $event) {
            $data = $event->getData();
            if (isset($data['name'])) {
                $data['name'] = strtoupper($data['name']);
            }
            $event->setData($data);
        });
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Channel::class,
        ]);
    }
}
