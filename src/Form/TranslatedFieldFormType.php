<?php

namespace App\Form;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;

class TranslatedFieldFormType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $languages = $options['attr']['languages'] ?? [];
        $fieldType = $options['label_attr']['label_type'] ?? TextType::class;
        $required = $options['attr']['required'] ?? false;
        
        $fieldOptions = [
            'attr' => [
                'class' => 'translated-field',
            ],
        ];
        
        if ($required) {
            $fieldOptions['required'] = true;
            if (!isset($fieldOptions['constraints'])) {
                $fieldOptions['constraints'] = [];
            }
        }

        foreach ($languages as $language) {
            $constraints = $required ? [
                new NotBlank()
            ] : [];
            if ($fieldType == FileType::class) {
                
                $this->buildFileTypeForm($builder, $language, $constraints);
            } else {
                // Merge language-specific options
                $languageOptions = array_merge($fieldOptions, [
                    'label' => $language,
                    'constraints' => $constraints,
                ]);

                $builder->add($language, $fieldType, $languageOptions);
            }
        }
    }

    private function buildFileTypeForm(FormBuilderInterface &$builder, string $key, ?array $constraints = [])
    {
        $builder
            ->add($key . '_input', FileType::class, [
                'label' => $key,
                'data_class' => null,
                'row_attr' => [
                    'type' => 'fileInput'
                ],
                'attr' => [
                    'placeholder' => 'choose_file'
                ]
            ])
            ->add($key, TextType::class, [
                'label' => false,
                'attr' => [
                    'class' => 'd-none image-type-error'
                ],
                'constraints' => $constraints
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => null, 
            'label_attr' => [], 
            'languages' => []
        ]);
    }
}