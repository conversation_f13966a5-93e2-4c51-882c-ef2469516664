import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;

import java.util.List;

public class Locators {
    public static WebDriver driver=TestUtilities.getDriver();
    public static WebElement Administration() {
        return driver.findElement(By.xpath("//i[@class='menu-icon fas fa-copy']//ancestor::a"));
    }
    public static WebElement Site_Management() {
        return driver.findElement(By.xpath("//a[@href='/site/']"));
    }
    public static WebElement NewTranslationKey() {
        return driver.findElement(By.xpath("//div[@class='d-flex float-right']/a[@class='btn btn-success']"));
    }
    public static WebElement ReferenceLabelManagement() {
        return driver.findElement(By.xpath("//a[@href='/translation-key/']"));
    }
    public static WebElement Local_translations() {
        return driver.findElement(By.xpath("//a[@href='/local-translation/']"));
    }
    public static WebElement Feature_Setting() {
        return driver.findElement(By.xpath("//a[@href='/feature-setting/']"));
    }
    public static WebElement Media_Delete() {
        return driver.findElement(By.xpath("//a[@id='delete-media']"));
    }
    public static WebElement Media_Copy_Url() {
        return driver.findElement(By.xpath("//button[@id='copy-media']"));
    }
    public static WebElement Confirm_Media_Delete() {
        return driver.findElement(By.xpath("//div[@class='modal-footer']/button[@class='btn btn-danger']"));
    }
    public static WebElement Access_Management() {
        return driver.findElement(By.id("menu_item_13"));
    }
    public static WebElement Global_Profiles() {
        return driver.findElement(By.xpath("//a[@href='/profile/global']"));
    }
    public static WebElement Role() {
        return driver.findElement(By.xpath("//a[@href='/idp-role/']"));
    }
    public static WebElement Menu() {
        return driver.findElement(By.xpath("//a[@href='/role/list']"));
    }
    public static WebElement Local_Profiles() {
        return driver.findElement(By.xpath("//a[@href='/profile/local']"));
    }
    public static WebElement Icon_Management() {
        return driver.findElement(By.xpath("//a[@href='/icon/']"));
    }
    public static WebElement Channel_Management() {
        return driver.findElement(By.xpath("//a[@href='/channel/']"));
    }
    public static WebElement Settings_Release() {
        return driver.findElement(By.xpath("//a[@href='/settings/sync']"));
    }
    public static WebElement Widget_Administration() {
        return driver.findElement(By.xpath("//a[@href='/widget/']"));
    }
    public static WebElement Widget_Management() {
        return driver.findElement(By.xpath("//a[@href='/widget_management/']"));
    }
    public static WebElement Reference_Label_Release() {
        return driver.findElement(By.xpath("//a[@href='/publish/']"));
    }
    public static WebElement History_Data_Log() {
        return driver.findElement(By.xpath("//a[@href='/access-logs/']"));
    }
    public static WebElement Bulk_Operation() {
        return driver.findElement(By.xpath("//a[@href='/bulk-operation/form']"));
    }

    public static WebElement Dashboard() {
        return driver.findElement(By.xpath("//i[@class='menu-icon fas fa-home']//ancestor::a"));
    }
    public static WebElement Title_Text() {
        return driver.findElement(By.id("selected-menu"));
    }
    public static WebElement Reference_Translation_Dashboard() {
        return driver.findElement(By.xpath("//a[@href='/reference-translation/' and @class='btn btn-block btn-primary']"));
    }
    public static WebElement Local_Translation_Dashboard() {
        return driver.findElement(By.xpath("//a[contains(@href,'/local-translations/') and @class='btn btn-block btn-primary']"));
    }
    public static WebElement Translation_Dashboard_Text() {
        return driver.findElement(By.xpath("//div[@class='card-header']/h3"));
    }

    public static WebElement User_Profile() {
        return driver.findElement(By.xpath("//a[@id='user-login-dropdown']"));
    }
    public static WebElement Settings_Profile() {
        return driver.findElement(By.xpath("//a[@href='/user-settings/dashboard']"));
    }
    public static WebElement User_Prefernces() {
        return driver.findElement(By.xpath("//a[@href='/user-settings/preferences']"));
    }
    public static WebElement Language_Select() {
        return driver.findElement(By.id("profile_login_form_language"));
    }
    public static WebElement Media_Library() {
        return driver.findElement(By.xpath("//a[@href='/admin/profile/1/medias/']"));
    }
    public static List<WebElement> Language_Options() {
        return driver.findElements(By.xpath("//select[@id='profile_login_form_language']/option"));
    }
    public static List<WebElement> Leaf_APP() {
        return driver.findElements(By.xpath("//ul[@id='Leaf-APP']/li/span"));
    }
    public static List<WebElement> Leaf_Web() {
        return driver.findElements(By.xpath("//ul[@id='Leaf-WEB']/li"));
    }
    public static WebElement APP() {
        return driver.findElement(By.id("icon-APP"));
    }
    public static WebElement WEB() {
        return driver.findElement(By.id("icon-WEB"));
    }
    public static WebElement Language_Save() {
        return driver.findElement(By.id("btn_save"));
    }
    public static WebElement APP_CITROEN() {
        return driver.findElement(By.xpath("//ul[@id='Leaf-APP']/li/span[text()='Citroën']"));
    }

    public static WebElement ADD_Media() {
        return driver.findElement(By.xpath("//a[@id='add-media']"));
    }
    public static WebElement Alt_Text() {
        return driver.findElement(By.xpath("//input[@id='edit_media_textAlt' and @class='form-control form-control']"));
    }
    public static WebElement Edit_Alt_Text() {
        return driver.findElement(By.xpath("//input[@id='edit_media_textAlt' and @class='edit_medias_form_textAlt form-control']"));
    }
    public static WebElement Edit_Cpyright_Text() {
        return driver.findElement(By.xpath("//input[@id='edit_media_copyright' and @class='edit_medias_form_copyright form-control']"));
    }
    public static WebElement Cpyright_Text() {
        return driver.findElement(By.xpath("//input[@id='edit_media_copyright' and @class='form-control form-control']"));
    }
    public static WebElement Edit_Comment_Text() {
        return driver.findElement(By.xpath("//textarea[@id='edit_media_comment' and @class='edit_medias_form_comment form-control']"));
    }
    public static WebElement EDIT_Save() {
        return driver.findElement(By.xpath("//button[@id='media-form-edit-button']"));
    }
    public static WebElement Comment_Text() {
        return driver.findElement(By.xpath("//textarea[@id='edit_media_comment' and @class='form-control form-control']"));
    }
    public static WebElement EDIT_Media() {
        return driver.findElement(By.xpath("//a[@id='edit-media']"));
    }
    public static WebElement ADD_Browse() {
        return driver.findElement(By.xpath("//input[@class='uppy-Dashboard-input']"));
    }
    public static WebElement Media_Save() {
        return driver.findElement(By.xpath("//button[@id='media-form-add-button']"));
    }
    public static WebElement Media_Img_lastdiv() {
        return driver.findElement(By.xpath("//div[@id='list-image']/div[last()]"));
    }
    public static WebElement Close_File() {
        return driver.findElement(By.xpath("//button[@title='Remove file' and @aria-label='Remove file']"));
    }
    public static WebElement New_Translation_Key() {
        return driver.findElement(By.xpath("//a [@href='/translation-key/create']"));
    }
    public static WebElement Label_Key() {
        return driver.findElement(By.id("translation_key_form_label_key"));
    }
    public static WebElement Francias_Label() {
        return driver.findElement(By.id("translation_key_form_referenceTranslations_0_translation"));
    }
    public static WebElement Anglias_Label() {
        return driver.findElement(By.id("translation_key_form_referenceTranslations_1_translation"));
    }
    public static WebElement Italien_Label() {
        return driver.findElement(By.id("translation_key_form_referenceTranslations_2_translation"));
    }
    public static WebElement Espagnol_Label() {
        return driver.findElement(By.id("translation_key_form_referenceTranslations_3_translation"));
    }
    public static WebElement Allemand_Label() {
        return driver.findElement(By.id("translation_key_form_referenceTranslations_4_translation"));
    }
    public static WebElement App_Reference_CheckBox() {
        return driver.findElement(By.id("translation_key_form_channel_0"));
    }
    public static WebElement Web_Reference_CheckBox() {
        return driver.findElement(By.id("translation_key_form_channel_1"));
    }
    public static WebElement Reference_Label_Save() {
        return driver.findElement(By.xpath("//button[@class='btn btn-success float-right']"));
    }
    public static WebElement Reference_Label_Cancel() {
        return driver.findElement(By.xpath("//a[@href='/translation-key/' and @class='ml-2 btn btn-dark']"));
    }
    public static WebElement Reference_Label_Search() {
        return driver.findElement(By.xpath("//input[@type='search']"));
    }
    public static WebElement Reference_Label_Table() {
        return driver.findElement(By.xpath("//table[@id='dt_translation_key_list']/tbody"));
    }
    public static WebElement Settings_Release_Source() {
        return driver.findElement(By.xpath("//input[@class='select2-search__field']"));
    }
    public static WebElement Settings_Release_Btn() {
        return driver.findElement(By.xpath("//button[@id='submit']"));
    }
    public static List<WebElement> Reference_Release() {
        return driver.findElements(By.xpath("//a[@class='btn btn-block btn-primary']"));
    }
    public static WebElement Reference_Release_Btn() {
        return driver.findElement(By.xpath("//a[@href='/publish/release']"));
    }
    public static WebElement Reference_Right_Move_Btn() {
        return driver.findElement(By.xpath("//button[@id='publish_form_culture_rightAll']"));
    }
    public static WebElement Create_Features_Btn() {
        return driver.findElement(By.xpath("//a[@class='btn btn-success']"));
    }
    public static WebElement Feature_Setting_Name() {
        return driver.findElement(By.id("feature_setting_name"));
    }
    public static WebElement Feature_Setting_File() {
        return driver.findElement(By.id("feature_setting_file"));
    }
    public static WebElement Widget_File_Upload() {
        return driver.findElement(By.id("widget_form_file"));
    }
    public static WebElement Feature_Setting_View_Btn() {
        return driver.findElement(By.id("viewButton"));
    }
    public static WebElement Features_View_Close_Btn() {
        return driver.findElement(By.xpath("//button[@class='btn-close']"));
    }
    public static WebElement Features_Back_Btn() {
        return driver.findElement(By.xpath("//a[@class='btn btn-warning']"));
    }
    public static WebElement Profile_exit_Btn() {
        return driver.findElement(By.xpath("//i[@class='fa fa-share-square nav-icon']"));
    }
    public static WebElement Features_Last_Row() {
        return driver.findElement(By.xpath("//table/tbody/tr[last()]/td[1]"));
    }
    public static WebElement Features_Admin_Mode() {
        return driver.findElement(By.xpath("//select[@id='feature_setting_featureAccesses_0_mode']/option[last()]"));
    }
    public static WebElement Features() {
        return driver.findElement(By.xpath("//a[@class='link-menu parent' and @data-toggle='collapse']"));
    }
    public static WebElement Global_Features() {
        return driver.findElement(By.xpath("//a[@id='menu_item_90']"));
    }
    public static WebElement Global_Features_Last() {
        return driver.findElement(By.xpath("//div[@class='collapse ps-2 sub_menu' and @style]/div/div[last()]"));
    }
    public static WebElement Features_Setting_Save() {
        return driver.findElement(By.xpath("//button[@class='btn btn-success']"));
    }
    public static WebElement Widget_Save() {
        return driver.findElement(By.id("submit"));
    }
    public static WebElement Features_Title() {
        return driver.findElement(By.xpath("//h3[@id='selected-menu']"));
    }
    public static WebElement Features_View() {
        return driver.findElement(By.xpath("//table/tbody/tr[last()]/td[2]/div/div[3]"));
    }
    public static WebElement Features_Delete() {
        return driver.findElement(By.xpath("//table/tbody/tr[last()]/td[2]/div/div[4]"));
    }
    public static WebElement Features_Confirm_Delete() {
        return driver.findElement(By.xpath("//button[@class='btn btn-danger']"));
    }
    public static WebElement Features_Edit() {
        return driver.findElement(By.xpath("//table/tbody/tr[last()]/td[2]/div/div[1]"));
    }
    public static WebElement Widget_Name() {
        return driver.findElement(By.id("widget_form_name"));
    }
    public static WebElement Widget_Description() {
        return driver.findElement(By.id("widget_form_description"));
    }
    public static WebElement Widget_Version() {
        return driver.findElement(By.id("widget_form_version"));
    }
    public static WebElement Widget_Type_Space() {
        return driver.findElement(By.xpath("//select[@id='widget_form_type']/option[@value='space']"));
    }
    public static WebElement Bulk_Brand_Citroen() {
        return driver.findElement(By.xpath("//select[@id='brandFilter']/option[1]"));
    }
    public static WebElement Bulk_Country_UK() {
        return driver.findElement(By.xpath("//select[@id='countryFilter']/option[@value='GB']"));
    }
    public static WebElement Bulk_Channel_APP() {
    return driver.findElement(By.xpath("//select[@id='channelFilter']/option[@value='APP']"));
    }
    public static WebElement Bulk_Translation_Status() {
        return driver.findElement(By.xpath("//select[@id='statusFilter']/option[@value='1']"));
    }
    public static WebElement Bulk_LocalLang_Eng() {
        return driver.findElement(By.xpath("//select[@id='locallanguageFilter']/option[@value='en']"));
    }
    public static WebElement Bulk_FirstRow_Checkbox() {
        return driver.findElement(By.xpath("//table/tbody/tr[1]//input[@type='checkbox']"));
    }
    public static WebElement Bulk_Copy() {
        return driver.findElement(By.xpath("//select[@id='massiveOperation']/option[@value='bulk_copy']"));
    }
    public static WebElement Bulk_Apply() {
        return driver.findElement(By.xpath("//button[@id='massiveOperationApply']"));
    }
    public static WebElement Bulk_Next_Step1() {
        return driver.findElement(By.xpath("//button[@id='bulkCopyStep1Next']"));
    }
    public static WebElement Bulk_Next_Step2() {
        return driver.findElement(By.xpath("//button[@id='bulkCopyStep2Next']"));
    }
    public static WebElement Bulk_Finish() {
        return driver.findElement(By.xpath("//button[@id='bulkCopyStep3Finish']"));
    }
    public static WebElement Bulk_Country_India() {
        return driver.findElement(By.xpath("//select[@id='target_country_ids']/option[text()='India']"));
    }



}
