{% extends '_layout/layout.html.twig' %}

{% block content %}
	<div class="card shadow-sm border-0 rounded-3 overflow-hidden">
		{% include '_layout/form_card_header.html.twig' with {
        'title': 'channel_add' | trans
    } %}

		<!-- Flash Message Container -->
		<div id="flash-message-container" class="container mt-3" style="display: none;">
			<div class="alert alert-danger alert-dismissible fade show" role="alert">
				<strong>Error!</strong>
				<span id="flash-message-text">{{ 'invalid_form' | trans }}</span>
				<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
			</div>
		</div>

		<div class="container py-4">
			{{ form_start(form, {'attr': {'class': 'needs-validation', 'novalidate': 'novalidate', 'id': 'channel-form'}}) }}

			<div
				class="row g-4">
				<!-- Left Column -->
				<div
					class="col-md-6">
					<!-- Label Field -->
					<div class="form-group mb-3">
						{{ form_label(form.label, null, {'label_attr': {'class': 'form-label fw-semibold'}}) }}
						{{ form_widget(form.label, {'attr': {'class': 'form-control rounded-3', 'placeholder': 'Enter label', 'required': 'required'}}) }}
						<div class="invalid-feedback">{{ 'invalid_label' | trans }}</div>
					</div>

					<!-- Brand Field -->
					<div class="form-group mb-3">
						{{ form_label(form.brand, null, {'label_attr': {'class': 'form-label fw-semibold'}}) }}
						{{ form_widget(form.brand, {'attr': {'class': 'form-control rounded-3', 'placeholder': 'Select brand', 'required': 'required'}}) }}
						<div class="invalid-feedback">{{ 'invalid_brand' | trans }}</div>
					</div>

					<!-- SDP Field -->
					<div class="form-group mb-3">
						{{ form_label(form.sdp, null, {'label_attr': {'class': 'form-label fw-semibold'}}) }}
						{{ form_widget(form.sdp, {'attr': {'class': 'form-control rounded-3', 'placeholder': 'Enter SDP value', 'required': 'required'}}) }}
						<div class="invalid-feedback">{{ 'invalid_sdp' | trans }}</div>
					</div>

					<!-- isO2x Field -->
					<div class="form-check form-switch mb-3">
						{{ form_widget(form.isO2x, {'attr': {'class': 'form-check-input'}}) }}
						{{ form_label(form.isO2x, null, {'label_attr': {'class': 'form-check-label ms-2'}}) }}
					</div>

					<!-- Default Image Field -->
					<div class="form-group mb-3">
						<label for="defaultImage" class="form-label fw-semibold">
							{{ 'defaultManual' | trans }}
						</label>
						<div class="d-flex align-items-center gap-3 mt-2">
							<button type="button" class="btn btn-outline-primary" id="choose-image-btn" data-bs-toggle="modal" data-bs-target="#select-image-modal">
								<i class="bi bi-image"></i>
								{{ 'Choose PDF' | trans }}
							</button>

							<div id="selected-image-preview" class="d-none">
								<div class="selected-image-container border rounded-3 overflow-hidden" style="width: 80px; height: 80px;">
									<img src="{{ asset('images/pdf-icon.png') }}" id="preview-image" class="img-fluid w-100 h-100 object-fit-cover" alt="Selected PDF">
								</div>
								<p id="preview-name" class="mt-2"></p>
							</div>
						</div>

						{{ form_widget(form.defaultManual, {'attr': {'class': 'd-none', 'required': 'required'}}) }}
						<div class="invalid-feedback" id="image-feedback" style="display: none;">{{ 'invalid_image' | trans }}.</div>
					</div>
				</div>

				<!-- Right Column -->
				<div
					class="col-md-6">
					<!-- LCDV Field -->
					<div class="form-group mb-4">
						{{ form_label(form.lcdv, null, {'label_attr': {'class': 'form-label fw-semibold'}}) }}

						<div class="lcdv-container">
							<div class="input-group mb-3">
								<input type="text" id="lcdv-input" class="form-control rounded-start" placeholder="Enter LCDV code">
								<button type="button" id="add-lcdv" class="btn btn-success rounded-end">
									<i class="bi bi-plus-lg"></i>
									{{ 'addLcdv' | trans }}
								</button>
							</div>
							<div id="lcdv-feedback" class="invalid-feedback" style="display: none;">
								{{ 'invalid_lcvd_msg' | trans }}

							</div>

							<!-- Store the prototype safely -->
							<div id="lcdv-prototype" data-prototype={{ form_widget(form.lcdv.vars.prototype)|e('html_attr') }} style="display: none;"></div>

							<div id="lcdv-fields" class="lcdv-fields rounded-3 p-3 border" data-index="{{ form.lcdv|length }}">
								{% if form.lcdv|length == 0 %}
									<div class="text-center text-muted p-3">
										{{ 'invalid_lcvd_msg' | trans }}

									</div>
								{% endif %}

								{% for lcdvField in form.lcdv %}
									<div class="lcdv-item card mb-2 border-0 shadow-sm rounded-3">
										<div class="card-body d-flex justify-content-between align-items-center py-2 px-3">
											<span class="fw-medium">{{ lcdvField.vars.value }}</span>
											<button type="button" class="remove-lcdv btn btn-sm btn-outline-danger">
												<i class="bi bi-x">
													X
												</i>
											</button>
											{{ form_widget(lcdvField, {'attr': {'class': 'lcdv-value form-control', 'hidden': true}}) }}
										</div>
									</div>
								{% endfor %}
							</div>
						</div>
					</div>


				</div>
			</div>

			<!-- Submit Button -->
			<div class="mt-4 d-flex justify-content-end gap-4">
				<a class="mr-3 btn btn-dark" role="button" href="{{ path('owners_manual_list') }}">
					<i class="fas fa-undo"></i>
					{{ 'btn_cancel'|trans }}
				</a>
				<button type="submit" class="btn btn-primary px-4 rounded-3">
					<i class="bi bi-check-lg me-2"></i>
					{{ 'saveModel' | trans }}
				</button>
			</div>

			{{ form_end(form) }}
		</div>

		<!-- Image Selection Modal -->
		<div class="modal fade" id="select-image-modal" tabindex="-1" role="dialog" aria-labelledby="selectImageModalLabel" aria-hidden="true">
			<div class="modal-dialog modal-xl" role="document">
				<div class="modal-content border-0 shadow">
					<div class="modal-header">
						<h5 class="modal-title" id="selectImageModalLabel">{{ 'saveImage' | trans }}</h5>
						<button type="button" class="btn-close" id='close-modal' data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div class="input-group mb-4">
							<span class="input-group-text bg-light border-end-0">
								<i class="bi bi-search"></i>
							</span>
							<input type="text" id="mediaSearch" class="form-control border-start-0" placeholder="Search by media name..."/>
						</div>

						<div class="row g-3 h-100 border-0">
							{% for media in medias %}
								{% if media.extension == 'pdf' %}
									<div class="col-md-3 col-sm-4 col-6 mb-3">
										<div class="card h-100 media-card border-0 shadow-sm hover-shadow transition rounded-3 overflow-hidden" data-media-id="{{ media.id }}" data-media-src="{{ mediaUrl ~ '/' ~ media.path }}">
											<div class="ratio ratio-1x1">
												<img src="{{ asset('images/pdf-icon.png') }}" class="card-img-top media-thumbnail" alt="{{ media.name }}">
											</div>
											<div class="card-body p-2">
												<p class="card-text media-name text-truncate small">{{ media.name }}</p>
											</div>
										</div>
									</div>
								{% endif %}
							{% endfor %}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
{% endblock %}

{% block stylesheets %}
	{{ parent() }}
	<style type="text/css">
		/* General Styling */
		.form-label {
			margin-bottom: 0.5rem;
		}

		/* LCDV Field Styling */
		.lcdv-fields {
			max-height: 250px;
			overflow-y: auto;
			background-color: #f8f9fa;
			transition: all 0.3s ease;
		}

		.lcdv-fields.border-danger {
			background-color: rgba(255, 0, 0, 0.05);
		}

		.lcdv-item {
			transition: all 0.2s ease;
		}

		.lcdv-item:hover {
			background-color: #f0f0f0;
		}

		.remove-lcdv {
			width: 30px;
			height: 30px;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 0;
		}

		/* Media Selection Styling */
		.media-card {
			cursor: pointer;
			transition: all 0.2s ease;
		}

		.media-card:hover {
			transform: translateY(-5px);
			box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
		}

		.media-card.selected {
			border: 3px solid var(--bs-primary) !important;
		}

		.hover-shadow:hover {
			box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
		}

		.transition {
			transition: all 0.3s ease;
		}

		/* Flash message animation */
		@keyframes flashIn {
			from {
				opacity: 0;
				transform: translateY(-20px);
			}
			to {
				opacity: 1;
				transform: translateY(0);
			}
		}

		#flash-message-container {
			animation: flashIn 0.3s ease-out;
		}
	</style>
{% endblock %}

{% block script %} 
	<script src="{{ asset('js/jquery.min.js') }}"></script>
	<script>
	document.addEventListener('DOMContentLoaded', function() {
	    // Set up variables for the collection handling
	    var $container = $('#lcdv-fields');
	    var index = $container.data('index');
	    var prototype = $('#lcdv-prototype').data('prototype');
	    
	    // Function to show flash message
	    function showFlashMessage(message) {
	        const flashContainer = document.getElementById('flash-message-container');
	        const flashText = document.getElementById('flash-message-text');
	        
	        flashText.textContent = message;
	        flashContainer.style.display = 'block';
	        
	        // Scroll to top to ensure flash message is visible
	        window.scrollTo({top: 0, behavior: 'smooth'});
	        
	        // Auto-dismiss after 5 seconds
	        setTimeout(() => {
	            const bsAlert = new bootstrap.Alert(flashContainer.querySelector('.alert'));
	            bsAlert.close();
	            setTimeout(() => {
	                flashContainer.style.display = 'none';
	            }, 300);
	        }, 5000);
	    }
	    
	    // Check if LCDV list is empty and handle empty state display
	    function updateEmptyState() {
	        if ($('.lcdv-item').length === 0) {
	            if (!$container.find('.text-muted').length) {
	                $container.html('<div class="text-center text-muted p-3">{{ 'invalid_lcvd_msg' | trans }}</div>');
	            }
	        } else {
	            $container.find('.text-muted').remove();
	        }
	    }
	    
	    // Initial check
	    updateEmptyState();
	    
	    // Function to add a new LCDV item
	    function addLcdvItem(value) {
	        if (!prototype) return;
	        // Replace __name__ in the prototype with the current index
	        var newForm = prototype.replace(/__name__/g, index);
	        
	        // Remove empty state message if present
	        $container.find('.text-muted').remove();
	        
	        // Create a new form element
	        var $newFormLi = $('<div class="lcdv-item card mb-2 border-0 shadow-sm rounded-3"></div>');
	        var $cardBody = $('<div class="card-body d-flex justify-content-between align-items-center py-2 px-3"></div>');
	        
	        // Create a visible span with the value
	        var $span = $('<span class="fw-medium">' + value + '</span>');
	        
	        // Create a remove button
	        var $removeButton = $('<button type="button" class="remove-lcdv btn btn-sm btn-outline-danger rounded-circle"><i class="bi bi-x"></i></button>');
	        
	        // Create the hidden input with the proper value
	        var $input = $(newForm).val(value).addClass('lcdv-value').attr('hidden', true);
	        
	        // Append all elements to the new form item
	        $cardBody.append($span).append($removeButton).append($input);
	        $newFormLi.append($cardBody);
	        
	        // Add the new form item to the container with animation
	        $newFormLi.hide();
	        $container.append($newFormLi);
	        $newFormLi.fadeIn(300);
	        
	        // Increment the index for the next item
	        index++;
	        
	        // Remove any error styling since we now have an LCDV item
	        $('#lcdv-fields').removeClass('border-danger');
	        $('#lcdv-feedback').hide();
	    }
	    
	    // Add LCDV button click handler
	    $('#add-lcdv').click(function() {
	        var input = $('#lcdv-input');
	        var value = input.val().trim();
	        
	        if (value === '') {
	            input.addClass('is-invalid');
	            setTimeout(() => input.removeClass('is-invalid'), 2000);
	            return;
	        }
	        
	        // Check if the value already exists
	        if ($('.lcdv-item span').filter(function() {
	            return $(this).text() === value;
	        }).length > 0) {
	            input.addClass('is-invalid');
	            showFlashMessage("This LCDV code already exists.");
	            setTimeout(() => input.removeClass('is-invalid'), 2000);
	            return;
	        }
	        
	        // Add the new item
	        addLcdvItem(value);
	        
	        // Clear the input field
	        input.val('');
	        input.focus();
	    });
	    
	    // Enter key in the input field
	    $('#lcdv-input').keypress(function(e) {
	        if(e.which == 13) {
	            e.preventDefault();
	            $('#add-lcdv').click();
	        }
	    });
	    
	    // Remove LCDV item handler (using event delegation)
	    $(document).on('click', '.remove-lcdv', function() {
	        var item = $(this).closest('.lcdv-item');
	        item.fadeOut(300, function() {
	            item.remove();
	            updateEmptyState();
	        });
	    });
	    
	    // Form validation
	    $('#channel-form').on('submit', function(event) {
	        event.preventDefault(); // Prevent default submission first
	        
	        let hasErrors = false;
	        let errorMessage = "Please correct the following errors:";
	        
	        // Validate form fields
	        const labelField = document.getElementById('{{ form.label.vars.id }}');
	        const brandField = document.getElementById('{{ form.brand.vars.id }}');
	        const sdpField = document.getElementById('{{ form.sdp.vars.id }}');
	        const imageField = document.getElementById('{{ form.defaultManual.vars.id }}');
	        
	        // Reset validation states
	        labelField.classList.remove('is-invalid');
	        brandField.classList.remove('is-invalid');
	        sdpField.classList.remove('is-invalid');
	        $('#lcdv-fields').removeClass('border-danger');
	        $('#lcdv-feedback').hide();
	        $('#image-feedback').hide();
	        
	        // Check Label
	        if (!labelField.value.trim()) {
	            labelField.classList.add('is-invalid');
	            hasErrors = true;
	            errorMessage += " Label is required.";
	        }
	        
	        // Check Brand
	        if (!brandField.value.trim()) {
	            brandField.classList.add('is-invalid');
	            hasErrors = true;
	            errorMessage += " Brand is required.";
	        }
	        
	        // Check SDP
	        if (!sdpField.value.trim()) {
	            sdpField.classList.add('is-invalid');
	            hasErrors = true;
	            errorMessage += " SDP is required.";
	        }
	        
	        // Check LCDV
	        if ($('.lcdv-item').length === 0) {
	            $('#lcdv-fields').addClass('border-danger');
	            $('#lcdv-feedback').show();
	            hasErrors = true;
	            errorMessage += " At least one LCDV code is required.";
	        }
	        
	        // Check Image
	        if (!imageField.value.trim()) {
	            $('#image-feedback').show();
	            hasErrors = true;
	            errorMessage += " Default image is required.";
	        }
	        
	        if (hasErrors) {
	            showFlashMessage(errorMessage);
	            return false;
	        }
	        
	        // If no errors, submit the form
	        this.submit();
	    });
	    
	    // Image selection
	    const modal = document.getElementById('select-image-modal');
	    const bsModal = new bootstrap.Modal(modal);
	    const mediaCards = document.querySelectorAll('.media-card');
	    const modalClose = document.getElementById('close-modal');
	    
	    const imageField = document.getElementById('{{ form.defaultManual.vars.id }}');
	    const previewContainer = document.getElementById('selected-image-preview');
	    const previewImage = document.getElementById('preview-image');
		const previewName = document.getElementById('preview-name');
	    
	    // Show preview if image is already selected
	    if (imageField.value && imageField.value.trim() !== '') {
			previewName.textContent = imageField.value.split('/').pop();
	        previewContainer.classList.remove('d-none');
	    }
	    
	    // Media search functionality
	    document.getElementById('mediaSearch').addEventListener('input', function(e) {
	        const searchTerm = e.target.value.toLowerCase();
	        document.querySelectorAll('.media-card').forEach(card => {
	            const mediaName = card.querySelector('.media-name').textContent.toLowerCase();
	            const parentCol = card.closest('.col-md-3');
	            if (mediaName.includes(searchTerm)) {
	                parentCol.style.display = '';
	            } else {
	                parentCol.style.display = 'none';
	            }
	        });
	    });
	    
	    // Handle card selection
	    mediaCards.forEach(card => {
	        card.addEventListener('click', function() {
	            // Remove selected class from all cards
	            mediaCards.forEach(c => c.classList.remove('selected'));
	            // Add selected class to clicked card
	            this.classList.add('selected');
	            selectMedia(this.dataset.mediaSrc);
	            modalClose.click();
	        });
	    });
	    
	    function selectMedia(mediaSrc) {
	        imageField.value = mediaSrc;
			previewName.textContent = mediaSrc.split('/').pop();
	        previewContainer.classList.remove('d-none');
	        $('#image-feedback').hide();
	        
	        // Add animation
	        previewContainer.classList.add('animate__animated', 'animate__fadeIn');
	        setTimeout(() => {
	            previewContainer.classList.remove('animate__animated', 'animate__fadeIn');
	        }, 1000);
	    }
	});
	</script>
{% endblock %}
