$(document).ready(function () {
    $('body').on('click', '.remove-model', function(e){
        e.preventDefault();
        var $wrapper = $(this).closest('.multi-fields').find('.multi-fields-wrapper');
        var index = $wrapper.data('index');
        if(index > 2) {
            $(this).parents('.multi-fields').find('.remove-model').removeClass('disabled');
        } else {
            $(this).parents('.multi-fields').find('.remove-model').addClass('disabled');
        }
        $(this).closest('.model-container').remove();
        $wrapper.data('index', index - 1);
        if(index == 2) {
            $wrapper.find('.custom-hr').remove();
        } 
    })
    $('body').on('click', '.jslink', function (e) {
        var $wrapper = $(this).closest('.multi-fields').find('.multi-fields-wrapper');
        e.preventDefault();
        // Get the data-prototype explained earlier
        var prototype = $wrapper.data('prototype');
        // get the new index
        var index = $wrapper.data('index');
        // Replace '__name__' in the prototype's HTML to
        // instead be a number based on how many items we have
        var newForm = prototype.replace(/__name__/g, index);
        newForm = newForm.replace(/__INDEXOR__/, (index + 1));
        
        // increase the index with one for the next item
        $wrapper.data('index', index + 1);
        // Display the form in the page before the "new" link
        $wrapper.find('h4').after(newForm);

        if(index >= 1) {
            $wrapper.find('.model-container').eq(0).append('<hr class="custom-hr">');
            $(this).closest('.multi-fields').find('.remove-model').removeClass('disabled');
        } else {
            $(this).closest('.multi-fields').find('.remove-model').addClass('disabled');
        }

        $wrapper.animate({ scrollTop: 0 }, 'slow');
        
    });
    $('.multi-fields-wrapper').each(function(i, obj) {
        if($(obj).attr('data-index') == 0) {
           $(obj).parents('.multi-fields').find('.jslink').trigger('click');
        }
    });
    $('.search-input').on('input', function() {
        var searchText = $(this).val().toLowerCase();
        $(this).parent().parent().parent().find('.model-container').each(function() {
            var containerText = $(this).find('input[type="text"], input[type="hidden"]').map(function() {
                    return $(this).val().toLowerCase();
                }).get().join(' ');
                $(this).toggle(containerText.indexOf(searchText) > -1);
        });
    });
    $('body').on('click', '.custom-preview-button', function(e) {
        e.preventDefault();
        
        const $parent = $(this).closest('.multi-fields');
        const $wrappers = $parent.find('.multi-fields-wrapper');
        const collection = [];
        
        $wrappers.find('.model-container').each(function() {
            let item = {}; 
            
            $(this).find('input, select').each(function() {
                const $input = $(this);
                const idPrefix = $parent.attr('data-section') + '_';
                const nameParts = $input.attr('id').replace(idPrefix, '').split("_");
                if (nameParts[1] !== 'type') {
                    const name = nameParts[1];
                    const language = nameParts[2];
                    const value = $input.val().replace(/^.*\\/, "");
                    if (language) {
                        if (!item[name]) {
                            item[name] = {};
                        }
                        item[name][language] = value;
                    } else {
                        item[name] = value;
                    }
                }
            });
            
            collection.push(item);
        });
    
        // Set the JSON string in the textarea
        $('.multi-fields-textarea').val(JSON.stringify(collection, null, 4));
    });
    

    $(".multi-field-btn-copy").click(function(e){
        e.preventDefault();
        $(".multi-fields-textarea").select();
        document.execCommand('copy');
    });

    $('body').on('click', '.custom-file-label', function(e) {
        e.preventDefault();
        var $id = 'select-' + $(this).closest('.multi-fields').attr('data-section');
        var fieldId = $(this).closest('.multi-fields').attr('data-section');
        $('#' + $id).modal('show');
        $('#' + $id).find('.preview-img-valider').attr("data-input", $(this).parent().find('input').attr('id'))
        $(".model-image-picker").imagepicker({hide_select: true, show_label: true});
        $('#mediaSearch-'+fieldId).on('input', function() {
            var query = $(this).val().toLowerCase();
            $('.image_picker_selector li').each(function() {
                var img = $(this).find('.image_picker_image');
                if (img.length > 0) {
                    var mediaName = img.attr('data-name').toLowerCase();
                    if (mediaName && mediaName.includes(query)) { 
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                }
            });
        });
    });

    $(".preview-img-valider").on("click", function() {
        var fieldId = $(this).attr('data-media');
        var selectedImg = $("#select-"+fieldId+" .image_picker_selector .selected .image_picker_image").attr('src');
        var mediaName = selectedImg.split('/').pop();
        fieldId=fieldId.replace("_input","");
        if ($.type(selectedImg) == 'string') {
            $("#" + fieldId).val(selectedImg);
            $('#' + $(this).attr('data-input')).closest('.preview-media-block').find('.preview-block-image').remove();
            $('#' + $(this).attr('data-input')).closest('.preview-media-block').append(`
                    <div class="preview-block-image">
                        <div class="d-flex">
                            <a class="preview-media-link" target="_blank" href="${selectedImg}">
                                <img src="${selectedImg}" class="preview-media-multi-field">
                            </a> 
                            <i class="fas fa-trash preview-media-delete"></i>
                        </div>
                        <span class="preview-media-name">${mediaName}</span>
                    </div>
                `);
                const fileInput = document.querySelector('#' + $(this).attr('data-input'));

                const myFile = new File([''], selectedImg);
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(myFile);
                //fileInput.files = dataTransfer.files;
                var $inputId = $(this).attr('data-input').replace('_input', '');
                $('#' + $inputId).val(selectedImg);

        } else {
            $("#delete-"+ fieldId).hide();
            $("#preview-"+ fieldId).hide();
        }
        $(".close").click();
    });

    $('body').on('click', '.preview-media-delete', function(e) {
        e.preventDefault();
        var $id = $(this).closest('.preview-media-block').find('input').attr('id');
        $(this).closest('.preview-media-block').find('.preview-block-image').remove();
        $("#" + $id).val(null);
        var $inputId = $id.replace('_input', '');
        $('#' + $inputId).val('');
    });

    $(".multi-fields-wrapper").animate({ scrollTop: $(".multi-fields-div .invalid-feedback").closest('.multi-fields-div').position()?.top - 100});
    $('.image-type-error').closest('.multi-fields-div').find('.invalid-feedback').closest('.multi-fields-div').addClass('mt-3 mb-1');
    $(window).keydown(function(event){
        if(event.keyCode == 13) {
            event.preventDefault();
        }
    });

    $('body').on('click', 'button[type=submit]', function(e) {
        var current = e;
        var requiredFields = [];
        $(this).closest('form').find('input[type=file]').each(function(e) {
            var inputFileLink = $(this).attr('id').replace('_input', '');
            if($('#' + inputFileLink).prop('required') && $('#' + inputFileLink).val() == '') {
                current.preventDefault();
                var fieldName = $('#' + inputFileLink).attr('name');
                fieldName = ( fieldName.split('[')[1] ? fieldName.split('[')[1].replace(']', '') : fieldName );
                requiredFields.push(fieldName);
            }
        });

        if(requiredFields.length > 0) {
            alert('Media File Field(s) Required:\n' + requiredFields.join(',\n'));
        }
    });

    $('.multi-fields .feature-label').on('click', function(e) {
        e.preventDefault();
    });
    
    $('.custom-multifield-font-label').on('click', function(e) {
        e.preventDefault();
    })
});