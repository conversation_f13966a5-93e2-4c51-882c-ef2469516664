<?php


namespace App\Controller;

use App\Form\DebugApiFormType;
use App\Service\DebugConsoleManager;
use App\Service\MongoAtlasQueryService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Psr\Log\LoggerInterface;


#[Route(path: '/debug_api', name: 'debug_api_')]
class DebugApiController extends AbstractController
{
    public function __construct(
        private MongoAtlasQueryService $mongoService,
        private HttpClientInterface $httpClient,
        private LoggerInterface $logger,
        private String $baseURL
    ) {}

    #[Route(path: '/', name: 'form')]
    public function index(Request $request, DebugConsoleManager $debugConsoleManager): Response
    {
        // Clear globalParams cookie on page refresh/load
        $response = new Response();
        $response->headers->clearCookie('globalParams', '/');

        $form = $this->createForm(DebugApiFormType::class);
        $form->handleRequest($request);

        $mongoData = null;
        $vehicles = [];
        $error = null;
        $param = [];

        if ($form->isSubmitted() && $form->isValid()) {
            $formData = $form->getData();
            $param = $formData;
            if($param['country']){
                $param['country'] = strtoupper($param['country']->getCode());
            }
            if($param['language']){
                $param['language'] = strtolower($param['language']->getCode());
            }
            if($param['language'] && $param['country']){
                $param['culture'] = $param['language'] . '-' . $param['country'];
            }
            if($param['source']){
                $param['source'] = strtolower($param['source']->getName());
            }
            if($param['brand']){
                $param['brand'] = strtoupper($param['brand']->getCode());
            }
            if($param['source']){
                $param['source'] = strtoupper($param['source']);
            }
            if($param['userID']){
                $param['userId'] = $param['userID'];
            }

            // Build MongoDB filter based on form data
            $filter = $debugConsoleManager->buildMongoFilter($formData);

            // Query the userData collection
            $result = $this->mongoService->find('userData', $filter);

            if ($result->getCode() === 200) {
                $mongoData = json_decode($result->getData(), true);

                // Extract vehicles from all documents with enhanced information
                if (isset($mongoData['documents']) && is_array($mongoData['documents'])) {
                    foreach ($mongoData['documents'] as $index => $document) {
                        if (isset($document['vehicle']) && is_array($document['vehicle'])) {
                            $mongoVehicle=[];
                            foreach ($document['vehicle'] as $vehicle) {
                                if ($formData['vin'] && $formData['vin'] != $vehicle['vin']) continue;
                                $vehicles[] = [
                                    'vin' => $vehicle['vin'] ?? 'N/A',
                                    'label' => $debugConsoleManager->generateVehicleLabel($vehicle, $document),
                                    'versionId' => $vehicle['versionId'] ?? 'N/A',
                                    'brand' => $vehicle['brand'] ?? 'N/A',
                                    'userId' => $document['userId'] ?? 'N/A',
                                    'userEmail' => $document['profile']['email'] ?? 'N/A',
                                    'userCountry' => $document['profile']['country'] ?? 'N/A',
                                    'userName' => ($document['profile']['firstName'] ?? '') . ' ' . ($document['profile']['lastName'] ?? ''),
                                    'mileage' => $vehicle['mileage']['value'] ?? 'N/A',
                                    'mileageUnit' => $vehicle['mileage']['unit'] ?? 'km',
                                    'featureCodesCount' => count($vehicle['featureCode'] ?? [])
                                ];
                                $mongoVehicle[] = $vehicle;
                            }
                            $mongoData['documents'][$index]['vehicle'] = $mongoVehicle;
                        }
                    }
                }
            } else {
                $error = [
                    'message' => $result->getData(),
                    'code' => $result->getCode()
                ];
            }
        }

        // Load external APIs configuration
        $configPath = $this->baseURL . '/uploads/GLOBAL/api_config.yaml';
        $externalApis = $debugConsoleManager->loadExternalApisConfig($configPath);

        // Render the template and set it to the response that clears the cookie
        $response->setContent($this->renderView('debug_api/index.html.twig', [
            'form' => $form->createView(),
            'mongoData' => $mongoData,
            'vehicles' => $vehicles,
            'error' => $error,
            'jsonData' => $mongoData ? json_encode($mongoData) : null,
            'externalApis' => $externalApis,
            'globalParam' => $param
        ]));

        return $response;
    }

    /**
     * Simple API proxy to avoid CORS issues
     */
    #[Route(path: '/proxy', name: 'proxy', methods: ['GET'])]
    public function proxy(Request $request): JsonResponse
    {
        try {
            $targetUrl = $request->query->get('url');
            if (!$targetUrl) {
                return new JsonResponse(['error' => 'Missing URL parameter'], 400);
            }

            // Extract headers from query parameter
            $headersParam = $request->query->get('headers');
            $customHeaders = [];

            if ($headersParam) {
                $decodedHeaders = json_decode($headersParam, true);
                if ($decodedHeaders && is_array($decodedHeaders)) {
                    $customHeaders = $decodedHeaders;
                }
            }

            // Merge with default headers
            $headers = array_merge([
                'Accept' => 'application/json',
                'User-Agent' => 'Space-BO-Debug-Tool/1.0'
            ], $customHeaders);

            $this->logger->info('Debug API Proxy Request', [
                'url' => $targetUrl,
                'headers' => $headers
            ]);

            $response = $this->httpClient->request('GET', $targetUrl, [
                'headers' => $headers,
                'timeout' => 30
            ]);

            $statusCode = $response->getStatusCode();
            $content = $response->getContent(false);

            // Try to decode JSON
            $jsonData = null;
            try {
                $jsonData = json_decode($content, true);
            } catch (\Exception $e) {
                $jsonData = ['raw_content' => $content];
            }

            return new JsonResponse($jsonData ?: ['raw_content' => $content], 200);
        } catch (\Exception $e) {
            $this->logger->error('Debug API Proxy Error', ['message' => $e->getMessage()]);
            return new JsonResponse([
                'error' => [
                    'message' => $e->getMessage(),
                    'type' => get_class($e)
                ]
            ], 500);
        }
    }
}
