<script>
    var treeUrl = "{{path('admin_media_get_tree', {profile: profile.id})}}";
        mediaDirectoryUrl = "{{ (path('admin_media_directory_medias', {profile: profile.id, id: ':id'})) }}",
        mediaDirectoryAddUrl = "{{ (path('admin_media_directory_add', {profile: profile.id, id: ':id'})) }}",
        mediaDirectoryEditUrl = "{{ (path('admin_media_directory_update', {profile: profile.id, id: ':id'})) }}",
        mediaDirectoryRemoveUrl = "{{ (path('admin_media_directory_remove', {profile: profile.id, id: ':id'})) }}",
        getMediaDetailPath = '{{ (path("admin_media_get", {profile: profile.id, media: ":id"})) }}',
        mediaUploadUrl = "{{ path('admin_media_upload', {profile: profile.id, media: ':id'}) }}",
        deleteMediaUrl = '{{ (path("admin_media_delete", {profile: profile.id, media: ":id"})) }}';
</script>