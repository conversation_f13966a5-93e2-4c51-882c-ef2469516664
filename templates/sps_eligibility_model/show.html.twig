{#
    SPS Eligibility Model Show Template

    This template displays detailed information about a specific Model-based eligibility rule
    for the SPS (Smart Phone Services) system. Model rules define eligibility
    based on vehicle models and model years.

    <AUTHOR>
    @since 2025-06-27
#}
{% extends '_layout/layout.html.twig' %}

{% block title %}{{ 'Model Eligibility Rule Details' | trans }}{% endblock %}

{% block content %}
<div class="container-fluid">
    {# Page Header #}
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-eye me-2"></i>{{ 'Model Eligibility Rule Details' | trans }}
        </h1>
        <div class="btn-group" role="group">
            <a href="{{ path('sps_eligibility_index', {'tab': 'model'}) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>{{ 'Back to List' | trans }}
            </a>
            {% if profile.isSuperAdmin() or profile.isCentralAdministrator() %}
                <a href="{{ path('sps_eligibility_model_edit', {'id': sps_eligibility.id}) }}" class="btn btn-primary">
                    <i class="fas fa-edit me-2"></i>{{ 'Edit' | trans }}
                </a>
            {% endif %}
        </div>
    </div>

    {# Rule Details Card #}
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>{{ 'Rule Information' | trans }}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        {# Model Information #}
                        <div class="col-md-6 mb-4">
                            <h6 class="text-primary fw-bold">{{ 'Vehicle Models' | trans }}</h6>
                            <div class="mt-2">
                                {% for model in sps_eligibility.codes %}
                                    <span class="badge bg-secondary me-2 mb-2">{{ model }}</span>
                                {% endfor %}
                            </div>
                        </div>

                        <div class="col-md-6 mb-4">
                            <h6 class="text-primary fw-bold">{{ 'Model Year From' | trans }}</h6>
                            <div class="mt-2">
                                {% if sps_eligibility.modelYearFrom %}
                                    <span class="badge bg-primary fs-6">{{ sps_eligibility.modelYearFrom }}</span>
                                {% else %}
                                    <span class="text-muted">{{ 'Not specified' | trans }}</span>
                                {% endif %}
                            </div>
                        </div>



                        {# Type and Disclaimer Row #}
                        <div class="col-md-6 mb-3">
                            <h6 class="text-primary fw-bold">{{ 'Type' | trans }}</h6>
                            <span class="badge bg-info fs-6">{{ sps_eligibility.type }}</span>
                        </div>

                        <div class="col-md-6 mb-3">
                            <h6 class="text-primary fw-bold">{{ 'Eligibility Disclaimer' | trans }}</h6>      
                            {% set isYes = (sps_eligibility.eligibilityDisclaimer is same as('YES')) or (sps_eligibility.eligibilityDisclaimer is same as(true)) or (rule.eligibilityDisclaimer is same as('Yes')) %}
                            {% if isYes %}
                                <span class="badge bg-warning fs-6">{{ 'Yes' | trans }}</span>
                            {% else %}
                                <span class="badge bg-success fs-6">{{ 'No' | trans }}</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {# Metadata Card #}
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-clock me-2"></i>{{ 'Metadata' | trans }}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary fw-bold">{{ 'Rule ID' | trans }}</h6>
                        <code>{{ sps_eligibility.id }}</code>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-primary fw-bold">{{ 'Created At' | trans }}</h6>
                        {% if sps_eligibility.createdAt %}
                            <span class="text-muted">
                                {{ sps_eligibility.createdAt|date('Y-m-d H:i:s') }}
                            </span>
                        {% else %}
                            <span class="text-muted">{{ 'Not available' | trans }}</span>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <h6 class="text-primary fw-bold">{{ 'Updated At' | trans }}</h6>
                        {% if sps_eligibility.updatedAt %}
                            <span class="text-muted">
                                {{ sps_eligibility.updatedAt|date('Y-m-d H:i:s') }}
                            </span>
                        {% else %}
                            <span class="text-muted">{{ 'Not available' | trans }}</span>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <h6 class="text-primary fw-bold">{{ 'Scope' | trans }}</h6>
                        <span class="badge bg-success">{{ sps_eligibility.scope }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
