package listeners;
import org.testng.ITestContext;
import org.testng.ITestListener;
import org.testng.ITestResult;
import org.testng.ITestNGMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CustomTestListener implements ITestListener {

    // Create a logger instance for this class
    private static final Logger logger = LoggerFactory.getLogger(CustomTestListener.class);

    @Override
    public void onTestSuccess(ITestResult result) {
        // Iterate through the groups and log them
        String[] groups = result.getMethod().getGroups();
        for (String group : groups) {
            logger.info("Test passed for group: {}", group);
        }
    }

    @Override
    public void onTestFailure(ITestResult result) {
        // Iterate through the groups and log them
        String[] groups = result.getMethod().getGroups();
        for (String group : groups) {
            logger.error("Test failed for group: {}", group);
        }
    }

    @Override
    public void onTestSkipped(ITestResult result) {
        // Iterate through the groups and log them
        String[] groups = result.getMethod().getGroups();
        for (String group : groups) {
            logger.warn("Test skipped for group: {}", group);
        }
    }

    @Override
    public void onFinish(ITestContext context) {
        // Log the test suite name or class name
        logger.info("Finished executing test suite: {}", context.getName());

        // Loop through all the test methods in the suite
        ITestNGMethod[] methods = context.getAllTestMethods();
        for (ITestNGMethod method : methods) {
            // Log the name of each test method
            logger.info("Test name: {}", method.getMethodName());
        }
    }

    @Override
    public void onStart(ITestContext context) {
        // Log the start of the test suite
        logger.info("Starting test suite: {}", context.getName());
    }
}
