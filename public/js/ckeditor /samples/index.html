<!DOCTYPE html>
<!--
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
-->
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>CKEditor Sample</title>
	<script src="../ckeditor.js"></script>
	<script src="js/sample.js"></script>
	<link rel="stylesheet" href="css/samples.css">
	<link rel="stylesheet" href="toolbarconfigurator/lib/codemirror/neo.css">
	<meta name="viewport" content="width=device-width,initial-scale=1">
	<meta name="description" content="Try the latest sample of CKEditor 4 and learn more about customizing your WYSIWYG editor with endless possibilities.">
</head>
<body id="main">

<nav class="navigation-a">
	<div class="grid-container">
		<ul class="navigation-a-left grid-width-70">
			<li><a href="https://ckeditor.com/ckeditor-4/">Project Homepage</a></li>
			<li><a href="https://github.com/ckeditor/ckeditor4/issues">I found a bug</a></li>
			<li><a href="https://github.com/ckeditor/ckeditor4" class="icon-pos-right icon-navigation-a-github">Fork CKEditor on GitHub</a></li>
		</ul>
		<ul class="navigation-a-right grid-width-30">
			<li><a href="https://ckeditor.com/blog/">CKEditor Blog</a></li>
		</ul>
	</div>
</nav>

<header class="header-a">
	<div class="grid-container">
		<h1 class="header-a-logo grid-width-30">
			<a href="index.html"><img src="img/logo.svg" onerror="this.src='img/logo.png'; this.onerror=null;" alt="CKEditor Sample"></a>
		</h1>

		<nav class="navigation-b grid-width-70">
			<ul>
				<li><a href="index.html" class="button-a button-a-background">Start</a></li>
				<li><a href="toolbarconfigurator/index.html" class="button-a">Toolbar configurator <span class="balloon-a balloon-a-nw">Edit your toolbar now!</span></a></li>
			</ul>
		</nav>
	</div>
</header>

<main>
	<div class="adjoined-top">
		<div class="grid-container">
			<div class="content grid-width-100">
				<h1>Congratulations!</h1>
				<p>
					If you can see CKEditor below, it means that the installation succeeded.
					You can now try out your new editor version, see its features, and when you are ready to move on, check some of the <a href="#sample-customize">most useful resources</a> recommended below.
				</p>
			</div>
		</div>
	</div>
	<div class="adjoined-bottom">
		<div class="grid-container">
			<div class="grid-width-100">
				<div id="editor">
					<h1>Hello world!</h1>
					<p>I'm an instance of <a href="https://ckeditor.com">CKEditor</a>.</p>
				</div>
			</div>
		</div>
	</div>

	<div class="grid-container">
		<div class="content grid-width-100">
			<section id="sample-customize">
				<h2>Customize Your Editor</h2>
				<p>Modular build and <a href="https://ckeditor.com/docs/ckeditor4/latest/guide/dev_configuration.html">numerous configuration options</a> give you nearly endless possibilities to customize CKEditor. Replace the content of your <code><a href="../config.js">config.js</a></code> file with the following code and refresh this page (<strong>remember to clear the browser cache</strong>.html)!</p>
		<pre class="cm-s-neo CodeMirror"><code><span style="padding-right: 0.1px;"><span class="cm-variable">CKEDITOR</span>.<span class="cm-property">editorConfig</span> <span class="cm-operator">=</span> <span class="cm-keyword">function</span>( <span class="cm-def">config</span> ) {</span>
<span style="padding-right: 0.1px;"><span class="cm-tab">	</span><span class="cm-variable-2">config</span>.<span class="cm-property">language</span> <span class="cm-operator">=</span> <span class="cm-string">'es'</span>;</span>
<span style="padding-right: 0.1px;"><span class="cm-tab">	</span><span class="cm-variable-2">config</span>.<span class="cm-property">uiColor</span> <span class="cm-operator">=</span> <span class="cm-string">'#F7B42C'</span>;</span>
<span style="padding-right: 0.1px;"><span class="cm-tab">	</span><span class="cm-variable-2">config</span>.<span class="cm-property">height</span> <span class="cm-operator">=</span> <span class="cm-number">300</span>;</span>
<span style="padding-right: 0.1px;"><span class="cm-tab">	</span><span class="cm-variable-2">config</span>.<span class="cm-property">toolbarCanCollapse</span> <span class="cm-operator">=</span> <span class="cm-atom">true</span>;</span>
<span style="padding-right: 0.1px;">};</span></code></pre>
			</section>

			<section>
				<h2>Toolbar Configuration</h2>
				<p>If you want to reorder toolbar buttons or remove some of them, check <a href="toolbarconfigurator/index.html">this handy tool</a>!</p>
			</section>

			<section>
				<h2>More Samples!</h2>
				<p>Visit the <a href="https://ckeditor.com/docs/ckeditor4/latest/examples/index.html">CKEditor Examples</a> for a huge collection of samples showcasing editor features, with source code readily available to copy and use in your own implementation.</p>
			</section>

			<section>
				<h2>Developer's Guide</h2>
				<p>The most important resource for all developers working with CKEditor, integrating it with their websites and applications, and customizing to their needs. You can start from here:</p>
				<ul>
					<li><a href="https://ckeditor.com/docs/ckeditor4/latest/guide/dev_installation.html">Getting Started</a> &ndash; Explains most crucial editor concepts and practices as well as the installation process and integration with your website.</li>
					<li><a href="https://ckeditor.com/docs/ckeditor4/latest/guide/dev_advanced_installation.html">Advanced Installation Concepts</a> &ndash; Describes how to upgrade, install additional components (plugins, skins.html), or create a custom build.</li>
				</ul>
					<p>When you have the basics sorted out, feel free to browse some more advanced sections like:</p>
				<ul>
					<li><a href="https://ckeditor.com/docs/ckeditor4/latest/features/index.html">Features Overview</a> &ndash; Descriptions and samples of various editor features.</li>
					<li><a href="https://ckeditor.com/docs/ckeditor4/latest/guide/plugin_sdk_intro.html">Plugin SDK</a>, <a href="https://ckeditor.com/docs/ckeditor4/latest/guide/widget_sdk_intro.html">Widget SDK</a>, and <a href="https://ckeditor.com/docs/ckeditor4/latest/guide/skin_sdk_intro.html">Skin SDK</a> &ndash; Useful when you want to create your own editor components.</li>
				</ul>
			</section>

			<section>
				<h2>CKEditor JavaScript API</h2>
				<p>CKEditor boasts a rich <a href="https://ckeditor.com/docs/ckeditor4/latest/api/index.html">JavaScript API</a> that you can use to adjust the editor to your needs and integrate it with your website or application.</p>
			</section>
		</div>
	</div>
</main>

<footer class="footer-a grid-container">
	<div class="grid-container">
		<p class="grid-width-100">
			CKEditor &ndash; The text editor for the Internet &ndash; <a class="samples" href="https://ckeditor.com/">https://ckeditor.com</a>
		</p>
		<p class="grid-width-100" id="copy">
			Copyright &copy; 2003-2023, <a class="samples" href="https://cksource.com/">CKSource</a> Holding sp. z o.o. All rights reserved.
		</p>
	</div>
</footer>
<script>
	initSample();
</script>

</body>
</html>
