<?php

namespace App\Service;

use App\Entity\Brand;
use App\Entity\Country;
use App\Entity\Media;
use App\Entity\MediaDirectory;
use App\Entity\Profile;
use App\Entity\Site;
use App\Helpers\MediaErrorResponse;
use App\Helpers\FileHelper;
use App\Helpers\IMediaResponseArrayFormat;
use App\Model\MediaError;
use App\Repository\MediaDirectoryRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use App\Helpers\LoggerTrait;
use Aws\Result;

class MediaManager
{
    use LoggerTrait;

    public const GLOBAL_BRANDS = ['XX', 'CT', ''];
    public const PATH_SEPARATOR = '>';
    public const MAIN_FOLDERS_LEVEL = 3; // RACINE > BRAND > COUNTRY
    public const FOLDER = 'uploads';
    public const GLOBAL_CHANNEL = 'GLOBAL';
    public const LOCAL_CHANNEL = 'BRAND';

    public function __construct(
        private EntityManagerInterface $em,
        private MediaDirectoryRepository $mediaDirectoryRepository,
        private ValidatorInterface $validator,
        private string $mediaUrl,
        private TranslatorInterface $translator,
        private AWSS3Service $s3Service,
        private string $mediaBucket,
        private string $settingsBucket,
        private string $distributionId,
        private AWSCloudFrontService $cloudFrontService
    ) {}

    public function createDirectory(
        MediaDirectory $parent,
        ?Site $site,
        string $directoryName,
        bool $forceCreation = false
    ): ?MediaDirectory {

        $directory = $this->addDirectory($parent, $site, $directoryName, $forceCreation);
        if ($directory) {
            $this->em->persist($directory);
            $this->em->flush();
        }
        return $directory;
    }

    public function createDirectoryFromSite(
        MediaDirectory $parent,
        Site $site,
        string $directoryName,
        bool $forceCreation = false,
        string $directoryCode = null
    ): ?MediaDirectory {
        return $this->addDirectory($parent, $site, $directoryName, $forceCreation, $directoryCode);
    }

    public function updateDirectory(MediaDirectory $directory, string $directoryName): ?MediaDirectory
    {
        if ($directory->getReadOnly()) {
            throw new \Exception('Cannot update a readOnly Folder');
        }
        $oldPath = $this->getDir($directory?->getPath());
        $newPath = $this->getDir($directory->getParentDirectory()->getPath() . " > {$directoryName}");
        $directory = $directory
            ->setPath($directory->getParentDirectory()->getPath() . " > {$directoryName}")
            ->setLabel($directoryName)
            ->setName($directoryName);
        if (
            !$this->validator->validate($directory)->count() && 
            !$this->isSystemDirectory($directory)
        ) {
            $this->editS3Folder($newPath, $oldPath, $directory);
            $this->em->flush();
            return $directory;
        }
        return null;
    }

    public function deleteDirectory(MediaDirectory $directory): bool
    {
        try {
            if ($directory->getReadOnly()) {
                throw new \Exception('Cannot delete a readOnly Folder');
            }
            $this->em->remove($directory);
            $this->em->flush();
            return true;
        } catch (\Exception $e) {
            $this->logger->error("MediaManager::delete exception {$e->getMessage()}");
        }
        return false;
    }

    public function getTreeStructure(Profile $profile): array
    {
        $site = $profile->getSite();
        $brand = $site->getBrand()?->getCode() ?? $profile->getBrand()?->getCode();
        $country = $site->getCountry()?->getCode() ?? '';
        $directories = $this->mediaDirectoryRepository->getMediaDirectories($brand, $country, $profile);
        return $this->buildTree($directories);
    }

    public function getMedias(
        MediaDirectory $mediaDirectory,
        int $offset,
        int $limit,
        bool $recalculateTotal = false,
        string $search = ''
    ): array
    {
        $mediaRepository = $this->em->getRepository(Media::class);
        $dbMedias = $mediaRepository->getMediasPaginated($mediaDirectory, $offset, $limit, $search);
        $totalMedias = $recalculateTotal ? (int) $mediaRepository->getMediasCount($mediaDirectory) : null;
        $medias = [];
        foreach ($dbMedias as $media) {
            $medias[] = [
                'id' => $media['id'],
                'path' => $this->mediaUrl . '/' . $media['path'],
                'createdBy' => $media['createdBy'],
                'extension' => $media['extension'],
                'role' =>  $media['role_name'] ?? '',
                'size' =>  $media['size'] ?? 0,
            ];
        }
        return [
            'medias' => $medias,
            'totalMedias' => $totalMedias,
        ];
    }

    public function getMediaDetails(Media $media): array
    {
        $media = [
            'id' => $media->getId(),
            'path' => $this->mediaUrl . '/' . $media->getPath(),
            'name' => $media->getName(),
            'extension' => $media->getExtension(),
            'textAlt' => $media->getTextAlt(),
            'copyright' => $media->getCopyright(),
            'size' => $media->getSize(),
            'createdAt' => date_format($media->getCreationDate(), 'd/m/Y'),
            'comment' => $media->getComment(),
            'parentPath' => $media->getParentDirectory() ? $media->getParentDirectory()->getPath() : 'n/d',
            'folderPath' => $this->getFolderPath($media),
            'parentDirectoryId' => $media->getParentDirectory()?->getId() ?? null,
            'createdBy' => $media->getCreatedBy(),
        ];
        return $media;
    }

    public function isSystemDirectory(MediaDirectory $mediaDirectory)
    {
        $pathArray = explode(self::PATH_SEPARATOR, $mediaDirectory->getPath());
        // the path will be like "RACINE > BRAND > COUNTRY > SUBFOLDERS..."
        if (count($pathArray) > self::MAIN_FOLDERS_LEVEL || self::GLOBAL_CHANNEL == $mediaDirectory?->getChannel()?->getChannelType()?->getName()) {
            return false;
        }
        return true;
    }

    public function manageErrors(
        $files,
        string $selectedLanguage,
        Media $media,
        MediaDirectory $mediaDirectory,
        bool $toErase = false
    ): IMediaResponseArrayFormat {
        $mediaName = $media->getName();
        $mediaErrorResponse = new MediaErrorResponse();
        $oldFilePath = $media->getPath();
        if ($this->isFileExceeds($files)) {
            $this->logger->error('The uploaded file exceeds the upload_max_filesize directive in php.ini');
            $error = (new MediaError())
                ->setMessage(
                    $this->translator->trans(FileHelper::getFileUploadError(1), [], null, $selectedLanguage)
                )->setCode(JsonResponse::HTTP_UNPROCESSABLE_ENTITY);
            $mediaErrorResponse->addError($error);
            return $mediaErrorResponse;
        }
        foreach ($files as $file) {
            $name = $this->getFileName($file->getClientOriginalName(), $mediaName);
            $filename = pathinfo($name, PATHINFO_FILENAME);
            $dir = $this->getDir($mediaDirectory?->getPath());
            $newFilePath = $dir . '/' . $name;
            $mediaId = $media->getId();
            $fileErrorCode = $file->getError();
            if ($this->isFileHasError($file)) {
                $this->logger->error('Error: ' . FileHelper::getFileUploadError($fileErrorCode));
                $error = (new MediaError())
                    ->setMessage(
                        "{$file->getClientOriginalName()} : {$this->translator->trans(FileHelper::getFileUploadError($fileErrorCode), [], null,$selectedLanguage)} !"
                    )->setCode(JsonResponse::HTTP_UNPROCESSABLE_ENTITY);
                $mediaErrorResponse->addError($error);
            }
            if (!$this->isValidFileName($filename)) {
                $this->logger->error("Error: {$filename} invalid file name");
                $error = (new MediaError())
                    ->setMessage(
                        "{$filename} : {$this->translator->trans("error_media_validation", [], null,$selectedLanguage)} !"
                    )->setCode(JsonResponse::HTTP_UNPROCESSABLE_ENTITY);
                $mediaErrorResponse->addError($error);
            }
            if ($this->checkIfFileExistedInS3Bucket($oldFilePath, $newFilePath, $mediaId, $toErase)) {
                $this->logger->error("Error: {$name} this name already exists");
                $error = (new MediaError())
                    ->setMessage(
                        "{$name} : {$this->translator->trans("name_exists", [], null,$selectedLanguage)} !"
                    )->setCode(JsonResponse::HTTP_CONFLICT)
                    ->setName($name);
                $mediaErrorResponse->addError($error);
            }
        }
        return $mediaErrorResponse;
    }

    public function addFile(
        Profile $profile,
        $file,
        MediaDirectory $mediaDirectory,
        string $createdBy,
        Media $media,
        $path
    ): Media {
        $this->putFileInS3($path, $file->getPathname(), $file?->getClientMimeType() ?? null);
        return $this->addMediaInDB($profile, $file, $mediaDirectory, $createdBy, $media);
    }
    public function versioning(string $pathFile, string $mimeType = "application/x-yaml"): void
    {
        //timestamp versioning
        $versioningPath = "API/versioning/" . 'v-' . time() . "/api_config.yaml" ;
        $this->putFileInSettingS3($versioningPath, $pathFile, $mimeType);
        
    }

    public function editFile(Media $media, string $newPath, string $oldPath, $file): Media
    {
        $this->editS3File($newPath, $oldPath, $file->getPathname(), $file?->getClientMimeType() ?? null);
        $extension = pathinfo($newPath, PATHINFO_EXTENSION);
        $size = $file->getSize();
        return $this->upadteMediaInDB($media, $newPath, $extension, $size);
    }

    public function toErease(
        MediaDirectory $mediaDirectory,
        string $newPath,
        string $oldPath,
        string $pathFile,
        Media $media,
        $size,
        string $mimeType = null,
        $isUpdateAction = false
    ): void
    {
        $toInvalidate = [];
        $deletePath = $newPath;
        if ($isUpdateAction) {
            $deletePath = $oldPath;
            $this->em->remove($media);
            $toInvalidate = [$oldPath, $newPath];
        }
        $this->deleteFileFromS3($deletePath, false);
        $this->putFileInS3($newPath, $pathFile, $mimeType);
        $mediaDirectory->getMedias()->filter(function ($e) use ($newPath, $media, $size) {
            if ($e->getPath() == $newPath) {
                $e->setCopyright($media->getCopyright());
                $e->setComment($media->getComment());
                $e->setTextAlt($media->getTextAlt());
                $extension = pathinfo($newPath, PATHINFO_EXTENSION);
                $e->setExtension($extension);
                $e->setSize($size);
                return $e;
            }
            return false;
        });
        $this->em->flush();
        if ($toInvalidate) {
            $this->invalidateCloudFront($toInvalidate);
        }
    }

    public function isFileExistInS3Bucket(string $newPath): bool
    {
        return $this->s3Service->objectExist($this->mediaBucket, $newPath);
    }

    public function deleteFileFromS3(string $path, $withInvalidation = true): void
    {
        $this->s3Service->deleteObject(
            $this->mediaBucket,
            $path
        );
        if ($withInvalidation) {
            $this->invalidateCloudFront([$path]);
        }
    }

    public function deleteMedia(Media $media): void
    {

        if ($this->isFileExistInS3Bucket($media->getPath())) {
            $this->deleteFileFromS3($media->getPath());
        }        
        $this->em->remove($media);
        $this->em->flush();
    }

    public function hasGrantedToDoActions(Media $media, Profile $profile): bool
    {
        $hasSameRole = $media?->getProfile()?->getRole()?->getName() == $profile?->getRole()?->getName();
        $isChannelDirectory = $media?->getParentDirectory()?->getParentDirectory() == null;
        return (
            $hasSameRole
            || (($isChannelDirectory && $hasSameRole) || $profile->isSuperAdmin())
            || ($profile->isGlobalAndCentralAdministrator() && !$isChannelDirectory)
        );
    }

    public function getFileFromS3Bucket($path): Result
    {
        return $this->s3Service->getObject($this->mediaBucket, $path);
    }

    private function upadteMediaInDB(Media $media, string $newPath, string $extension, $size): Media
    {
        $media->setPath($newPath)
        ->setExtension($extension)
        ->setSize($size);
        $this->em->flush();
        return $media;
    }

    private function addMediaInDB(
        Profile $profile,
        $file,
        MediaDirectory $mediaDirectory,
        string $createdBy,
        Media $media
    ): Media
    {
        $repo = $this->em->getRepository(Media::class);
        return  $repo->uploadOne($profile, $file, $mediaDirectory, $createdBy, $media);
    }

    private function putFileInS3(string $path, $pathname, ?string $mimeType = null): void
    {
        $this->s3Service->putObject(
            $path,
            fopen($pathname, 'r'),
            $this->mediaBucket,
            $mimeType
        );
    }

    private function putFileInSettingS3(string $path, $pathname, ?string $mimeType = null): void
    {
        $this->s3Service->putObject(
            $path,
            fopen($pathname, 'r'),
            $this->settingsBucket,
            $mimeType
        );
    }

    private function editS3File(string $newPath, string $oldName, string $pathname, string $mimeType = null): void
    {
        if ($this->isFileExistInS3Bucket($newPath) && $newPath != $oldName) {
            $this->deleteFileFromS3($newPath);
            $this->s3Service->copyObject(
                $this->mediaBucket,
                $newPath,
                "{$this->mediaBucket}/{$oldName}"
            );
            $this->deleteFileFromS3($oldName, false);
            $this->invalidateCloudFront([$newPath, $oldName]);
        } else {
            $this->deleteFileFromS3($oldName);
            $this->putFileInS3($newPath, $pathname, $mimeType);
        }
    }

    private function getFolderPath(Media $media): string
    {
        $site = $media->getParentDirectory()->getSite() ?? null;
        $path =  str_replace(' ', '', $media?->getParentDirectory()?->getPath());
        $pathArray = explode(self::PATH_SEPARATOR, $path);
        if (!$site) {
            $pathArray[1] = $media->getParentDirectory()->getName() ??  $pathArray[1];
        } else {
            $brandName = $this->em->getRepository(Brand::class)->findOneBy(['code' => trim($pathArray[1])])?->getName();
            $countryName = $this->em->getRepository(Country::class)->findOneBy(['code' => trim($pathArray[2])])?->getName();
            $pathArray[1] = $brandName ??  $pathArray[1];
            $pathArray[2] = $countryName ?? $pathArray[2];
        }
        return implode(' ' . self::PATH_SEPARATOR . ' ', $pathArray);
    }

    private function isFileExceeds($files): bool
    {
        return empty($files);
    }

    private function isFileHasError($file)
    {
        return $file->getError() ?? false;
    }

    private function isValidFileName(?string $name = '')
    {
        return  !preg_match('/[^A-Z0-9\-\_]/i', $name);
    }

    private function getFileName(string $originName, ?string $mediaName): string
    {
        $extension = pathinfo($originName, PATHINFO_EXTENSION);
        return $mediaName ? $mediaName . '.' . $extension : $originName;
    }

    private function checkIfFileExistedInS3Bucket(
        ?string $oldFilePath,
        ?string $newFilePath,
        ?int $mediaId,
        bool $toErase = false
    ): bool
    {
        return $this->isFileExistInS3Bucket($newFilePath) && !$mediaId && !$toErase ||
            ($this->isFileExistInS3Bucket($newFilePath) && $mediaId && $oldFilePath != $newFilePath && !$toErase);
    }

    private function getDir(?string $path): string
    {
        return self::FOLDER . '/' . str_replace(' > ', '/', $path);
    }

    private function buildTree(array $data) {
        $directories = [];
        $actionsBtns = [];
        foreach ($data as $value) {
            $numberFolders = count(explode('>', $value['path']));
            $isAddedButton = $numberFolders > 3 || ($value['channel'] == self::GLOBAL_CHANNEL && $numberFolders == 2);
            $directories[] = [
                'label' => $value['label'],
                'text' => $value['text'],
                'id' => $value['id'],
                'path' => $value['path'],
                'isActionsBtn' => $isAddedButton,
                'parent' => $value['parent'] ?? '#',
                'isChannelDirectory' => $value['parent']  == null,
                'channelType' => $value['channel'] ?? ''
            ];

            if ($numberFolders >= 3 || ($value['channel'] == self::GLOBAL_CHANNEL && $numberFolders == 1)) {
                $actionsBtns[] = [
                    'text' => '<i class="fas fa-folder-plus fa-lg pl-1 color-add add-folder"></i>',
                    'icon' => false,
                    'id' => 'add' . uniqid(),
                    'parent' => $value['id'],
                    'channel' => null,
                    'add' => true,
                    'state' => ['disabled' => true]
                ];
            }
        }
        
        return array_merge($directories, $actionsBtns);
    }

    private function editS3Folder(string $newPath, string $oldPath, MediaDirectory $directory): void
    {
        if($newPath != $oldPath){
            $objects = $this->s3Service->listObjectsV2($this->mediaBucket, $oldPath);
            if (!empty($objects['Contents'])) {
                foreach ($objects['Contents'] as $object) {
                    $sourceKey = $object['Key'];
                    $fileName = pathinfo($sourceKey, PATHINFO_BASENAME);
                    $this->s3Service->copyObject(
                        $this->mediaBucket,
                        "{$newPath}/{$fileName}",
                        "{$this->mediaBucket}/{$sourceKey}"
                    );
                    $this->deleteFileFromS3($sourceKey);
                }

                $directory->getMedias()->filter(function ($media) use ($newPath) {
                    $media->setPath("{$newPath}/{$media->getName()}.{$media->getExtension()}");
                });
        
            }
        }
    }

    private function invalidateCloudFront(array $pathsToInvalidate = []){
        $prefix = '/';
        $pathsToInvalidate = array_map(fn($path) => "{$prefix}{$path}", $pathsToInvalidate);
        return $this->cloudFrontService->invalidateCloudFront($this->distributionId, $pathsToInvalidate);
    }

    private function addDirectory(MediaDirectory $parent, ?Site $site, string $directoryName, bool $forceCreation = false, string $directoryCode = null)
    {
        $label = $directoryCode ?? $directoryName;
        $directory = (new MediaDirectory())
            ->setLabel($label)
            ->setSite($site)
            ->setParentDirectory($parent)
            ->setPath($parent->getPath() . " > {$label }")
            ->setBrand($parent->getBrand())
            ->setChannel($parent->getChannel())
            ->setName($directoryName);
        if (!$this->validator->validate($directory)->count() && ($forceCreation || !$this->isSystemDirectory($directory))) {
            return $directory;
        }

        return null;
    }
}
