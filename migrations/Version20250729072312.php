<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250729072312 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Owners Manual Menu';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql("
            SELECT id INTO @ADMIN_MENU_ID FROM `menu` WHERE label = 'menu_admin';

            INSERT INTO `menu` (`id`, `parent_id`, `label`, `icon_class`, `route_name`, `parameters`, `feature_id`)
            VALUES (NULL, @ADMIN_MENU_ID, 'Owners Manual', 'fas fa-shield-alt', 'owners_manual_list', NULL, NULL);

            SELECT id INTO @DEBUG_MENU_ID FROM `menu` WHERE label = 'Owners Manual';

            SELECT id INTO @SUPER_ADMIN_ID FROM `role` WHERE label = 'Super Administrator';

            INSERT INTO `role_menu` (`id`, `role_id`, `menu_id`, `permission`)
            VALUES (NULL, @SUPER_ADMIN_ID, @DEBUG_MENU_ID, 'W');
        ");
        
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
       
    }


}
