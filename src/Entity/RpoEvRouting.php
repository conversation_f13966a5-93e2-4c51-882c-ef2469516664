<?php

namespace App\Entity;

use App\Repository\RpoEvRoutingRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Validator\Constraints as Assert;

#[ORM\Entity(repositoryClass: RpoEvRoutingRepository::class)]
#[ORM\Table(name: 'rpo_ev_routing')]
#[UniqueEntity(
    fields: ['rpo', 'evRouting'],
    repositoryMethod: 'findOneRpoLabelByBrand',
    message: 'rpolabel_rpo_unique',
    ignoreNull: true
)]
class RpoEvRouting
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[ORM\Column(type: 'string', length: 32, nullable: true)]
    #[Assert\Type(type: 'alnum', message: 'rpolabel_rpo_notvalid')]
    private ?string $rpo = null;

    #[ORM\Column(type: 'datetime', name: 'created_at', options: ['default' => 'CURRENT_TIMESTAMP'])]
    private ?\DateTimeInterface $createdAt = null;

    #[ORM\ManyToOne(targetEntity: EvRouting::class, inversedBy: 'rpos', fetch: 'EAGER')]
    #[ORM\JoinColumn(nullable: false)]
    private ?EvRouting $evRouting = null;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCreatedAt(): ?\DateTimeInterface
    {
        return $this->createdAt;
    }

    public function setCreatedAt(\DateTimeInterface $createdAt): self
    {
        $this->createdAt = $createdAt;
        return $this;
    }

    public function getRpo(): ?string
    {
        return $this->rpo;
    }

    public function setRpo(string $rpo): self
    {
        $this->rpo = $rpo;
        return $this;
    }

    public function getEvRouting(): ?EvRouting
    {
        return $this->evRouting;
    }

    public function setEvRouting(?EvRouting $evRouting): self
    {
        $this->evRouting = $evRouting;
        return $this;
    }
}
