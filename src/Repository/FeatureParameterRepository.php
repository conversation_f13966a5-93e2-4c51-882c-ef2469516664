<?php

namespace App\Repository;

use App\Entity\FeatureParameter;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<FeatureParameter>
 *
 * @method FeatureParameter|null find($id, $lockMode = null, $lockVersion = null)
 * @method FeatureParameter|null findOneBy(array $criteria, array $orderBy = null)
 * @method FeatureParameter[]    findAll()
 * @method FeatureParameter[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class FeatureParameterRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, FeatureParameter::class);
    }

    public function add(FeatureParameter $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(FeatureParameter $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }
}
