<?php

namespace App\Form;

use App\Repository\ChannelRepository;
use Symfony\Component\Form\AbstractType;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Validator\Constraints\NotBlank;

class SettingsFormType extends AbstractType
{

    /**
     * @param EntityManagerInterface $em
     */
    public function __construct(private EntityManagerInterface $em, private ChannelRepository $channelRepository) {}

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $channelMap = $this->channelRepository->getChannelMap();
        $builder->add('source', ChoiceType::class, [
            'choices' => $channelMap,
            'required' => true,
            'multiple' => true,
            'mapped' => true,
            'constraints' => [
                new NotBlank(),
            ]
        ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            //Set the data class here
        ]);
    }
}
