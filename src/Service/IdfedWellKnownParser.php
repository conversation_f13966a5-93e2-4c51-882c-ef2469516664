<?php

namespace App\Service;

use Drenso\OidcBundle\OidcWellKnownParserInterface;

/**
 * custom OidcWellKnownParser
 */
class IdfedWellKnownParser implements OidcWellKnownParserInterface
{
	const SUPPORTED_CODE_CHALLENGE_METHODS = ['code_challenge_methods_supported' => ['S256','plain']
	];
	
	function parseWellKnown(array $config): array
	{
		if(!isset($config["end_session_endpoint"])){
			if(isset($config["ping_end_session_endpoint"])){
				$config["end_session_endpoint"] = $config["ping_end_session_endpoint"];
			}
		}
		return array_merge($config, self::SUPPORTED_CODE_CHALLENGE_METHODS);
	}
}
