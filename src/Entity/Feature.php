<?php

namespace App\Entity;

use App\Repository\FeatureRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Serializer\Attribute\Groups;

#[ORM\Entity(repositoryClass: FeatureRepository::class)]
class Feature
{
    #[Groups(['package_export'])]
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    #[Groups(['package_export'])]
    #[ORM\Column(type: 'integer')]
    private int $code;

    #[Groups(['package_export'])]
    #[ORM\Column(type: 'string', length: 255)]
    private string $label;

    #[Groups(['package_export'])]
    #[ORM\Column(type: 'string', length: 50, nullable: true)]
    private ?string $brand;

    #[Groups(['package_export'])]
    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $country;

    #[Groups(['package_export'])]
    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $language;

    #[Groups(['package_export'])]
    #[ORM\Column(type: 'string', length: 50)]
    private string $source;

    #[Groups(['package_export'])]
    #[ORM\Column(type: 'boolean')]
    private bool $enabled;

    #[Groups(['package_export'])]
    #[ORM\Column(type: 'boolean')]
    private bool $isEarlyAdopter;

    #[Groups(['package_export'])]
    #[ORM\Column(type: 'array', nullable: true)]
    private ?array $earlyAdoptersList = [];

    #[ORM\ManyToOne(targetEntity: FeatureSetting::class, inversedBy: 'features', cascade: ['persist'])]
    #[ORM\JoinColumn(name: 'code', referencedColumnName: 'id')]
    private ?FeatureSetting $featureSetting = null;

    #[Groups(['package_export'])]
    #[ORM\OneToMany(targetEntity: FeatureParameter::class, mappedBy: 'feature', orphanRemoval: true, cascade: ['persist'])]
    private Collection $featureParameters;

    public function __construct()
    {
        $this->featureParameters = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getCode(): ?int
    {
        return $this->code;
    }

    public function setCode(int $code): self
    {
        $this->code = $code;

        return $this;
    }

    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function setLabel(string $label): self
    {
        $this->label = $label;

        return $this;
    }

    public function getBrand(): ?string
    {
        return $this->brand;
    }

    public function setBrand(?string $brand): self
    {
        $this->brand = $brand;

        return $this;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function setCountry(?string $country): self
    {
        $this->country = $country;

        return $this;
    }

    public function getLanguage(): ?string
    {
        return $this->language;
    }

    public function setLanguage(string $language): self
    {
        $this->language = $language;

        return $this;
    }

    public function getSource(): ?string
    {
        return $this->source;
    }

    public function setSource(string $source): self
    {
        $this->source = $source;

        return $this;
    }

    public function isEnabled(): ?bool
    {
        return $this->enabled;
    }

    public function setEnabled(bool $enabled): self
    {
        $this->enabled = $enabled;

        return $this;
    }

    public function isIsEarlyAdopter(): bool
    {
        return $this->isEarlyAdopter;
    }

    public function setIsEarlyAdopter(bool $isEarlyAdopter): self
    {
        $this->isEarlyAdopter = $isEarlyAdopter;

        return $this;
    }

    public function getEarlyAdoptersList(): ?array
    {
        return $this->earlyAdoptersList;
    }

    public function setEarlyAdoptersList(?array $earlyAdoptersList): self
    {
        $this->earlyAdoptersList = $earlyAdoptersList;

        return $this;
    }

    public function getFeatureSetting(): ?FeatureSetting
    {
        return $this->featureSetting;
    }

    public function setFeatureSetting(?FeatureSetting $featureSetting): self
    {
        $this->featureSetting = $featureSetting;

        return $this;
    }

    /**
     * @return Collection<int, FeatureParameter>
     */
    public function getFeatureParameters(): Collection
    {
        return $this->featureParameters;
    }

    public function addFeatureParameter(FeatureParameter $featureParameter): self
    {
        if (!$this->featureParameters->contains($featureParameter)) {
            $this->featureParameters[] = $featureParameter;
            $featureParameter->setFeature($this);
        }

        return $this;
    }

    public function removeFeatureParameter(FeatureParameter $featureParameter): self
    {
        if ($this->featureParameters->removeElement($featureParameter)) {
            // set the owning side to null (unless already changed)
            if ($featureParameter->getFeature() === $this) {
                $featureParameter->setFeature(null);
            }
        }

        return $this;
    }
}
