<?php

namespace App\Repository;

use App\Entity\Language;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Language>
 *
 * @method Language|null find($id, $lockMode = null, $lockVersion = null)
 * @method Language|null findOneBy(array $criteria, array $orderBy = null)
 * @method Language[]    findAll()
 * @method Language[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class LanguageRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Language::class);
    }

    public function add(Language $entity, bool $flush = false): void
    {
        $this->getEntityManager()->persist($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    public function remove(Language $entity, bool $flush = false): void
    {
        $this->getEntityManager()->remove($entity);

        if ($flush) {
            $this->getEntityManager()->flush();
        }
    }

    /**
     * Get references key languages
     * @return Array
     */
    public function getReferenceLanguages() {
        return $this->createQueryBuilder('language')
            ->where('language.isReference = :reference')
            ->setParameter('reference', true)
            ->orderBy('language.id', 'ASC')
            ->getQuery()
            ->getResult();
    }

    public function getAllLanguageCodes(array $params): array
    {
        $qb = $this->createQueryBuilder('l')
            ->select('l.code');
        if (!empty($params['local_languages'])) {
            $qb
                ->andWhere('l.code IN (:locallanguages)')
                ->setParameter('locallanguages', $params['local_languages']);
        }
        $result = $qb->getQuery()->getScalarResult();
        $codes = array_column($result, 'code');
        return $codes;
    }

    public function getAllReferenceLanguageCodes(array $params): array
    {
        $qb = $this->createQueryBuilder('l')
            ->select('l.code');
        if (!empty($params['reference_languages'])) {
            $qb
                ->andWhere('l.code IN (:languages)')
                ->setParameter('languages', $params['reference_languages']);
        }
        $qb->andWhere('l.isReference = :isReference')
            ->setParameter('isReference', true);
        $result = $qb->getQuery()->getScalarResult();
        $codes = array_column($result, 'code');
        return $codes;
    }

    public function getLanguagesByCountries(array $countries): array
    {
        $qb = $this->createQueryBuilder('l')
            ->innerJoin('l.sites', 's')
            ->innerJoin('s.country', 'c')
            ->innerJoin('s.languages', 'lang')
            ->andWhere('c.name IN (:countries)')
            ->setParameter('countries', $countries);

        return $qb->getQuery()->getResult();
    }
}
