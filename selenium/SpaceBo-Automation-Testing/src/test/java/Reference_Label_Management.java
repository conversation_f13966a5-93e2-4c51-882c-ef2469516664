import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.testng.Assert;
import org.testng.annotations.Test;
import java.util.concurrent.TimeUnit;
import java.util.List;

public class Reference_Label_Management{
    @Test(groups = {"Reference Label Management"})
    public void Label_Creation() throws InterruptedException {
        Methods user = new Methods();
        user.login();
        Locators.Administration().click();
        TimeUnit.SECONDS.sleep(1);
        Locators.ReferenceLabelManagement().click();
        Locators.New_Translation_Key().click();
        String Label_Key="Automation_Test_Title_05";
        Locators.Label_Key().sendKeys(Label_Key);
        Locators.Francias_Label().sendKeys("Automation_Test_02");
        Locators.Anglias_Label().sendKeys("Automation_Test_02");
        Locators.Italien_Label().sendKeys("Automation_Test_02");
        Locators.Espagnol_Label().sendKeys("Automation_Test_02");
        Locators.Allemand_Label().sendKeys("Automation_Test_02");
        user.Scroll_Down();
        TimeUnit.SECONDS.sleep(2);
        Locators.App_Reference_CheckBox().click();
        Locators.Web_Reference_CheckBox().click();
        Locators.Reference_Label_Save().click();
//        Locators.Reference_Label_Cancel().click();
        TimeUnit.SECONDS.sleep(2);
        Locators.Reference_Label_Search().sendKeys(Label_Key);
        TimeUnit.SECONDS.sleep(2);

        WebElement table = Locators.Reference_Label_Table();
        // Get all rows from the table
        List<WebElement> rows = table.findElements(By.tagName("tr"));
        boolean isTextFound = false; // Flag to track if text is found
        // Iterate through each row and cell
        for (WebElement row : rows) {
            List<WebElement> cells = row.findElements(By.tagName("td"));
            for (WebElement cell : cells) {
                // Check if the cell text contains the specified text
                if (cell.getText().contains(Label_Key)) {
                    isTextFound = true; // Text found
                    break;
                }
            }
            if (isTextFound) break; // Exit loop if text is found
        }
        // Assert that the text was found in the table
        Assert.assertTrue(isTextFound,"Text was not found in the table.");
        user.exit();
    }
}
