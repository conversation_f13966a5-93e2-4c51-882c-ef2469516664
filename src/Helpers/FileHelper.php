<?php

declare(strict_types=1);

namespace App\Helpers;

/**
 * Class FileFormat.
 */
class FileHelper
{
    public const FILE_UPLOAD_ERROR = [
        0 => 'file_no_error',
        1 => 'file_exceeds_max_filesize_ini',
        2 => 'file_exceeds_max_filesize_html',
        3 => 'file_partially_uploaded',
        4 => 'file_not_uploaded',
        6 => 'file_missing_temporary_folder',
        7 => 'file_field_to_write_in_disk',
        8 => 'file_php_extension_stopped_upload',
    ];

    public static function getFileUploadError(int $code){
        
        return self::FILE_UPLOAD_ERROR[$code] ?? null;
    }
}
