/**
 * Dynamic Form Sections Handler
 * Handles choice fields with dynamic sections that enable/disable based on selection
 */

$(document).ready(function() {
    console.log('🔄 Initializing Dynamic Form Sections...');
    
    // Initialize dynamic sections for all forms
    initializeDynamicSections();
    
    // Set up event listeners for choice field changes
    setupChoiceFieldListeners();
});

/**
 * Initialize dynamic sections on page load
 */
function initializeDynamicSections() {
    // Find all choice fields with dynamic attributes
    $('select[data-dynamic], input[type="radio"][data-dynamic]').each(function() {
        const $choiceField = $(this);
        const dynamicConfig = getDynamicConfig($choiceField);
        
        if (dynamicConfig) {
            console.log('🎛️ Found dynamic choice field:', $choiceField.attr('id'), dynamicConfig);
            
            // Initialize sections based on current selection
            initializeFieldSections($choiceField, dynamicConfig);
        }
    });
    
    // Also check for dynamic config in row_attr (from Twig templates)
    $('.multi-fields').each(function() {
        const $container = $(this);
        const $choiceField = $container.find('select, input[type="radio"]').first();
        
        if ($choiceField.length > 0) {
            const dynamicConfig = getDynamicConfigFromRowAttr($choiceField);
            
            if (dynamicConfig) {
                console.log('🎛️ Found dynamic choice field from row_attr:', $choiceField.attr('id'), dynamicConfig);
                
                // Store config as data attribute for easier access
                $choiceField.attr('data-dynamic', JSON.stringify(dynamicConfig));
                
                // Initialize sections
                initializeFieldSections($choiceField, dynamicConfig);
            }
        }
    });
}

/**
 * Get dynamic configuration from data attribute
 */
function getDynamicConfig($field) {
    const dynamicAttr = $field.attr('data-dynamic');
    if (dynamicAttr) {
        try {
            return JSON.parse(dynamicAttr);
        } catch (e) {
            console.error('Error parsing dynamic config:', e);
            return null;
        }
    }
    return null;
}

/**
 * Get dynamic configuration from Twig row_attr (for server-rendered forms)
 */
function getDynamicConfigFromRowAttr($field) {
    // Check if the field's container has dynamic configuration from Twig
    const $container = $field.closest('.multi-fields');
    const dynamicConfigAttr = $container.attr('data-dynamic-config');

    if (dynamicConfigAttr) {
        try {
            return JSON.parse(dynamicConfigAttr);
        } catch (e) {
            console.error('Error parsing dynamic config from container:', e);
        }
    }

    // Check if field itself has dynamic data attribute
    const fieldDynamicAttr = $field.attr('data-dynamic');
    if (fieldDynamicAttr) {
        try {
            return JSON.parse(fieldDynamicAttr);
        } catch (e) {
            console.error('Error parsing dynamic config from field:', e);
        }
    }

    return null;
}

/**
 * Initialize sections based on current field value
 */
function initializeFieldSections($choiceField, dynamicConfig) {
    const currentValue = $choiceField.val();
    const fieldContainer = getFieldContainer($choiceField);
    
    console.log('🔧 Initializing sections for field:', $choiceField.attr('id'), 'Current value:', currentValue);
    
    // Create or find section containers for each option
    Object.keys(dynamicConfig).forEach(optionValue => {
        const sectionName = dynamicConfig[optionValue];
        const $section = findOrCreateSection(fieldContainer, optionValue, sectionName);
        
        if (currentValue === optionValue) {
            // Enable the selected section
            enableSection($section, optionValue);
            console.log('✅ Enabled section:', sectionName);
        } else {
            // Disable other sections
            disableSection($section, optionValue);
            console.log('❌ Disabled section:', sectionName);
        }
    });
}

/**
 * Set up event listeners for choice field changes
 */
function setupChoiceFieldListeners() {
    // Handle select dropdowns
    $(document).on('change', 'select[data-dynamic]', function() {
        const $choiceField = $(this);
        const dynamicConfig = getDynamicConfig($choiceField);
        
        if (dynamicConfig) {
            handleChoiceChange($choiceField, dynamicConfig);
        }
    });
    
    // Handle radio buttons
    $(document).on('change', 'input[type="radio"][data-dynamic]', function() {
        const $choiceField = $(this);
        const dynamicConfig = getDynamicConfig($choiceField);
        
        if (dynamicConfig) {
            handleChoiceChange($choiceField, dynamicConfig);
        }
    });
}

/**
 * Handle choice field value change
 */
function handleChoiceChange($choiceField, dynamicConfig) {
    const newValue = $choiceField.val();
    const fieldContainer = getFieldContainer($choiceField);
    
    console.log('🔄 Choice changed:', $choiceField.attr('id'), 'New value:', newValue);
    
    // Update all sections based on new selection
    Object.keys(dynamicConfig).forEach(optionValue => {
        const sectionName = dynamicConfig[optionValue];
        const $section = findOrCreateSection(fieldContainer, optionValue, sectionName);
        
        if (newValue === optionValue) {
            enableSection($section, optionValue);
            console.log('✅ Enabled section:', sectionName);
            
            // Show notification
            if (typeof showNotification === 'function') {
                showNotification(`Enabled ${sectionName} section`, 'info');
            }
        } else {
            disableSection($section, optionValue);
            console.log('❌ Disabled section:', sectionName);
        }
    });
}

/**
 * Get the container element for the field
 */
function getFieldContainer($field) {
    // Try to find the closest form section or multi-fields container
    let $container = $field.closest('.multi-fields');
    
    if ($container.length === 0) {
        $container = $field.closest('.tab-pane');
    }
    
    if ($container.length === 0) {
        $container = $field.closest('form');
    }
    
    return $container;
}

/**
 * Find or create a section for the given option
 */
function findOrCreateSection($container, optionValue, sectionName) {
    // Look for existing section
    let $section = $container.find(`[data-dynamic-section="${optionValue}"]`);

    if ($section.length === 0) {
        // Look for section by name pattern in the same form/tab
        const $form = $container.closest('form');
        $section = $form.find(`[data-section*="${optionValue}"], .${optionValue}-section`);
    }

    if ($section.length === 0) {
        // Look for fields that might belong to this section by name patterns
        const $form = $container.closest('form');
        $section = $form.find(`[id*="${optionValue}"], [name*="${optionValue}"]`).closest('.multi-fields');
    }

    if ($section.length === 0) {
        // Look for sections with similar names (case insensitive)
        const $form = $container.closest('form');
        $form.find('.multi-fields').each(function() {
            const sectionId = $(this).attr('data-section') || '';
            if (sectionId.toLowerCase().includes(optionValue.toLowerCase())) {
                $section = $(this);
                return false; // break the loop
            }
        });
    }

    // If still not found, create a placeholder
    if ($section.length === 0) {
        console.warn('⚠️ Section not found for option:', optionValue, 'Creating placeholder');
        $section = $('<div>')
            .attr('data-dynamic-section', optionValue)
            .addClass('dynamic-section-placeholder')
            .text(`Dynamic Section: ${sectionName}`);
        $container.after($section);
    }

    // Mark the section with our identifier
    $section.attr('data-dynamic-section', optionValue);

    return $section;
}

/**
 * Enable a section and its fields
 */
function enableSection($section, optionValue) {
    $section.removeClass('dynamic-section-disabled').addClass('dynamic-section-enabled');
    $section.show();
    
    // Enable all form fields in the section
    $section.find('input, select, textarea').each(function() {
        $(this).prop('disabled', false);
        $(this).removeClass('field-disabled');
    });
    
    // Add visual feedback
    $section.css({
        'opacity': '1',
        'pointer-events': 'auto'
    });
}

/**
 * Disable a section and its fields
 */
function disableSection($section, optionValue) {
    $section.removeClass('dynamic-section-enabled').addClass('dynamic-section-disabled');
    
    // Disable all form fields in the section
    $section.find('input, select, textarea').each(function() {
        $(this).prop('disabled', true);
        $(this).addClass('field-disabled');
    });
    
    // Add visual feedback
    $section.css({
        'opacity': '0.5',
        'pointer-events': 'none'
    });
}

/**
 * Debug function to show current dynamic sections state
 */
function debugDynamicSections() {
    console.group('🔍 Dynamic Sections Debug');
    
    $('[data-dynamic]').each(function() {
        const $field = $(this);
        const config = getDynamicConfig($field);
        const value = $field.val();
        
        console.log('Field:', $field.attr('id'), 'Value:', value, 'Config:', config);
    });
    
    $('[data-dynamic-section]').each(function() {
        const $section = $(this);
        const optionValue = $section.attr('data-dynamic-section');
        const isEnabled = $section.hasClass('dynamic-section-enabled');
        
        console.log('Section:', optionValue, 'Enabled:', isEnabled);
    });
    
    console.groupEnd();
}

/**
 * Manually trigger dynamic section setup for a specific field
 */
function setupDynamicField(fieldSelector, dynamicConfig) {
    const $field = $(fieldSelector);
    if ($field.length === 0) {
        console.error('Field not found:', fieldSelector);
        return;
    }

    // Add dynamic config to field
    $field.attr('data-dynamic', JSON.stringify(dynamicConfig));

    // Initialize sections
    initializeFieldSections($field, dynamicConfig);

    console.log('✅ Dynamic field setup complete for:', fieldSelector);
}

/**
 * Test function to simulate choice changes
 */
function testDynamicChoice(fieldSelector, newValue) {
    const $field = $(fieldSelector);
    if ($field.length === 0) {
        console.error('Field not found:', fieldSelector);
        return;
    }

    $field.val(newValue).trigger('change');
    console.log('🧪 Triggered change for field:', fieldSelector, 'New value:', newValue);
}

// Make functions globally available for testing
window.debugDynamicSections = debugDynamicSections;
window.setupDynamicField = setupDynamicField;
window.testDynamicChoice = testDynamicChoice;
